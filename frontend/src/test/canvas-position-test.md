# Canvas Position Persistence Test

## 功能说明
完善画布上artifacts位置状态变更保存，刷新页面后，要能够记住移动后的位置，只需要记住位置，不需要记住放大和缩小。

## 实现的功能

### 1. DesignCanvas组件增强
- 添加了 `getViewState()` 方法：获取当前画布的缩放和偏移状态
- 添加了 `setViewState()` 方法：设置画布的缩放和偏移状态
- 添加了 `onPositionChange` 回调：在拖拽结束时触发位置保存

### 2. useCanvasPersistence Hook更新
- 修改了 `loadCanvasState()` 返回类型，现在返回包含设计和视图状态的对象
- 保存时会同时保存设计位置和画布视图状态
- 加载时会恢复设计位置和画布视图状态

### 3. DesignContext集成
- 在添加设计时自动保存当前视图状态
- 在位置变更时自动触发保存
- 在加载时恢复完整的画布状态

### 4. 自动保存机制
- 拖拽结束时自动保存位置
- 支持本地存储和服务器存储
- 离线时优先使用本地存储

## 测试步骤

1. **添加设计图片**
   - 生成一些设计图片到画布
   - 验证图片正常显示

2. **移动图片位置**
   - 拖拽图片到不同位置
   - 验证拖拽功能正常

3. **刷新页面测试**
   - 刷新浏览器页面
   - 验证图片位置是否保持不变
   - 验证画布视图状态是否保持

4. **多图片测试**
   - 添加多个设计图片
   - 移动到不同位置
   - 刷新页面验证所有位置都保持

5. **离线测试**
   - 断开网络连接
   - 移动图片位置
   - 刷新页面验证本地存储功能

## 技术实现细节

### 数据结构
```typescript
interface DesignItem {
  id: string;
  image_url: string;
  prompt?: string;
  timestamp: Date;
  position: { x: number; y: number };  // 保存的位置信息
  scale: number;
  isSelected: boolean;
  metadata?: any;
}

interface ViewState {
  scale: number;      // 画布缩放级别
  offset_x: number;   // 画布X轴偏移
  offset_y: number;   // 画布Y轴偏移
}
```

### 保存时机
- 拖拽结束时（handleGlobalMouseUp）
- 添加新设计时
- 手动调用saveCanvasState时

### 存储位置
- 优先保存到服务器（在线时）
- 同时保存到localStorage（离线支持）
- 加载时优先从服务器获取，失败时使用本地存储

## 注意事项

1. **只保存位置，不保存缩放**
   - 根据用户需求，只记住图片位置
   - 画布缩放状态会保存但主要用于视图恢复
   - 图片的scale属性保持为1（不记住图片的放大缩小）

2. **性能优化**
   - 使用防抖机制避免频繁保存
   - 拖拽过程中不保存，只在拖拽结束时保存
   - 使用本地缓存减少网络请求

3. **错误处理**
   - 网络错误时自动使用本地存储
   - 数据损坏时有默认值兜底
   - 加载失败时不影响正常使用
