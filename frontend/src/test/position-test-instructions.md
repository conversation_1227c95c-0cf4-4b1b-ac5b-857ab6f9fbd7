# 画布位置保存功能测试指南

## 问题诊断

根据用户反馈"移动了画布上artifacts位置，刷新，位置还是之前位置"，我们已经实现了完整的位置保存功能，但需要验证是否正常工作。

## 已实现的功能

### 1. 调试日志系统
我们添加了详细的调试日志来跟踪整个保存和加载过程：

- **拖拽结束时**: `🎯 拖拽结束，触发位置保存`
- **位置变更处理**: `🔄 设计位置已变更，自动保存状态`
- **DesignContext保存**: `💾 DesignContext保存画布状态`
- **useCanvasPersistence保存**: `🚀 开始保存画布状态到useCanvasPersistence`
- **加载过程**: `📥 开始加载画布状态`

### 2. conversationId修复
- 修复了从URL参数获取`project_id`作为`conversationId`的逻辑
- 如果没有`project_id`参数，会生成一个默认的测试ID

### 3. 完整的保存链路
1. 拖拽结束 → `handleGlobalMouseUp`
2. 触发 → `onPositionChange`
3. 调用 → `saveCanvasState`
4. 执行 → `persistCanvasState`
5. 保存到本地存储和服务器

## 测试步骤

### 第一步：检查控制台日志
1. 打开浏览器开发者工具（F12）
2. 切换到Console标签
3. 访问设计页面
4. 查看是否有以下日志：
   - `🔍 从URL获取project_id: xxx`
   - `✅ 设置conversationId为: xxx`

### 第二步：测试位置保存
1. 生成一些设计图片到画布
2. 拖拽图片到不同位置
3. 拖拽结束时查看控制台是否出现：
   - `🎯 拖拽结束，触发位置保存`
   - `🔄 设计位置已变更，自动保存状态`
   - `💾 DesignContext保存画布状态`
   - `🚀 开始保存画布状态到useCanvasPersistence`

### 第三步：测试位置恢复
1. 刷新页面
2. 查看控制台是否出现：
   - `📥 开始加载画布状态`
   - `✅ 从服务器加载成功` 或 `✅ 从本地存储加载成功`
   - `📍 解析后的设计位置: [...]`
3. 验证图片是否恢复到移动后的位置

## 可能的问题和解决方案

### 问题1：没有conversationId
**症状**: 控制台显示 `⚠️ 无conversationId，跳过保存`
**解决**: 确保URL中有`project_id`参数，或者会生成默认ID

### 问题2：拖拽没有触发保存
**症状**: 拖拽后没有看到保存相关日志
**解决**: 检查`onPositionChange`回调是否正确传递

### 问题3：保存成功但加载失败
**症状**: 看到保存日志但刷新后没有恢复
**解决**: 检查加载日志，可能是数据格式问题

### 问题4：本地存储问题
**症状**: 服务器保存失败，本地存储也没有数据
**解决**: 检查浏览器本地存储是否被禁用

## 手动验证本地存储

在浏览器控制台中运行：
```javascript
// 查看本地存储的画布数据
const conversationId = 'your-conversation-id';
const key = `canvas_state_${conversationId}`;
const data = localStorage.getItem(key);
console.log('本地存储数据:', JSON.parse(data || '{}'));
```

## 调试命令

在控制台中可以手动触发保存和加载：
```javascript
// 手动保存（如果有DesignContext）
window.designContext?.saveCanvasState();

// 手动加载
window.designContext?.loadCanvasState();
```

## 预期行为

正常工作时应该看到：
1. 拖拽结束时立即保存
2. 刷新页面时自动加载
3. 图片位置完全恢复到移动后的位置
4. 控制台有完整的保存和加载日志

如果仍然有问题，请提供控制台的完整日志输出。
