# 画布位置保存功能调试指南

## 当前状态

我们已经完成了画布位置保存功能的实现，包括：

1. ✅ 拖拽结束时触发保存
2. ✅ 获取真实的画布视图状态
3. ✅ 保存到本地存储和服务器
4. ✅ 页面刷新时自动加载
5. ✅ 修复了image_url保存问题
6. ✅ 修复了conversationId获取问题

## 调试步骤

### 1. 检查conversationId
打开浏览器控制台，应该看到：
```
🔍 从URL获取project_id: [project_id或null]
🆔 生成默认project_id用于测试: default-project-[timestamp]
✅ 设置conversationId为: [id]
```

### 2. 测试拖拽保存
拖拽画布上的图片，应该看到：
```
🎯 拖拽结束，触发位置保存，当前设计数量: [数量]
📍 当前设计位置: [{id: "xxx", position: {x: 123, y: 456}}]
🔄 设计位置已变更，自动保存状态
📊 当前设计数据: [{id: "xxx", position: {x: 123, y: 456}}]
💾 DesignContext保存画布状态
📋 设计数量: [数量]
🎯 设计位置: [{id: "xxx", position: {x: 123, y: 456}}]
👁️ 视图状态: {scale: 1, offset_x: 0, offset_y: 0}
🚀 开始保存画布状态到useCanvasPersistence
📝 conversationId: [id]
🎨 设计数量: [数量]
📍 设计位置详情: [{id: "xxx", position: {x: 123, y: 456}}]
👁️ 视图状态: {scale: 1, offset_x: 0, offset_y: 0}
🔄 转换后的artifacts: [{id: "xxx", position: {x: 123, y: 456}}]
💾 准备保存的完整数据: {artifacts: [...], view_state: {...}}
✅ 本地存储保存完成
🌐 在线状态，保存到服务器
✅ 服务器保存完成
🎉 画布状态保存成功
```

### 3. 测试刷新加载
刷新页面，应该看到：
```
📥 开始加载画布状态
📝 conversationId: [id]
🌐 在线状态，从服务器加载
✅ 从服务器加载成功
🔄 解析画布数据: {artifacts: [...], view_state: {...}}
📍 解析后的设计位置: [{id: "xxx", position: {x: 123, y: 456}}]
👁️ 解析后的视图状态: {scale: 1, offset_x: 0, offset_y: 0}
Canvas is ready, restoring designs and view state
Canvas state restored successfully
```

## 手动测试命令

在浏览器控制台中运行以下命令进行手动测试：

### 检查本地存储
```javascript
// 获取当前conversationId（从控制台日志中找到）
const conversationId = 'your-conversation-id-here';

// 检查本地存储
const key = `canvas_state_${conversationId}`;
const data = localStorage.getItem(key);
console.log('本地存储数据:', JSON.parse(data || 'null'));
```

### 手动保存测试
```javascript
// 如果有全局的设计上下文，可以手动触发保存
if (window.designContext) {
  window.designContext.saveCanvasState();
}
```

### 检查画布状态
```javascript
// 检查当前画布上的设计
if (window.canvasRef && window.canvasRef.current) {
  const designs = window.canvasRef.current.getDesigns();
  console.log('当前画布设计:', designs);
  
  const viewState = window.canvasRef.current.getViewState();
  console.log('当前视图状态:', viewState);
}
```

## 常见问题排查

### 问题1: 没有保存日志
**可能原因**: 
- conversationId为空
- onPositionChange回调没有正确传递
- 拖拽事件没有正确触发

**检查方法**: 
- 查看conversationId设置日志
- 确认拖拽结束时有相关日志

### 问题2: 保存成功但加载失败
**可能原因**:
- 数据格式不匹配
- image_url丢失
- 服务器API问题

**检查方法**:
- 查看本地存储数据格式
- 检查加载时的错误日志

### 问题3: 位置没有恢复
**可能原因**:
- 位置数据保存错误
- 视图状态恢复问题
- 画布渲染时机问题

**检查方法**:
- 对比保存和加载的位置数据
- 确认setViewState被调用

## 预期结果

正常工作时：
1. 拖拽图片到新位置
2. 看到完整的保存日志链
3. 刷新页面
4. 看到完整的加载日志链
5. 图片出现在移动后的位置

如果仍有问题，请提供：
1. 完整的控制台日志
2. 本地存储数据内容
3. 具体的错误信息
