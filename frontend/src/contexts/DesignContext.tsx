'use client';

import React, { createContext, useContext, useCallback, useRef, useEffect } from 'react';
import { DesignItem, DesignCanvasRef } from '@/components/DesignCanvas';
import { useCanvasPersistence } from '@/hooks/useCanvasPersistence';

interface DesignContextType {
  // Canvas引用
  canvasRef: React.RefObject<DesignCanvasRef | null>;
  
  // 添加设计到canvas
  addDesignToCanvas: (design: {
    id: string;
    image_url: string;
    prompt?: string;
    timestamp?: Date;
    metadata?: any;
  }) => void;
  
  // 清空所有设计
  clearAllDesigns: () => void;
  
  // 获取所有设计
  getAllDesigns: () => DesignItem[];
  
  // 选中设计
  selectDesign: (designId: string | null) => void;
  
  // 持久化状态
  isSaving: boolean;
  lastSaved: Date | null;
  saveError: string | null;
  isOnline: boolean;
  
  // 手动保存和加载
  saveCanvasState: (providedDesigns?: DesignItem[], providedViewState?: { scale: number; offset_x: number; offset_y: number }) => Promise<void>;
  loadCanvasState: () => Promise<{ designs: DesignItem[]; viewState: { scale: number; offset_x: number; offset_y: number } } | null>;
  clearCanvasState: () => Promise<void>;
}

const DesignContext = createContext<DesignContextType | undefined>(undefined);

export function DesignProvider({ children, conversationId }: { children: React.ReactNode; conversationId?: string }) {
  console.log('🏗️ DesignProvider接收到conversationId:', conversationId);
  const canvasRef = useRef<DesignCanvasRef>(null);
  const pendingDesigns = useRef<Array<{
    id: string;
    image_url: string;
    prompt?: string;
    timestamp?: Date;
    metadata?: any;
  }>>([]);
  const canvasReadyCheckInterval = useRef<NodeJS.Timeout | null>(null);
  const isInitialized = useRef(false);
  const isLoading = useRef(false); // 防止重复加载

  // 持久化状态
  const {
    isSaving,
    lastSaved,
    saveError,
    saveCanvasState: persistCanvasState,
    loadCanvasState: loadPersistedState,
    clearCanvasState: clearPersistedState,
    isOnline
  } = useCanvasPersistence({
    conversationId,
    autoSave: true,
    autoSaveDelay: 1000,
    enableOffline: true
  });

  const addDesignToCanvas = useCallback((design: {
    id: string;
    image_url: string;
    prompt?: string;
    timestamp?: Date;
    metadata?: any;
  }) => {
    if (canvasRef.current) {
      // 检查是否已存在相同ID的设计
      const existingDesigns = canvasRef.current.getDesigns();
      const existingDesign = existingDesigns.find(d => d.id === design.id);

      if (existingDesign) {
        // 如果设计已存在，不重复添加
        return;
      }

      canvasRef.current.addDesign(design);

      // 暂时禁用自动保存，避免循环
      // setTimeout(() => {
      //   const designs = canvasRef.current?.getDesigns() || [];
      //   const viewState = canvasRef.current?.getViewState() || { scale: 1, offset_x: 0, offset_y: 0 };
      //   persistCanvasState(designs, viewState);
      // }, 100);
    } else {
      console.log('Canvas not ready yet, adding to pending designs');
      // Add to pending designs and try again after a short delay
      pendingDesigns.current.push(design);
      
      // Start checking if canvas is ready if not already checking
      if (!canvasReadyCheckInterval.current) {
        canvasReadyCheckInterval.current = setInterval(() => {
          if (canvasRef.current) {
            console.log('Canvas ready now, adding pending designs:', pendingDesigns.current.length);
            pendingDesigns.current.forEach(pendingDesign => {
              canvasRef.current?.addDesign(pendingDesign);
            });
            pendingDesigns.current = [];
            if (canvasReadyCheckInterval.current) {
              clearInterval(canvasReadyCheckInterval.current);
              canvasReadyCheckInterval.current = null;
            }
            
            // 暂时禁用自动保存，避免循环
            // setTimeout(() => {
            //   const designs = canvasRef.current?.getDesigns() || [];
            //   persistCanvasState(designs, { scale: 1, offset_x: 0, offset_y: 0 });
            // }, 100);
          }
        }, 50);
      }
    }
  }, [persistCanvasState]);

  const clearAllDesigns = useCallback(() => {
    if (canvasRef.current) {
      canvasRef.current.clearDesigns();
      // 清除后自动保存
      setTimeout(() => {
        persistCanvasState([], { scale: 1, offset_x: 0, offset_y: 0 });
      }, 100);
    }
  }, [persistCanvasState]);

  const getAllDesigns = useCallback((): DesignItem[] => {
    if (canvasRef.current) {
      return canvasRef.current.getDesigns();
    }
    return [];
  }, []);

  const selectDesign = useCallback((designId: string | null) => {
    if (canvasRef.current) {
      canvasRef.current.selectDesign(designId);
      // 选择变更后自动保存
      setTimeout(() => {
        const designs = canvasRef.current?.getDesigns() || [];
        persistCanvasState(designs, { scale: 1, offset_x: 0, offset_y: 0 });
      }, 100);
    }
  }, [persistCanvasState]);

  // conversationId变化时重置初始化状态
  useEffect(() => {
    console.log('🔄 conversationId变化，重置初始化状态:', conversationId);
    isInitialized.current = false;
    isLoading.current = false; // 重置加载状态
  }, [conversationId]);

  // 页面加载时恢复画布状态
  useEffect(() => {
    const restoreCanvasState = async () => {
      if (!conversationId) {
        console.log('🔄 无conversationId，跳过加载');
        return;
      }

      if (isInitialized.current) {
        console.log('🔄 已初始化，跳过重复加载');
        return;
      }

      if (isLoading.current) {
        console.log('🔄 正在加载中，跳过重复请求');
        return;
      }

      try {
        isLoading.current = true; // 设置加载状态
        console.log('🔄 Loading canvas state for conversation:', conversationId);
        console.log('🔄 当前画布设计数量:', canvasRef.current?.getDesigns()?.length || 0);

        const savedState = await loadPersistedState();

        if (savedState && savedState.designs.length > 0) {
          console.log('🔄 Restoring', savedState.designs.length, 'designs to canvas');

          // 等待canvas准备就绪
          const waitForCanvas = () => {
            if (canvasRef.current) {
              // 清空现有设计
              console.log('🧹 清空现有设计，当前数量:', canvasRef.current.getDesigns().length);
              canvasRef.current.clearDesigns();
              console.log('🧹 清空后设计数量:', canvasRef.current.getDesigns().length);

              // 恢复视图状态
              console.log('🔄 恢复视图状态:', savedState.viewState);
              canvasRef.current.setViewState(savedState.viewState);

              // 添加保存的设计
              console.log('🔄 开始恢复设计，数量:', savedState.designs.length);
              savedState.designs.forEach((design, index) => {
                console.log(`🔄 恢复设计 ${index + 1}:`, {
                  id: design.id,
                  position: design.position,
                  image_url: design.image_url
                });
                canvasRef.current?.addDesign({
                  id: design.id,
                  image_url: design.image_url,
                  prompt: design.prompt,
                  timestamp: design.timestamp,
                  metadata: design.metadata,
                  position: design.position, // 传递保存的位置
                  scale: design.scale, // 传递保存的缩放
                  isSelected: design.isSelected // 传递保存的选中状态
                });
              });

              isInitialized.current = true;
              console.log('✅ Canvas state restored successfully');
            } else {
              // 继续等待
              setTimeout(waitForCanvas, 100);
            }
          };

          waitForCanvas();
        } else {
          console.log('📭 No saved canvas state found');
          isInitialized.current = true;
        }
      } catch (error) {
        console.error('❌ Failed to restore canvas state:', error);
        isInitialized.current = true;
      } finally {
        isLoading.current = false; // 清除加载状态
      }
    };

    restoreCanvasState();
  }, [conversationId]); // 移除loadPersistedState依赖，避免不必要的重新加载

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (canvasReadyCheckInterval.current) {
        clearInterval(canvasReadyCheckInterval.current);
        canvasReadyCheckInterval.current = null;
      }
    };
  }, []);



  // 手动保存和加载方法
  const saveCanvasState = useCallback(async (providedDesigns?: DesignItem[], providedViewState?: { scale: number; offset_x: number; offset_y: number }) => {
    const designs = providedDesigns || canvasRef.current?.getDesigns() || [];
    const viewState = providedViewState || canvasRef.current?.getViewState() || { scale: 1, offset_x: 0, offset_y: 0 };
    console.log('💾 DesignContext保存画布状态');
    console.log('📋 设计数量:', designs.length);
    console.log('🎯 设计位置:', designs.map(d => ({ id: d.id, position: d.position })));
    console.log('👁️ 视图状态:', viewState);
    await persistCanvasState(designs, viewState);
  }, [persistCanvasState]);

  // 手动加载画布状态（直接使用useCanvasPersistence提供的方法）
  const loadCanvasState = loadPersistedState;

  const clearCanvasState = useCallback(async () => {
    if (canvasRef.current) {
      canvasRef.current.clearDesigns();
    }
    await clearPersistedState();
  }, [clearPersistedState]);

  const value: DesignContextType = {
    canvasRef,
    addDesignToCanvas,
    clearAllDesigns,
    getAllDesigns,
    selectDesign,
    // 持久化状态
    isSaving,
    lastSaved,
    saveError,
    isOnline,
    // 手动保存和加载
    saveCanvasState,
    loadCanvasState,
    clearCanvasState
  };

  return (
    <DesignContext.Provider value={value}>
      {children}
    </DesignContext.Provider>
  );
}

export function useDesignContext() {
  const context = useContext(DesignContext);
  if (context === undefined) {
    throw new Error('useDesignContext must be used within a DesignProvider');
  }
  return context;
}

// 自定义hook：处理对话中的设计生成
export function useDesignGeneration() {
  const { addDesignToCanvas } = useDesignContext();

  const handleDesignGenerated = useCallback((workflowData: {
    image_url?: string;
    design_prompt?: string;
    design_id?: string;
    task_id?: string;
    [key: string]: any;
  }) => {
    console.log('handleDesignGenerated called with:', workflowData);

    if (workflowData.image_url) {
      // 使用稳定的ID生成策略，优先使用后端提供的ID
      let designId = workflowData.design_id || workflowData.task_id;

      if (!designId) {
        // 如果没有后端ID，基于image_url生成稳定的ID
        const urlHash = workflowData.image_url.split('/').pop()?.split('?')[0] || 'unknown';
        designId = `design_${urlHash}`;
      }

      const designData = {
        id: designId,
        image_url: workflowData.image_url,
        prompt: workflowData.design_prompt,
        timestamp: new Date(),
        metadata: {
          workflow_data: workflowData,
          generation_source: 'conversation'
        }
      };

      console.log('Adding design to canvas with stable ID:', designData);
      addDesignToCanvas(designData);

      return designId;
    } else {
      console.log('No image_url in workflowData, skipping canvas add');
    }
    return null;
  }, [addDesignToCanvas]);

  return {
    handleDesignGenerated
  };
}

// 自定义hook：处理设计历史
export function useDesignHistory() {
  const { getAllDesigns, selectDesign } = useDesignContext();

  const getDesignHistory = useCallback(() => {
    const designs = getAllDesigns();
    return designs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }, [getAllDesigns]);

  const getLatestDesign = useCallback(() => {
    const history = getDesignHistory();
    return history.length > 0 ? history[0] : null;
  }, [getDesignHistory]);

  const selectLatestDesign = useCallback(() => {
    const latest = getLatestDesign();
    if (latest) {
      selectDesign(latest.id);
    }
  }, [getLatestDesign, selectDesign]);

  return {
    getDesignHistory,
    getLatestDesign,
    selectLatestDesign
  };
}

// 自定义hook：处理设计操作
export function useDesignOperations() {
  const { getAllDesigns } = useDesignContext();

  const downloadDesign = useCallback((designId: string) => {
    const designs = getAllDesigns();
    const design = designs.find(d => d.id === designId);
    
    if (design) {
      // 创建下载链接
      const link = document.createElement('a');
      link.href = design.image_url;
      link.download = `design_${design.id}_${design.timestamp.toISOString().split('T')[0]}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [getAllDesigns]);

  const shareDesign = useCallback((designId: string) => {
    const designs = getAllDesigns();
    const design = designs.find(d => d.id === designId);
    
    if (design && navigator.share) {
      navigator.share({
        title: '我的设计作品',
        text: design.prompt || '查看我的设计作品',
        url: design.image_url
      }).catch(console.error);
    } else if (design) {
      // 回退到复制链接
      navigator.clipboard.writeText(design.image_url).then(() => {
        // 可以显示一个提示消息
        console.log('设计链接已复制到剪贴板');
      }).catch(console.error);
    }
  }, [getAllDesigns]);

  const exportDesigns = useCallback(() => {
    const designs = getAllDesigns();
    const exportData = {
      timestamp: new Date().toISOString(),
      designs: designs.map(design => ({
        id: design.id,
        image_url: design.image_url,
        prompt: design.prompt,
        timestamp: design.timestamp.toISOString(),
        metadata: design.metadata
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `designs_export_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  }, [getAllDesigns]);

  return {
    downloadDesign,
    shareDesign,
    exportDesigns
  };
}

// 自定义hook：处理设计统计
export function useDesignStats() {
  const { getAllDesigns } = useDesignContext();

  const getStats = useCallback(() => {
    const designs = getAllDesigns();
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    return {
      total: designs.length,
      today: designs.filter(d => d.timestamp >= today).length,
      thisWeek: designs.filter(d => d.timestamp >= thisWeek).length,
      hasDesigns: designs.length > 0,
      latestTimestamp: designs.length > 0 
        ? Math.max(...designs.map(d => d.timestamp.getTime()))
        : null
    };
  }, [getAllDesigns]);

  return {
    getStats
  };
}
