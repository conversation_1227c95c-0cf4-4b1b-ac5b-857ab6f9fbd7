'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface PerformanceMetrics {
  fps: number;
  renderTime: number;
  memoryUsage: number;
  lastUpdate: Date;
}

export function usePerformanceMonitor(enabled: boolean = true) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    renderTime: 0,
    memoryUsage: 0,
    lastUpdate: new Date()
  });
  
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const renderStartRef = useRef(0);
  const animationFrameRef = useRef<number>();

  useEffect(() => {
    if (!enabled) return;

    const measureFPS = () => {
      const now = performance.now();
      const delta = now - lastTimeRef.current;
      
      if (delta >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / delta);
        
        setMetrics(prev => ({
          ...prev,
          fps,
          memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0,
          lastUpdate: new Date()
        }));
        
        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }
      
      frameCountRef.current++;
      animationFrameRef.current = requestAnimationFrame(measureFPS);
    };

    animationFrameRef.current = requestAnimationFrame(measureFPS);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enabled]);

  const startRenderMeasure = () => {
    if (enabled) {
      renderStartRef.current = performance.now();
    }
  };

  const endRenderMeasure = () => {
    if (enabled && renderStartRef.current > 0) {
      const renderTime = performance.now() - renderStartRef.current;
      setMetrics(prev => ({
        ...prev,
        renderTime
      }));
      renderStartRef.current = 0;
    }
  };

  return {
    metrics,
    startRenderMeasure,
    endRenderMeasure
  };
}

// Canvas性能优化hook
export function useCanvasOptimization() {
  const [isOptimized, setIsOptimized] = useState(false);
  const optimizationRef = useRef({
    throttleTimeout: null as NodeJS.Timeout | null,
    lastUpdateTime: 0,
    updateQueue: [] as any[],
    isProcessing: false
  });

  const throttledUpdate = useCallback((callback: () => void, delay: number = 16) => {
    if (optimizationRef.current.throttleTimeout) {
      clearTimeout(optimizationRef.current.throttleTimeout);
    }

    optimizationRef.current.throttleTimeout = setTimeout(() => {
      callback();
      optimizationRef.current.throttleTimeout = null;
    }, delay);
  }, []);

  const batchedUpdate = useCallback((updates: any[]) => {
    optimizationRef.current.updateQueue.push(...updates);
    
    if (!optimizationRef.current.isProcessing) {
      optimizationRef.current.isProcessing = true;
      
      requestAnimationFrame(() => {
        const batchedUpdates = optimizationRef.current.updateQueue.splice(0);
        optimizationRef.current.updateQueue = [];
        optimizationRef.current.isProcessing = false;
        
        return batchedUpdates;
      });
    }
  }, []);

  const shouldOptimize = useCallback(() => {
    const now = performance.now();
    const timeSinceLastUpdate = now - optimizationRef.current.lastUpdateTime;
    
    // 如果距离上次更新时间很短，可能需要优化
    return timeSinceLastUpdate < 16; // 60fps
  }, []);

  const markUpdate = useCallback(() => {
    optimizationRef.current.lastUpdateTime = performance.now();
  }, []);

  const enableOptimization = useCallback(() => {
    setIsOptimized(true);
  }, []);

  const disableOptimization = useCallback(() => {
    setIsOptimized(false);
  }, []);

  return {
    isOptimized,
    throttledUpdate,
    batchedUpdate,
    shouldOptimize,
    markUpdate,
    enableOptimization,
    disableOptimization
  };
}

// 内存管理hook
export function useMemoryManager() {
  const [memoryStatus, setMemoryStatus] = useState({
    used: 0,
    total: 0,
    limit: 0,
    isCritical: false
  });

  useEffect(() => {
    if (!performance.memory) return;

    const checkMemory = () => {
      const { usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit } = performance.memory;
      
      setMemoryStatus({
        used: usedJSHeapSize,
        total: totalJSHeapSize,
        limit: jsHeapSizeLimit,
        isCritical: usedJSHeapSize > jsHeapSizeLimit * 0.9
      });
    };

    const interval = setInterval(checkMemory, 5000);
    checkMemory();

    return () => clearInterval(interval);
  }, []);

  const cleanup = useCallback(() => {
    if (typeof gc !== 'undefined') {
      gc();
    }
    
    // 清理事件监听器
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new Event('memorycleanup'));
    }
  }, []);

  return {
    memoryStatus,
    cleanup,
    formatBytes: (bytes: number) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  };
}