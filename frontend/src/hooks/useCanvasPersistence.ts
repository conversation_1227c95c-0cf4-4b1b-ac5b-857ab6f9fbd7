'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { DesignItem } from '@/components/DesignCanvas';
import { apiClient, CanvasStateData, CanvasArtifactPosition } from '@/lib/api';
import { useCanvasOptimization } from './usePerformanceOptimization';

interface UseCanvasPersistenceOptions {
  conversationId?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
  enableOffline?: boolean;
}

interface UseCanvasPersistenceReturn {
  isSaving: boolean;
  lastSaved: Date | null;
  saveError: string | null;
  saveCanvasState: (designs: DesignItem[], viewState: { scale: number; offset_x: number; offset_y: number }) => Promise<void>;
  loadCanvasState: () => Promise<{ designs: DesignItem[]; viewState: { scale: number; offset_x: number; offset_y: number } } | null>;
  clearCanvasState: () => Promise<void>;
  isOnline: boolean;
}

export function useCanvasPersistence(
  options: UseCanvasPersistenceOptions = {}
): UseCanvasPersistenceReturn {
  const {
    conversationId,
    autoSave = true,
    autoSaveDelay = 1000,
    enableOffline = true
  } = options;

  console.log('🔧 useCanvasPersistence初始化，conversationId:', conversationId);

  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingSaveRef = useRef<{
    designs: DesignItem[];
    viewState: { scale: number; offset_x: number; offset_y: number };
  } | null>(null);
  
  // 性能优化
  const { throttledUpdate, shouldOptimize, markUpdate } = useCanvasOptimization();

  // 监听conversationId变化
  useEffect(() => {
    console.log('🔄 useCanvasPersistence conversationId变化:', conversationId);
  }, [conversationId]);

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 网络恢复时尝试保存待处理的数据
  useEffect(() => {
    if (isOnline && pendingSaveRef.current && enableOffline) {
      const { designs, viewState } = pendingSaveRef.current;
      saveCanvasState(designs, viewState);
      pendingSaveRef.current = null;
    }
  }, [isOnline, enableOffline]);

  // 转换DesignItem为CanvasArtifactPosition
  const convertDesignsToArtifacts = useCallback((designs: DesignItem[]): CanvasArtifactPosition[] => {
    return designs.map((design, index) => ({
      artifact_id: design.id,
      position_x: design.position.x, // 保持浮点精度，不四舍五入
      position_y: design.position.y, // 保持浮点精度，不四舍五入
      scale: design.scale,
      rotation: 0, // DesignItem没有rotation字段，默认为0
      z_index: index, // 使用数组索引作为z-index
      is_selected: design.isSelected,
      is_visible: true, // 默认可见
      metadata: {
        image_url: design.image_url, // 保存image_url到metadata
        prompt: design.prompt,
        timestamp: design.timestamp.toISOString(),
        ...design.metadata
      }
    }));
  }, []);

  // 转换CanvasArtifactPosition为DesignItem
  const convertArtifactsToDesigns = useCallback((artifacts: CanvasArtifactPosition[]): DesignItem[] => {
    return artifacts.map(artifact => ({
      id: artifact.artifact_id,
      image_url: artifact.metadata?.image_url || '',
      prompt: artifact.metadata?.prompt,
      timestamp: new Date(artifact.metadata?.timestamp || Date.now()),
      position: {
        x: artifact.position_x,
        y: artifact.position_y
      },
      scale: artifact.scale,
      isSelected: artifact.is_selected,
      metadata: artifact.metadata
    }));
  }, []);

  // 保存到本地存储（离线支持）
  const saveToLocalStorage = useCallback((canvasData: CanvasStateData) => {
    if (!enableOffline || !conversationId) return;

    const key = `canvas_state_${conversationId}`;
    const saveData = {
      data: canvasData,
      timestamp: Date.now(),
      version: '1.0'
    };

    try {
      localStorage.setItem(key, JSON.stringify(saveData));
    } catch (error) {
      console.warn('Failed to save canvas state to localStorage:', error);
    }
  }, [conversationId, enableOffline]);

  // 从本地存储加载
  const loadFromLocalStorage = useCallback((): CanvasStateData | null => {
    if (!enableOffline || !conversationId) return null;

    const key = `canvas_state_${conversationId}`;
    try {
      const saved = localStorage.getItem(key);
      if (saved) {
        const { data, timestamp, version } = JSON.parse(saved);
        
        // 如果数据较新（5分钟内），优先使用本地数据
        if (Date.now() - timestamp < 5 * 60 * 1000) {
          return data;
        }
      }
    } catch (error) {
      console.warn('Failed to load canvas state from localStorage:', error);
    }
    return null;
  }, [conversationId, enableOffline]);

  // 保存画布状态到服务器
  const saveCanvasStateToServer = useCallback(async (canvasData: CanvasStateData) => {
    if (!conversationId) return;

    try {
      console.log('🌐 发送到服务器的数据:', JSON.stringify(canvasData, null, 2));
      console.log('🔑 当前token:', apiClient.getToken());
      const result = await apiClient.saveCanvasState(conversationId, canvasData);
      console.log('🌐 服务器响应:', result);
      setLastSaved(new Date());
      setSaveError(null);

      return result;
    } catch (error) {
      console.error('❌ 保存到服务器失败:', error);
      if (error.response) {
        console.error('❌ 响应状态:', error.response.status);
        console.error('❌ 响应数据:', error.response.data);
        console.error('❌ 响应头:', error.response.headers);
      }
      setSaveError(error instanceof Error ? error.message : 'Save failed');
      throw error;
    }
  }, [conversationId]);

  // 防重复加载的全局状态
  const loadingRequests = useRef<Set<string>>(new Set());

  // 从服务器加载画布状态
  const loadCanvasStateFromServer = useCallback(async (): Promise<CanvasStateData | null> => {
    if (!conversationId) return null;

    // 防重复加载检查
    const requestKey = `load-${conversationId}`;
    if (loadingRequests.current.has(requestKey)) {
      console.log('🔄 Already loading state for conversation:', conversationId, '- skipping duplicate request');
      return null;
    }

    try {
      loadingRequests.current.add(requestKey);
      console.log('📥 Loading canvas state from server for conversation:', conversationId);

      const result = await apiClient.getCanvasState(conversationId);

      // 检查是否是空的画布状态
      if (result.canvas_data.artifacts.length === 0) {
        console.log('📭 服务器返回空画布状态');
        return null;
      }

      return result.canvas_data;
    } catch (error) {
      console.error('Failed to load canvas state from server:', error);
      throw error;
    } finally {
      // 请求完成后移除标记
      loadingRequests.current.delete(requestKey);
    }
  }, [conversationId]);

  // 防抖保存
  const debouncedSave = useCallback((designs: DesignItem[], viewState: { scale: number; offset_x: number; offset_y: number }) => {
    markUpdate(); // 标记更新时间
    
    // 如果需要优化，使用更长的延迟
    const delay = shouldOptimize() ? Math.max(autoSaveDelay, 2000) : autoSaveDelay;
    
    throttledUpdate(() => {
      saveCanvasState(designs, viewState);
    }, delay);
  }, [autoSaveDelay, throttledUpdate, shouldOptimize, markUpdate]);

  // 保存画布状态
  const saveCanvasState = useCallback(async (
    designs: DesignItem[],
    viewState: { scale: number; offset_x: number; offset_y: number }
  ) => {
    // 使用最新的conversationId值
    const currentConversationId = options.conversationId || conversationId;
    console.log('🔍 保存时检查conversationId (options):', options.conversationId);
    console.log('🔍 保存时检查conversationId (local):', conversationId);
    console.log('🔍 保存时使用conversationId:', currentConversationId);

    if (!currentConversationId) {
      console.warn('⚠️ 无conversationId，跳过保存');
      console.warn('⚠️ options:', options);
      return;
    }

    console.log('🚀 开始保存画布状态到useCanvasPersistence');
    console.log('📝 conversationId:', currentConversationId);
    console.log('🎨 设计数量:', designs.length);
    console.log('📍 设计位置详情:', designs.map(d => ({ id: d.id, position: d.position })));
    console.log('👁️ 视图状态:', viewState);

    setIsSaving(true);
    setSaveError(null);

    try {
      const artifacts = convertDesignsToArtifacts(designs);
      console.log('🔄 转换后的artifacts:', artifacts.map(a => ({ id: a.artifact_id, position: { x: a.position_x, y: a.position_y } })));

      const canvasData: CanvasStateData = {
        artifacts,
        view_state: {
          scale: viewState.scale,
          offset_x: viewState.offset_x,
          offset_y: viewState.offset_y
        },
        canvas_metadata: {
          version: '1.0',
          total_artifacts: artifacts.length,
          saved_at: new Date().toISOString()
        }
      };

      console.log('💾 准备保存的完整数据:', canvasData);

      // 先保存到本地存储
      saveToLocalStorage(canvasData);
      console.log('✅ 本地存储保存完成');

      // 如果在线，保存到服务器
      if (isOnline) {
        console.log('🌐 在线状态，保存到服务器');
        await saveCanvasStateToServer(canvasData);
        console.log('✅ 服务器保存完成');
      } else {
        console.log('📴 离线状态，保存到待处理队列');
        // 离线时，保存待处理数据
        pendingSaveRef.current = { designs, viewState };
      }

      setLastSaved(new Date());
      console.log('🎉 画布状态保存成功');
    } catch (error) {
      console.error('❌ 保存画布状态失败:', error);
      setSaveError(error instanceof Error ? error.message : 'Save failed');
    } finally {
      setIsSaving(false);
    }
  }, [conversationId, isOnline, convertDesignsToArtifacts, saveToLocalStorage, saveCanvasStateToServer]);

  // 加载画布状态
  const loadCanvasState = useCallback(async (): Promise<{ designs: DesignItem[]; viewState: { scale: number; offset_x: number; offset_y: number } } | null> => {
    if (!conversationId) {
      console.warn('⚠️ 无conversationId，跳过加载');
      return null;
    }

    console.log('📥 开始加载画布状态');
    console.log('📝 conversationId:', conversationId);

    try {
      let canvasData: CanvasStateData | null = null;

      // 优先尝试从服务器加载
      if (isOnline) {
        console.log('🌐 在线状态，从服务器加载');
        canvasData = await loadCanvasStateFromServer();
        if (canvasData) {
          console.log('✅ 从服务器加载成功');
        } else {
          console.log('📭 服务器无数据');
        }
      }

      // 如果服务器没有数据或离线，尝试从本地存储加载
      if (!canvasData) {
        console.log('💾 尝试从本地存储加载');
        canvasData = loadFromLocalStorage();
        if (canvasData) {
          console.log('✅ 从本地存储加载成功');
        } else {
          console.log('📭 本地存储无数据');
        }
      }

      if (canvasData) {
        console.log('🔄 解析画布数据:', canvasData);
        const designs = convertArtifactsToDesigns(canvasData.artifacts);
        const viewState = canvasData.view_state || { scale: 1, offset_x: 0, offset_y: 0 };
        console.log('📍 解析后的设计位置:', designs.map(d => ({ id: d.id, position: d.position })));
        console.log('👁️ 解析后的视图状态:', viewState);
        return { designs, viewState };
      }

      console.log('📭 没有找到任何保存的状态');
      return null;
    } catch (error) {
      console.error('❌ 加载画布状态失败:', error);
      return null;
    }
  }, [conversationId, isOnline, loadCanvasStateFromServer, loadFromLocalStorage, convertArtifactsToDesigns]);

  // 清除画布状态
  const clearCanvasState = useCallback(async () => {
    if (!conversationId) return;

    try {
      // 清除本地存储
      if (enableOffline) {
        const key = `canvas_state_${conversationId}`;
        localStorage.removeItem(key);
      }

      // 清除服务器数据
      if (isOnline) {
        await apiClient.clearCanvasState(conversationId);
      }

      setLastSaved(null);
      setSaveError(null);
    } catch (error) {
      console.error('Failed to clear canvas state:', error);
      setSaveError(error instanceof Error ? error.message : 'Clear failed');
    }
  }, [conversationId, isOnline, enableOffline]);

  // 自动保存的包装器
  const autoSaveCanvasState = useCallback(async (
    designs: DesignItem[],
    viewState: { scale: number; offset_x: number; offset_y: number }
  ) => {
    if (autoSave) {
      debouncedSave(designs, viewState);
    }
  }, [autoSave, debouncedSave]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  return {
    isSaving,
    lastSaved,
    saveError,
    saveCanvasState: autoSave ? autoSaveCanvasState : saveCanvasState,
    loadCanvasState,
    clearCanvasState,
    isOnline
  };
}