import { useState, useCallback, useRef } from 'react';
import { DesignItem } from '@/components/DesignCanvas';

export interface UseDesignCanvasOptions {
  maxDesigns?: number;
  autoArrange?: boolean;
  gridSnap?: boolean;
  gridSize?: number;
}

export function useDesignCanvas(options: UseDesignCanvasOptions = {}) {
  const {
    maxDesigns = 20,
    autoArrange = true,
    gridSnap = false,
    gridSize = 20
  } = options;

  const [designs, setDesigns] = useState<DesignItem[]>([]);
  const [selectedDesignId, setSelectedDesignId] = useState<string | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  // 添加新设计
  const addDesign = useCallback((newDesign: {
    id: string;
    image_url: string;
    prompt?: string;
    timestamp?: Date;
    metadata?: any;
  }) => {
    console.log('useDesignCanvas addDesign called with:', newDesign);

    const canvasRect = canvasRef.current?.getBoundingClientRect();
    const canvasWidth = canvasRect?.width || 800;
    const canvasHeight = canvasRect?.height || 600;

    console.log('Canvas dimensions:', { canvasWidth, canvasHeight });

    // 计算新设计的位置
    let position = { x: canvasWidth / 2, y: canvasHeight / 2 };

    if (autoArrange && designs.length > 0) {
      // 自动排列：避免重叠
      const existingPositions = designs.map(d => d.position);
      position = findAvailablePosition(existingPositions, canvasWidth, canvasHeight);
    }

    // 网格对齐
    if (gridSnap) {
      position.x = Math.round(position.x / gridSize) * gridSize;
      position.y = Math.round(position.y / gridSize) * gridSize;
    }

    const designItem: DesignItem = {
      id: newDesign.id,
      image_url: newDesign.image_url,
      prompt: newDesign.prompt,
      timestamp: newDesign.timestamp || new Date(),
      position,
      scale: 1,
      isSelected: false,
      metadata: newDesign.metadata
    };

    console.log('Created designItem:', designItem);

    setDesigns(prev => {
      console.log('Previous designs:', prev);

      // 检查是否已经存在相同ID的设计，避免重复添加
      const existingDesign = prev.find(d => d.id === designItem.id);
      if (existingDesign) {
        console.log('⚠️ Design already exists, skipping duplicate:', designItem.id);
        return prev; // 返回原数组，不添加重复项
      }

      // 检查是否已经存在相同image_url的设计，避免重复添加
      const existingByUrl = prev.find(d => d.image_url === designItem.image_url);
      if (existingByUrl) {
        console.log('⚠️ Design with same image_url already exists, skipping duplicate:', designItem.image_url);
        return prev; // 返回原数组，不添加重复项
      }

      // 添加新设计
      const newDesigns = [...prev, designItem];
      console.log('✅ Adding new design to canvas:', designItem.id);
      console.log('New designs array:', newDesigns);

      // 如果超过最大数量，移除最旧的设计
      if (newDesigns.length > maxDesigns) {
        const trimmedDesigns = newDesigns.slice(-maxDesigns);
        console.log('Trimmed designs (exceeded max):', trimmedDesigns);
        return trimmedDesigns;
      }
      return newDesigns;
    });

    return designItem.id;
  }, [designs, autoArrange, gridSnap, gridSize, maxDesigns]);

  // 更新设计
  const updateDesign = useCallback((designId: string, updates: Partial<DesignItem>) => {
    setDesigns(prev => prev.map(design =>
      design.id === designId ? { ...design, ...updates } : design
    ));
  }, []);

  // 删除设计
  const deleteDesign = useCallback((designId: string) => {
    setDesigns(prev => prev.filter(design => design.id !== designId));
    if (selectedDesignId === designId) {
      setSelectedDesignId(null);
    }
  }, [selectedDesignId]);

  // 选中设计
  const selectDesign = useCallback((designId: string | null) => {
    setSelectedDesignId(designId);
    setDesigns(prev => prev.map(design => ({
      ...design,
      isSelected: design.id === designId
    })));
  }, []);

  // 清空所有设计
  const clearDesigns = useCallback(() => {
    setDesigns([]);
    setSelectedDesignId(null);
  }, []);

  // 获取选中的设计
  const selectedDesign = designs.find(d => d.id === selectedDesignId) || null;

  // 移动设计到前台
  const bringToFront = useCallback((designId: string) => {
    setDesigns(prev => {
      const design = prev.find(d => d.id === designId);
      if (!design) return prev;
      
      const others = prev.filter(d => d.id !== designId);
      return [...others, design];
    });
  }, []);

  // 批量更新设计
  const updateDesigns = useCallback((newDesigns: DesignItem[]) => {
    setDesigns(newDesigns);
  }, []);

  // 获取设计统计信息
  const getStats = useCallback(() => {
    return {
      total: designs.length,
      selected: selectedDesignId ? 1 : 0,
      hasSelection: selectedDesignId !== null
    };
  }, [designs.length, selectedDesignId]);

  return {
    // 状态
    designs,
    selectedDesignId,
    selectedDesign,
    canvasRef,
    
    // 操作方法
    addDesign,
    updateDesign,
    updateDesigns,
    deleteDesign,
    selectDesign,
    clearDesigns,
    bringToFront,
    
    // 工具方法
    getStats
  };
}

// 辅助函数：寻找可用位置
function findAvailablePosition(
  existingPositions: { x: number; y: number }[],
  canvasWidth: number,
  canvasHeight: number,
  imageWidth = 240,
  imageHeight = 320
): { x: number; y: number } {
  const margin = 20;
  const cols = Math.floor((canvasWidth - margin * 2) / (imageWidth + margin));
  const rows = Math.floor((canvasHeight - margin * 2) / (imageHeight + margin));

  // 尝试网格布局
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      const x = margin + col * (imageWidth + margin) + imageWidth / 2;
      const y = margin + row * (imageHeight + margin) + imageHeight / 2;

      // 检查是否与现有位置冲突
      const hasConflict = existingPositions.some(pos => {
        const dx = Math.abs(pos.x - x);
        const dy = Math.abs(pos.y - y);
        return dx < imageWidth / 2 + margin && dy < imageHeight / 2 + margin;
      });

      if (!hasConflict) {
        return { x, y };
      }
    }
  }

  // 如果网格位置都被占用，随机放置
  const attempts = 10;
  for (let i = 0; i < attempts; i++) {
    const x = margin + Math.random() * (canvasWidth - margin * 2 - imageWidth) + imageWidth / 2;
    const y = margin + Math.random() * (canvasHeight - margin * 2 - imageHeight) + imageHeight / 2;

    const hasConflict = existingPositions.some(pos => {
      const dx = Math.abs(pos.x - x);
      const dy = Math.abs(pos.y - y);
      return dx < imageWidth / 2 + margin && dy < imageHeight / 2 + margin;
    });

    if (!hasConflict) {
      return { x, y };
    }
  }

  // 最后回退到中心位置
  return { x: canvasWidth / 2, y: canvasHeight / 2 };
}

// 导出类型
export type { DesignItem };
