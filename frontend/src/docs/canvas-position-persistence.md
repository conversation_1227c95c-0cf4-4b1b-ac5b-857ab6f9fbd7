# 画布位置状态持久化功能

## 功能概述

已完善画布上artifacts位置状态变更保存功能，现在刷新页面后能够记住移动后的位置。根据需求，只记住位置信息，不记住放大和缩小状态。

## 主要改进

### 1. DesignCanvas组件增强

**新增方法：**
- `getViewState()`: 获取当前画布视图状态（缩放和偏移）
- `setViewState()`: 设置画布视图状态
- `onPositionChange`: 位置变更回调，在拖拽结束时触发

**关键实现：**
```typescript
// 获取当前视图状态
const getViewState = useCallback(() => ({
  scale: canvasZoom,
  offset_x: canvasOffset.x,
  offset_y: canvasOffset.y
}), [canvasZoom, canvasOffset]);

// 拖拽结束时触发保存
const handleGlobalMouseUp = useCallback(() => {
  const wasDragging = isDragging;
  // ... 清理拖拽状态
  
  // 如果刚完成拖拽，触发位置变更回调
  if (wasDragging && onPositionChange) {
    onPositionChange(designs);
  }
}, [isDragging, onPositionChange, designs]);
```

### 2. useCanvasPersistence Hook更新

**返回类型改进：**
```typescript
// 之前只返回设计数组
loadCanvasState: () => Promise<DesignItem[] | null>

// 现在返回完整状态对象
loadCanvasState: () => Promise<{
  designs: DesignItem[];
  viewState: { scale: number; offset_x: number; offset_y: number }
} | null>
```

**保存和加载逻辑：**
- 保存时同时保存设计位置和画布视图状态
- 加载时恢复设计位置和画布视图状态
- 支持本地存储和服务器存储双重保障

### 3. DesignContext集成

**自动保存机制：**
- 添加设计时自动保存当前状态
- 位置变更时自动触发保存
- 获取真实的画布视图状态而不是固定值

**状态恢复：**
```typescript
// 恢复时同时设置视图状态和设计位置
if (canvasRef.current) {
  canvasRef.current.clearDesigns();
  canvasRef.current.setViewState(savedState.viewState);
  
  savedState.designs.forEach(design => {
    canvasRef.current?.addDesign(design);
  });
}
```

### 4. DesignWorkspace组件集成

**位置变更处理：**
```typescript
const handlePositionChange = (designs: any[]) => {
  console.log('设计位置已变更，自动保存状态');
  saveCanvasState();
};

<DesignCanvas
  ref={canvasRef}
  onPositionChange={handlePositionChange}
  // ... 其他props
/>
```

## 工作流程

### 保存流程
1. 用户拖拽设计图片
2. 拖拽结束时触发 `handleGlobalMouseUp`
3. 调用 `onPositionChange` 回调
4. 获取当前设计列表和视图状态
5. 通过 `persistCanvasState` 保存到本地和服务器

### 加载流程
1. 页面加载时调用 `loadCanvasState`
2. 优先从服务器获取状态，失败时使用本地存储
3. 解析返回的设计和视图状态
4. 恢复画布视图状态
5. 重新添加所有设计到画布

## 存储格式

### 本地存储
```typescript
const saveData = {
  data: {
    artifacts: CanvasArtifactPosition[],
    view_state: { scale: number; offset_x: number; offset_y: number },
    canvas_metadata: { version: string; total_artifacts: number; saved_at: string }
  },
  timestamp: number,
  version: string
};
```

### 服务器存储
通过 `/api/canvas/state` 端点保存和获取，格式与本地存储一致。

## 性能优化

1. **防抖保存**: 避免频繁的保存操作
2. **拖拽优化**: 只在拖拽结束时保存，拖拽过程中不保存
3. **本地缓存**: 优先使用本地存储，减少网络请求
4. **错误恢复**: 网络失败时自动降级到本地存储

## 用户体验

- ✅ 拖拽图片到任意位置
- ✅ 刷新页面后位置保持不变
- ✅ 支持多个图片的位置记忆
- ✅ 离线时也能保存位置
- ✅ 自动保存，无需手动操作
- ✅ 只记住位置，不记住缩放状态（符合需求）

## 测试建议

1. 生成几个设计图片
2. 拖拽到不同位置
3. 刷新浏览器页面
4. 验证所有图片位置保持不变
5. 测试离线情况下的位置保存
