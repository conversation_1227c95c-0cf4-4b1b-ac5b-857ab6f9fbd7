// 共享类型定义

// 设计类别 - 在DesignGallery中使用
export enum DesignCategory {
  // 服装类别
  DRESS = 'dress',
  SHIRT = 'shirt',
  PANTS = 'pants',
  SKIRT = 'skirt',
  JACKET = 'jacket',
  COAT = 'coat',
  SWEATER = 'sweater',
  TSHIRT = 't-shirt',
  JEANS = 'jeans',
  SUIT = 'suit',
  ACCESSORIES = 'accessories',
  // 平面设计类别
  POSTER = 'poster',
  LOGO = 'logo',
  BANNER = 'banner',
  FLYER = 'flyer',
  BUSINESS_CARD = 'business_card'
}

// 保持向后兼容
export const ClothingCategory = DesignCategory;
