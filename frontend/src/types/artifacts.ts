// 通用画布产物类型系统
export enum ArtifactType {
  IMAGE = 'image',
  TEXT = 'text',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document'
}

export enum DesignCategory {
  FASHION = 'fashion',
  LOGO = 'logo',
  POSTER = 'poster',
  DRAMA = 'drama',
  UI = 'ui'
}

// 基础产物接口
export interface BaseDesignItem {
  id: string;
  type: ArtifactType;
  category: DesignCategory;
  title: string;
  timestamp: Date;
  position: { x: number; y: number };
  scale: number;
  isSelected: boolean;
  metadata?: {
    agent_type?: string;
    workflow_stage?: string;
    [key: string]: any;
  };
}

// 图片产物（兼容现有设计）
export interface ImageArtifact extends BaseDesignItem {
  type: ArtifactType.IMAGE;
  image_url: string;
  prompt?: string;
  metadata?: {
    ai_model?: string;
    generation_params?: any;
    originalWidth?: number;
    originalHeight?: number;
    design_type?: 'fashion' | 'logo' | 'poster' | 'character' | 'storyboard';
  } & BaseDesignItem['metadata'];
}

// 文本产物（剧本、分镜脚本等）
export interface TextArtifact extends BaseDesignItem {
  type: ArtifactType.TEXT;
  content: string;
  format?: 'plain' | 'markdown' | 'json' | 'script';
  metadata?: {
    word_count?: number;
    language?: string;
    text_type?: 'script' | 'storyboard' | 'character_description' | 'scene_plan';
  } & BaseDesignItem['metadata'];
}

// 视频产物（短剧、视频片段等）
export interface VideoArtifact extends BaseDesignItem {
  type: ArtifactType.VIDEO;
  video_url: string;
  thumbnail_url?: string;
  duration?: number;
  metadata?: {
    resolution?: string;
    format?: string;
    video_type?: 'segment' | 'final_drama' | 'preview';
    source_images?: string[];
  } & BaseDesignItem['metadata'];
}

// 音频产物（配音、音乐等）
export interface AudioArtifact extends BaseDesignItem {
  type: ArtifactType.AUDIO;
  audio_url: string;
  duration?: number;
  metadata?: {
    format?: string;
    audio_type?: 'voiceover' | 'background_music' | 'sound_effect';
    character?: string;
  } & BaseDesignItem['metadata'];
}

// 文档产物（方案、报告等）
export interface DocumentArtifact extends BaseDesignItem {
  type: ArtifactType.DOCUMENT;
  content: string;
  download_url?: string;
  metadata?: {
    file_type?: string;
    document_type?: 'design_plan' | 'production_report' | 'requirements';
  } & BaseDesignItem['metadata'];
}

// 联合类型
export type DesignItem = ImageArtifact | TextArtifact | VideoArtifact | AudioArtifact | DocumentArtifact;

// 画布区域配置
export interface CanvasZone {
  id: string;
  name: string;
  bounds: { x: number; y: number; width: number; height: number };
  acceptedTypes: ArtifactType[];
  acceptedCategories: DesignCategory[];
  backgroundColor?: string;
  borderColor?: string;
}

// 预定义区域配置
export const CANVAS_ZONES: Record<string, CanvasZone> = {
  // 通用设计区域（兼容现有功能）
  DESIGN_GALLERY: {
    id: 'design_gallery',
    name: '设计作品',
    bounds: { x: 0, y: 0, width: 800, height: 400 },
    acceptedTypes: [ArtifactType.IMAGE],
    acceptedCategories: [DesignCategory.FASHION, DesignCategory.LOGO, DesignCategory.POSTER]
  },
  
  // 短剧制作区域
  DRAMA_SCRIPT: {
    id: 'drama_script',
    name: '剧本区域',
    bounds: { x: 0, y: 420, width: 300, height: 200 },
    acceptedTypes: [ArtifactType.TEXT],
    acceptedCategories: [DesignCategory.DRAMA]
  },
  
  DRAMA_CHARACTERS: {
    id: 'drama_characters',
    name: '角色设计',
    bounds: { x: 320, y: 420, width: 300, height: 200 },
    acceptedTypes: [ArtifactType.IMAGE, ArtifactType.TEXT],
    acceptedCategories: [DesignCategory.DRAMA]
  },
  
  DRAMA_STORYBOARD: {
    id: 'drama_storyboard',
    name: '分镜图片',
    bounds: { x: 640, y: 420, width: 300, height: 200 },
    acceptedTypes: [ArtifactType.IMAGE],
    acceptedCategories: [DesignCategory.DRAMA]
  },
  
  DRAMA_VIDEOS: {
    id: 'drama_videos',
    name: '视频制作',
    bounds: { x: 0, y: 640, width: 600, height: 200 },
    acceptedTypes: [ArtifactType.VIDEO],
    acceptedCategories: [DesignCategory.DRAMA]
  },
  
  DRAMA_AUDIO: {
    id: 'drama_audio',
    name: '音频制作',
    bounds: { x: 620, y: 640, width: 300, height: 200 },
    acceptedTypes: [ArtifactType.AUDIO],
    acceptedCategories: [DesignCategory.DRAMA]
  },
  
  DRAMA_FINAL: {
    id: 'drama_final',
    name: '最终作品',
    bounds: { x: 0, y: 860, width: 920, height: 200 },
    acceptedTypes: [ArtifactType.VIDEO, ArtifactType.DOCUMENT],
    acceptedCategories: [DesignCategory.DRAMA]
  }
};

// 产物分类服务
export class ArtifactClassificationService {
  static classifyArtifact(workflowData: any): {
    type: ArtifactType;
    category: DesignCategory;
    subType?: string;
  } {
    const agentType = workflowData.agent_type;
    const workflowStage = workflowData.workflow_stage || workflowData.production_phase;
    
    // 基于Agent类型分类
    if (agentType === 'FashionDesignAgent') {
      return {
        type: ArtifactType.IMAGE,
        category: DesignCategory.FASHION,
        subType: 'fashion_design'
      };
    }
    
    if (agentType === 'DramaProductionAgent') {
      // 基于工作流阶段细分
      switch (workflowStage) {
        case 'script':
          return {
            type: ArtifactType.TEXT,
            category: DesignCategory.DRAMA,
            subType: 'script'
          };
        case 'character':
          return workflowData.image_url ? {
            type: ArtifactType.IMAGE,
            category: DesignCategory.DRAMA,
            subType: 'character_image'
          } : {
            type: ArtifactType.TEXT,
            category: DesignCategory.DRAMA,
            subType: 'character_description'
          };
        case 'scene':
          return workflowData.image_url ? {
            type: ArtifactType.IMAGE,
            category: DesignCategory.DRAMA,
            subType: 'storyboard_image'
          } : {
            type: ArtifactType.TEXT,
            category: DesignCategory.DRAMA,
            subType: 'scene_plan'
          };
        case 'video':
          return {
            type: ArtifactType.VIDEO,
            category: DesignCategory.DRAMA,
            subType: 'video_segment'
          };
        case 'post_production':
          return {
            type: ArtifactType.VIDEO,
            category: DesignCategory.DRAMA,
            subType: 'final_drama'
          };
      }
    }
    
    // 默认分类
    return {
      type: ArtifactType.IMAGE,
      category: DesignCategory.FASHION,
      subType: 'unknown'
    };
  }
  
  static getRecommendedZone(artifact: DesignItem): string {
    const { type, category, metadata } = artifact;
    
    // 短剧相关产物
    if (category === DesignCategory.DRAMA) {
      if (type === ArtifactType.TEXT && metadata?.text_type === 'script') {
        return 'drama_script';
      }
      if (type === ArtifactType.IMAGE && metadata?.design_type === 'character') {
        return 'drama_characters';
      }
      if (type === ArtifactType.IMAGE && metadata?.design_type === 'storyboard') {
        return 'drama_storyboard';
      }
      if (type === ArtifactType.VIDEO && metadata?.video_type === 'segment') {
        return 'drama_videos';
      }
      if (type === ArtifactType.VIDEO && metadata?.video_type === 'final_drama') {
        return 'drama_final';
      }
      if (type === ArtifactType.AUDIO) {
        return 'drama_audio';
      }
    }
    
    // 传统设计产物
    if (type === ArtifactType.IMAGE && 
        [DesignCategory.FASHION, DesignCategory.LOGO, DesignCategory.POSTER].includes(category)) {
      return 'design_gallery';
    }
    
    return 'design_gallery'; // 默认区域
  }
}

// 向后兼容函数
export function migrateOldDesignItem(oldItem: any): ImageArtifact {
  return {
    ...oldItem,
    type: ArtifactType.IMAGE,
    category: DesignCategory.FASHION,
    title: oldItem.prompt || `设计 ${oldItem.id.slice(0, 8)}`,
    metadata: {
      ...oldItem.metadata,
      design_type: 'fashion'
    }
  };
}
