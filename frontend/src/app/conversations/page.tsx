'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { ArrowLeft, MessageSquare, Calendar, Image as ImageIcon, Sparkles, Edit } from 'lucide-react';
import Image from 'next/image';

interface ConversationItem {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  designCount: number;
  previewImage?: string;
}

export default function ConversationHistoryPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [conversations, setConversations] = useState<ConversationItem[]>([]);
  const [isLoadingConversations, setIsLoadingConversations] = useState(true);

  // 加载对话历史
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      loadConversations();
    }
  }, [isLoading, isAuthenticated]);

  const loadConversations = async () => {
    try {
      setIsLoadingConversations(true);
      
      // 获取所有会话
      const conversations = await apiClient.getConversations(0, 50);
      
      // 为每个会话获取消息信息
      const conversationPromises = conversations.map(async (conversation) => {
        try {
          const messages = await apiClient.getChatMessages(conversation.id);
          
          if (messages.length === 0) return null;

          // 使用会话标题
          const lastMessage = messages[messages.length - 1];
          
          // 计算设计数量
          const designCount = messages.filter(msg => msg.image_url).length;
          
          // 获取预览图片
          const previewImage = messages.find(msg => msg.image_url)?.image_url;

          return {
            id: conversation.id,
            title: conversation.title,
            lastMessage: lastMessage?.content.substring(0, 100) + (lastMessage?.content.length > 100 ? '...' : '') || '新会话',
            timestamp: new Date(conversation.updated_at),
            messageCount: messages.length,
            designCount: designCount,
            previewImage: previewImage
          };
        } catch (error) {
          console.error('Failed to load messages for conversation:', conversation.id, error);
          return null;
        }
      });

      const results = await Promise.all(conversationPromises);
      const validConversations = results.filter(conv => conv !== null) as ConversationItem[];
      
      // 按时间倒序排列
      validConversations.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      
      setConversations(validConversations);
    } catch (error) {
      console.error('Failed to load conversations:', error);
    } finally {
      setIsLoadingConversations(false);
    }
  };

  // 点击对话项，跳转到设计器页面并恢复对话
  const handleConversationClick = (conversation: ConversationItem) => {
    // 通过URL参数传递会话ID，使用project_id保持一致性
    router.push(`/designer?project_id=${conversation.id}`);
  };

  
  // 格式化时间
  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 48) {
      return '昨天';
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (isLoading || isLoadingConversations) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载对话历史...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/')}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>返回首页</span>
              </button>
              
              <div className="h-6 w-px bg-gray-300" />
              
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">对话历史</h1>
                  <p className="text-sm text-gray-500">查看和管理您的设计对话</p>
                </div>
              </div>
            </div>

            </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {conversations.length === 0 ? (
          <div className="text-center py-16">
            <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无对话历史</h3>
            <p className="text-gray-500 mb-6">开始您的第一个设计对话吧！</p>
            <button
              onClick={() => router.push('/')}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all flex items-center space-x-2 mx-auto"
            >
              <Sparkles className="h-5 w-5" />
              <span>开始设计</span>
            </button>
          </div>
        ) : (
          <div className="grid gap-4">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => handleConversationClick(conversation)}
                className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md hover:border-blue-200 transition-all cursor-pointer group"
              >
                <div className="flex items-start space-x-4">
                  {/* 预览图片 */}
                  {conversation.previewImage && (
                    <div className="flex-shrink-0">
                      <div className="w-20 h-24 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                          src={conversation.previewImage}
                          alt="设计预览"
                          width={80}
                          height={96}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  )}

                  {/* 对话内容 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                        {conversation.title}
                      </h3>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        <span>{formatTime(conversation.timestamp)}</span>
                      </div>
                    </div>

                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {conversation.lastMessage}
                    </p>

                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <MessageSquare className="h-4 w-4" />
                        <span>{conversation.messageCount} 条消息</span>
                      </div>
                      {conversation.designCount > 0 && (
                        <div className="flex items-center space-x-1">
                          <ImageIcon className="h-4 w-4" />
                          <span>{conversation.designCount} 个设计</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleConversationClick(conversation);
                      }}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="继续对话"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}