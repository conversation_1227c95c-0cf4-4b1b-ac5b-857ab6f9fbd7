'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { Sparkles, History, LogOut, ChevronDown } from 'lucide-react';
import ConversationDropdown from '@/components/ConversationDropdown';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isLoading, logout, user } = useAuth();
  const [inputMessage, setInputMessage] = useState('');
  const [isConversationDropdownOpen, setIsConversationDropdownOpen] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // 处理开始设计按钮点击
  const handleStartDesign = async () => {
    if (!inputMessage.trim() || isCreatingProject) return;

    try {
      setIsCreatingProject(true);
      
      // 生成新的会话ID
      const idResponse = await apiClient.generateConversationId();
      if (idResponse.success && idResponse.data) {
        const projectId = idResponse.data.conversation_id;
        console.log('🆕 生成新项目ID:', projectId);
        
        // 跳转到设计器页面并传递project_id参数
        router.push(`/designer?project_id=${projectId}`);
      } else {
        throw new Error('生成会话ID失败');
      }
    } catch (error) {
      console.error('Failed to start design:', error);
      // 如果生成会话ID失败，仍然跳转到设计页面（会被重定向回首页）
      router.push('/designer');
    } finally {
      setIsCreatingProject(false);
    }
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 z-50 flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-lg">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">创意设计平台</h1>
                <p className="text-sm text-gray-500">您的AI智能设计伙伴</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* 历史对话下拉菜单 */}
              <div className="relative">
                <button
                  onClick={() => setIsConversationDropdownOpen(!isConversationDropdownOpen)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-all"
                  title="历史对话"
                >
                  <History className="h-4 w-4" />
                  <span className="text-sm">历史对话</span>
                  <ChevronDown className={`h-3 w-3 transition-transform ${isConversationDropdownOpen ? 'rotate-180' : ''}`} />
                </button>
                
                <ConversationDropdown 
                  isOpen={isConversationDropdownOpen}
                  onToggle={() => setIsConversationDropdownOpen(!isConversationDropdownOpen)}
                />
              </div>

              {/* 用户菜单 */}
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-600">
                  欢迎，{user?.email || '用户'}
                </span>
                <button
                  onClick={logout}
                  className="flex items-center space-x-1 text-gray-500 hover:text-gray-700 transition-colors"
                  title="退出登录"
                >
                  <LogOut className="h-4 w-4" />
                  <span className="text-sm">退出</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center">
        <div className="max-w-2xl mx-auto text-center">
          {/* 欢迎界面 */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              告诉我您想要什么样的设计
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              用自然语言描述您的创意需求，我将与您一起创造独特的设计作品
            </p>
          </div>

          {/* 输入区域 */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
            <div className="space-y-4">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="例如：我想要一张现代科技风格的海报，蓝白配色，用于产品发布会..."
                className="w-full h-24 px-4 py-3 border-0 bg-gray-50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:outline-none focus:bg-white resize-none text-base transition-all"
                
              />
              <button
                onClick={handleStartDesign}
                disabled={!inputMessage.trim() || isCreatingProject}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white py-3 px-6 rounded-xl transition-all flex items-center justify-center space-x-2 shadow-sm"
              >
                {isCreatingProject ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <Sparkles className="h-5 w-5" />
                )}
                <span>{isCreatingProject ? '创建中...' : '开始设计'}</span>
              </button>
            </div>
          </div>

          {/* 示例提示 */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-100/50">
            <p className="text-sm text-gray-600 mb-4">试试这些示例：</p>
            <div className="flex flex-wrap gap-2 justify-center">
              {[
                '设计一张科技海报',
                '创建品牌Logo',
                '设计一件连衣裙',
                '制作宣传海报',
                '设计商务名片'
              ].map((example) => (
                <button
                  key={example}
                  onClick={() => {
                    setInputMessage(example);
                    // 自动触发开始设计
                    setTimeout(() => {
                      const event = new Event('input', { bubbles: true });
                      const textarea = document.querySelector('textarea');
                      if (textarea) {
                        textarea.value = example;
                        textarea.dispatchEvent(event);
                      }
                      handleStartDesign();
                    }, 100);
                  }}
                  className="px-3 py-2 bg-white/80 hover:bg-white text-gray-700 rounded-lg text-sm transition-all border border-gray-200 hover:border-gray-300 shadow-sm"
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
