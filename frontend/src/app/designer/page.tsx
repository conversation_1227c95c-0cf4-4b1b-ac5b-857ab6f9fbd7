'use client';

import { useState, useRef, useEffect, Suspense } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient, Conversation } from '@/lib/api';
import { Send, Sparkles, RotateCcw, Loader2, ArrowLeft, History, ChevronDown, Clock, MessageCircle } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams, redirect } from 'next/navigation';

import DesignCanvas from '@/components/DesignCanvas';
import { DesignProvider, useDesignGeneration, useDesignContext } from '@/contexts/DesignContext';

// 组件包装器，处理searchParams
function SearchParamsWrapper({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    }>
      {children}
    </Suspense>
  );
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  imageUrl?: string;
  timestamp: Date;
  type?: 'chat' | 'design_request' | 'design_result';
}

interface DesignRequirements {
  style: string;
  colors: string[];
  category: string;
  materials?: string[];
  occasion?: string;
  target_gender?: string;
  key_features?: string[];
  mood?: string;
}

// 内部组件，使用设计context
function DesignerWorkspaceInner({ conversationId }: { conversationId: string }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { handleDesignGenerated } = useDesignGeneration();
  const { clearAllDesigns, canvasRef, saveCanvasState } = useDesignContext();
  const [inputMessage, setInputMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isLoadingConversation, setIsLoadingConversation] = useState(true); // 默认为true，显示loading
  const [conversationState, setConversationState] = useState<'idle' | 'generating'>('idle');

  // conversationId现在作为props传入，不需要内部状态
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isHistoryDropdownOpen, setIsHistoryDropdownOpen] = useState(false);
  const [conversations, setConversations] = useState<any[]>([]);
  const [isLoadingConversations, setIsLoadingConversations] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 确保在客户端运行
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 从URL参数获取项目ID
  const projectIdFromUrl = isClient ? searchParams?.get('project_id') : null;
  
  // 使用useRef来跟踪上一次的project_id，避免重复调用
  const previousProjectId = useRef<string | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // WebSocket监听设计图实时推送
  useEffect(() => {
    if (!conversationId) {
      console.log('⏳ No conversation ID yet, waiting for backend to provide one');
      return;
    }

    console.log(`🔌 Setting up WebSocket connection for conversation: ${conversationId}`);
    const wsUrl = `ws://localhost:8000/ws/${conversationId}`;

    // 添加小延迟确保后端已经准备好处理这个conversation_id
    let ws: WebSocket | null = null;

    const connectTimer = setTimeout(() => {
      ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log(`✅ WebSocket connected for design updates: ${conversationId}`);
        
        // 检查是否有待处理的自动处理任务
        if (false) { // 禁用自动处理
          console.log('🔄 Auto-processing disabled');
        }
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', message);

          if (message.type === 'design_generated') {
            // 实时添加设计图到画布
            const designData = message.data;
            console.log('🎨 Real-time design generated:', designData);

            handleDesignGenerated({
              image_url: designData.image_url,
              design_prompt: designData.design_prompt,
              agent_type: designData.agent_type,
              task_id: designData.task_id,
              design_id: designData.design_id,
              metadata: designData.metadata
            });
          } else if (message.type === 'conversation_result') {
            // 处理对话结果（AI回复消息）- 现在改为同步处理，WebSocket不再推送消息
            // 这个逻辑保留作为兼容性处理，但一般情况下不会触发
            const resultData = message.data;
            console.log('💬 Conversation result received via WebSocket:', resultData);

            // 只在同步处理没有完成的情况下才处理WebSocket消息
            if (!isGenerating) {
              if (resultData.messages && resultData.messages.length > 0) {
                const newMessages = resultData.messages
                  .filter((msg: any) => msg.role === 'assistant')
                  .map((msg: any) => ({
                    id: Date.now().toString() + Math.random(),
                    role: 'assistant' as const,
                    content: msg.content,
                    timestamp: new Date(),
                    type: msg.type || 'chat'
                  }));

                setMessages(prev => [...prev, ...newMessages]);
              }
            } else {
              console.log('🚫 忽略WebSocket消息，因为正在同步处理中');
            }

            // 如果有设计结果，也添加到画布
            if (resultData.image_url) {
              handleDesignGenerated({
                image_url: resultData.image_url,
                design_prompt: resultData.design_prompt || resultData.optimized_prompt,
                agent_type: 'workflow',
                task_id: 'workflow_task',
                design_id: `design_${Date.now()}`,
                metadata: resultData.metadata || {}
              });
            }

            // 设置生成完成状态
            setIsGenerating(false);
            setConversationState('idle');
          } else if (message.type === 'task_progress') {
            // 处理任务进度更新
            console.log('📊 Task progress update:', message.data);
          } else if (message.type === 'collaboration_status') {
            // 处理协作状态更新
            console.log('🤝 Collaboration status update:', message.data);
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log(`🔌 WebSocket disconnected for conversation: ${conversationId}`);
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
    }, 100); // 100ms延迟

    return () => {
      clearTimeout(connectTimer);
      if (ws) {
        console.log(`🔌 Closing WebSocket connection for: ${conversationId}`);
        ws.close();
      }
    };
  }, [conversationId, handleDesignGenerated]);

  // 意图识别函数
  const detectIntent = (message: string): 'chat' | 'design_request' => {
    const designKeywords = [
      '设计', '创建', '生成', '制作', '想要', '需要',
      '海报', '标志', 'logo', '服装', '衣服', '裙子', '上衣', '裤子', '外套',
      '风格', '颜色', '材质', '款式', '宣传', '品牌', '视觉'
    ];

    const lowerMessage = message.toLowerCase();
    const hasDesignKeyword = designKeywords.some(keyword =>
      lowerMessage.includes(keyword) || message.includes(keyword)
    );

    // 如果是第一条消息或包含设计关键词，认为是设计请求
    return (messages.length === 0 || hasDesignKeyword) ? 'design_request' : 'chat';
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  
  // Initialize or get current conversation
  useEffect(() => {
    const initializeConversation = async () => {
      // Don't initialize if auth is still loading or user is not authenticated
      if (authLoading) {
        // 如果auth还在loading，保持当前的loading状态，不改变
        return;
      }
      
      if (!isAuthenticated) {
        setIsLoadingConversation(false);
        return;
      }

      try {
        // 不在这里重复设置loading状态，因为初始值已经是true

        // 获取URL参数
        const projectIdFromUrl = isClient ? searchParams?.get('project_id') : null;
        
        console.log('🔍 URL参数分析:', { projectIdFromUrl });
        
        // 检查是否已经加载过这个project_id，避免重复调用
        if (projectIdFromUrl && previousProjectId.current === projectIdFromUrl) {
          console.log('🔄 项目ID未变化，跳过重复加载');
          // 如果是首次加载，保持loading状态直到数据加载完成
          if (messages.length === 0) {
            console.log('🔄 首次加载，保持loading状态');
            return;
          }
          setIsLoadingConversation(false);
          return;
        }
        
        // 更新上一次的project_id
        if (projectIdFromUrl) {
          previousProjectId.current = projectIdFromUrl;
        }

        // 如果有projectId，说明是修改现有设计
        if (projectIdFromUrl) {
          console.log('📝 加载现有项目:', projectIdFromUrl);
          // conversationId现在由父组件管理，不需要在这里设置
          
          // 加载现有对话的消息历史
          try {
            const messages = await apiClient.getChatMessages(projectIdFromUrl);
            const formattedMessages = messages.map(msg => ({
              id: msg.id,
              role: msg.role,
              content: msg.content,
              imageUrl: msg.image_url,
              timestamp: new Date(msg.created_at),
              type: 'chat'
            }));
            setMessages(formattedMessages);
            console.log('✅ 已加载历史消息:', formattedMessages.length, '条');
            
            // 加载并恢复设计图片
            const designMessages = messages.filter(msg => msg.image_url);
            designMessages.forEach(msg => {
              if (msg.image_url) {
                handleDesignGenerated({
                  image_url: msg.image_url,
                  design_prompt: msg.content,
                  agent_type: 'historical',
                  task_id: `historical_${msg.id}`,
                  design_id: `design_${msg.id}`,
                  metadata: {
                    is_historical: true,
                    created_at: msg.created_at
                  }
                });
              }
            });
            console.log('✅ 已恢复历史设计图片:', designMessages.length, '张');
            
            // 加载历史设计产物到canvas
            try {
              const artifactsResponse = await apiClient.getConversationArtifacts(projectIdFromUrl);
              if (artifactsResponse.success && artifactsResponse.data.artifacts.length > 0) {
                console.log(`🎨 Loading ${artifactsResponse.data.artifacts.length} historical artifacts for conversation ${projectIdFromUrl}`);
                
                artifactsResponse.data.artifacts.forEach(artifact => {
                  if (artifact.file_url) {
                    handleDesignGenerated({
                      image_url: artifact.file_url,
                      design_prompt: artifact.metadata?.design_prompt || artifact.artifact_name,
                      agent_type: artifact.metadata?.agent_type,
                      task_id: artifact.task_id,
                      design_id: artifact.id,
                      metadata: {
                        ...artifact.metadata,
                        artifact_type: artifact.artifact_type,
                        created_at: artifact.created_at,
                        is_historical: true
                      }
                    });
                  }
                });
                console.log('✅ 已加载历史artifacts:', artifactsResponse.data.artifacts.length, '个');
              }
            } catch (error) {
              console.error('Failed to load historical artifacts:', error);
            }
            
            // 所有数据加载完成后才隐藏loading
            console.log('✅ 所有数据加载完成，隐藏loading');
            setIsLoadingConversation(false);
          } catch (error) {
            console.error('❌ 加载历史消息失败:', error);
            setIsLoadingConversation(false);
          }
        } 
        // 如果没有projectId，说明是手动新增设计，等待用户输入
        else {
          console.log('🆕 新建对话，等待用户输入');
          // 添加短暂延迟以确保用户体验一致性
          setTimeout(() => {
            setIsLoadingConversation(false);
          }, 500);
        }

        // 有projectId的情况已经在各自的try-catch块中处理了loading状态
      } catch (error) {
        console.error('❌ 初始化对话失败:', error);
        setIsLoadingConversation(false);
      }
    };

    initializeConversation();
  }, [authLoading, isAuthenticated, isClient]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;

    const messageContent = inputMessage;
    setInputMessage('');

    // 检查是否是图片编辑请求
    const isImageEditRequest = editingImage !== null;

    // 如果是图片编辑请求，需要先添加图片消息，再添加用户消息
    if (isImageEditRequest && editingImage) {
      const imageMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: `请编辑这张设计图：`,
        imageUrl: editingImage.image_url,
        timestamp: new Date(),
        type: 'chat'
      };

      const userMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'user',
        content: messageContent,
        timestamp: new Date(),
        type: 'chat'
      };

      setMessages(prev => [...prev, imageMessage, userMessage]);
    } else {
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: messageContent,
        timestamp: new Date(),
        type: 'chat'
      };

      setMessages(prev => [...prev, userMessage]);
    }
    setIsGenerating(true);

    try {
      console.log('=== 发送消息 ===');
      console.log('消息内容:', messageContent);
      console.log('当前对话ID:', conversationId);
      console.log('是否为图片编辑请求:', isImageEditRequest);
      console.log('编辑图片信息:', editingImage);

      // 构建请求数据 - 必须包含conversation_id
      const requestData: any = {
        content: messageContent,
        message_type: 'user'
      };

      // 如果是图片编辑请求，添加编辑相关信息
      if (isImageEditRequest && editingImage) {
        requestData.edit_image_url = editingImage.image_url;
        requestData.edit_image_id = editingImage.id;
        requestData.message_type = 'image_edit';
        console.log('添加图片编辑参数:', {
          edit_image_url: editingImage.image_url,
          edit_image_id: editingImage.id
        });

        // 立即清除编辑状态，让缩略图消失
        setEditingImage(null);
      }

      // 检查是否有conversation_id
      let targetConversationId = conversationId;
      if (!conversationId) {
        console.log('🆕 没有对话ID，生成新对话ID');
        // 生成新的conversation_id
        const idResponse = await apiClient.generateConversationId();
        if (idResponse.success && idResponse.data) {
          targetConversationId = idResponse.data.conversation_id;
          // TODO: 需要重新设计，因为conversationId现在由父组件管理
          // setConversationId(targetConversationId);
          console.log('🆕 已生成新对话ID:', targetConversationId);
        } else {
          throw new Error('生成对话ID失败');
        }
      } else {
        console.log('📝 使用现有对话ID:', conversationId);
        requestData.conversation_id = conversationId;
      }

      // 使用工作流处理所有消息
      const response = await apiClient.processConversation(targetConversationId, requestData);

      console.log('消息发送结果:', response);

      // 处理异步响应
      if (response.success && response.data) {
        const responseData = response.data;
        console.log('响应数据:', responseData);

        
        // 后端现在同步返回处理结果
        console.log('🔌 使用已有对话ID:', targetConversationId);

        // 处理同步返回的结果
        if (responseData.result) {
          console.log('🔄 处理同步返回的结果:', responseData.result);
          
          // 直接处理同步结果
          const workflowData = responseData.result;
          
          // 处理消息 - 只添加AI回复，不添加用户消息（因为用户消息已经在发送前添加了）
          if (workflowData.messages && workflowData.messages.length > 0) {
            const newMessages = workflowData.messages
              .filter((msg: any) => msg.role === 'assistant') // 只添加AI回复
              .map((msg: any) => {
                const message: Message = {
                  id: Date.now().toString() + Math.random(),
                  role: 'assistant' as const,
                  content: msg.content,
                  timestamp: new Date(),
                  type: msg.type || 'chat'
                };

                // 如果是设计相关的完成状态且有图片，添加图片到消息中
                if (workflowData.current_state === 'completed' && workflowData.image_url) {
                  const isDesignIntent = workflowData.intent === 'design_request' ||
                                        workflowData.intent === 'design_modification' ||
                                        workflowData.intent === 'requirement_confirmation' ||
                                        workflowData.intent === 'image_edit';

                  if (isDesignIntent) {
                    message.imageUrl = workflowData.image_url;
                    message.type = 'design_result';
                  }
                }

                return message;
              });

            console.log('添加消息:', newMessages);
            setMessages(prev => [...prev, ...newMessages]);
          }

          // 处理设计结果
          if (workflowData.image_url) {
            const isDesignIntent = workflowData.intent === 'design_request' ||
                                  workflowData.intent === 'design_modification' ||
                                  workflowData.intent === 'requirement_confirmation' ||
                                  workflowData.intent === 'image_edit';

            if (isDesignIntent) {
              console.log('设计生成完成，添加到画布:', workflowData.image_url);
              handleDesignGenerated(workflowData);
            }
          }

          // 设置状态
          if (workflowData.current_state === 'completed') {
            setConversationState('idle');
          } else if (workflowData.current_state === 'design_generation') {
            setConversationState('generating');
          }
        }

        // 同步处理已完成，上面的逻辑已经处理了所有结果
      } else {
        console.error('工作流响应失败:', response);
        // 处理错误响应
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '抱歉，处理您的消息时出现了问题，请稍后再试。',
          timestamp: new Date(),
          type: 'chat'
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('❌ 发送消息失败:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '抱歉，发生了错误。请稍后重试。',
        timestamp: new Date(),
        type: 'chat'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 当前编辑的图片状态
  const [editingImage, setEditingImage] = useState<any>(null);

  // 处理AI编辑
  const handleAIEdit = (design: any) => {
    console.log('=== 开始AI编辑流程 ===');
    console.log('设计信息:', design);

    // 设置当前正在编辑的图片（不立即加入消息历史）
    setEditingImage(design);

    // 清空输入框，让用户输入修改要求
    setInputMessage('');

    console.log('编辑状态已设置，等待用户输入修改要求...');
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingImage(null);
    setInputMessage('');
    console.log('已取消图片编辑');
  };

  // 防抖保存的ref
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 位置变更处理（带防抖）
  const handlePositionChange = (designs: any[]) => {
    console.log('🔄 设计位置已变更，准备保存状态');
    console.log('📊 当前设计数据:', designs.map(d => ({ id: d.id, position: d.position })));

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 防抖保存：200ms后执行保存（拖拽结束后快速保存）
    saveTimeoutRef.current = setTimeout(() => {
      console.log('💾 执行防抖保存');
      const viewState = canvasRef.current?.getViewState() || { scale: 1, offset_x: 0, offset_y: 0 };
      saveCanvasState(designs, viewState);
    }, 200);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  
  // 新会话处理
  const handleNewSession = async () => {
    try {
      // 清理当前会话
      setMessages([]);
      clearAllDesigns();
      setInputMessage('');
      // TODO: 需要重新设计，因为conversationId现在由父组件管理
      // setConversationId(undefined);

      // 生成新的会话ID
      const idResponse = await apiClient.generateConversationId();
      if (idResponse.success && idResponse.data) {
        const newProjectId = idResponse.data.conversation_id;
        // TODO: 需要重新设计，因为conversationId现在由父组件管理
        // setConversationId(newProjectId);
        
        // 更新URL参数
        const url = new URL(window.location.href);
        url.searchParams.set('project_id', newProjectId);
        window.history.pushState({}, '', url);
        
        console.log('🆕 已创建新会话:', newProjectId);
      }
    } catch (error) {
      console.error('创建新会话失败:', error);
    }
  };

  // 历史会话处理
  const handleHistorySession = () => {
    // 跳转到会话管理页面
    router.push('/conversations');
  };

  // 收起/展开处理
  const handleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // 加载历史会话 - 使用优化后的SQL查询
  const loadConversations = async () => {
    try {
      setIsLoadingConversations(true);
      
      // 获取所有会话，后端已经通过SQL多表关联查询优化了性能
      const conversations = await apiClient.getConversations(0, 20);
      
      // 直接使用后端返回的数据，无需额外查询
      const optimizedConversations = conversations.map((conversation) => {
        // 从meta_data中获取预计算的数据
        const metaData = conversation.meta_data || {};
        const messageCount = metaData.message_count || 0;
        const designCount = metaData.design_count || 0;
        const previewImage = metaData.preview_image;
        const lastMessage = metaData.last_message || '新会话';
        
        return {
          id: conversation.id,
          title: conversation.title,
          lastMessage: lastMessage,
          timestamp: new Date(conversation.updated_at),
          messageCount: messageCount,
          designCount: designCount,
          previewImage: previewImage
        };
      });
      
      // 按时间倒序排列，只显示最近10个
      optimizedConversations.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      setConversations(optimizedConversations.slice(0, 10));
    } catch (error) {
      console.error('Failed to load conversations:', error);
    } finally {
      setIsLoadingConversations(false);
    }
  };

  // 点击历史会话项
  const handleHistoryConversationClick = async (conversation: any) => {
    setIsHistoryDropdownOpen(false);
    
    // 如果点击的是当前会话，不做任何操作
    if (conversation.id === conversationId) {
      console.log('🔄 点击的是当前会话，跳过重复加载');
      return;
    }
    
    try {
      // 清理当前状态
      setMessages([]);
      clearAllDesigns();
      setInputMessage('');
      
      // TODO: 需要重新设计，因为conversationId现在由父组件管理
      // setConversationId(conversation.id);
      
      // 加载现有对话的消息历史
      const messages = await apiClient.getChatMessages(conversation.id);
      const formattedMessages = messages.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        imageUrl: msg.image_url,
        timestamp: new Date(msg.created_at),
        type: 'chat'
      }));
      setMessages(formattedMessages);
      console.log('✅ 已加载历史消息:', formattedMessages.length, '条');
      
      // 加载并恢复设计图片
      const designMessages = messages.filter(msg => msg.image_url);
      designMessages.forEach(msg => {
        if (msg.image_url) {
          handleDesignGenerated({
            image_url: msg.image_url,
            design_prompt: msg.content,
            agent_type: 'historical',
            task_id: `historical_${msg.id}`,
            design_id: `design_${msg.id}`,
            metadata: {
              is_historical: true,
              created_at: msg.created_at
            }
          });
        }
      });
      console.log('✅ 已恢复历史设计图片:', designMessages.length, '张');
      
      // 加载历史设计产物到canvas
      try {
        const artifactsResponse = await apiClient.getConversationArtifacts(conversation.id);
        if (artifactsResponse.success && artifactsResponse.data.artifacts.length > 0) {
          console.log(`🎨 Loading ${artifactsResponse.data.artifacts.length} historical artifacts for conversation ${conversation.id}`);
          
          artifactsResponse.data.artifacts.forEach(artifact => {
            if (artifact.file_url) {
              handleDesignGenerated({
                image_url: artifact.file_url,
                design_prompt: artifact.metadata?.design_prompt || artifact.artifact_name,
                agent_type: artifact.metadata?.agent_type,
                task_id: artifact.task_id,
                design_id: artifact.id,
                metadata: {
                  ...artifact.metadata,
                  artifact_type: artifact.artifact_type,
                  created_at: artifact.created_at,
                  is_historical: true
                }
              });
            }
          });
          console.log('✅ 已加载历史artifacts:', artifactsResponse.data.artifacts.length, '个');
        }
      } catch (error) {
        console.error('Failed to load historical artifacts:', error);
      }
      
    } catch (error) {
      console.error('加载历史会话失败:', error);
    }
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 48) {
      return '昨天';
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isHistoryDropdownOpen) {
        setIsHistoryDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isHistoryDropdownOpen]);

  // 加载状态
  if (isLoadingConversation) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在初始化设计工作台...</p>
        </div>
      </div>
    );
  }

  // 始终显示设计工作台界面
  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden">
      {/* Main Content - Full screen without header */}
      <main className="h-full relative overflow-hidden">
        {/* 设计Canvas区域 */}
        <div className="flex-1 relative overflow-hidden">
          <DesignCanvas
            ref={canvasRef}
            className="w-full h-full"
            maxDesigns={20}
            autoArrange={true}
            gridSnap={false}
            onAIEdit={handleAIEdit}
            onPositionChange={handlePositionChange}
          />

          {/* 设计对话浮层 */}
          <div className={`absolute ${isCollapsed ? 'top-6 left-6 w-12 h-12 rounded-full border-0' : 'top-6 left-6 bottom-6 w-96 rounded-3xl border-0'} bg-white shadow-xl flex flex-col z-[1000] backdrop-blur-sm ${isCollapsed ? 'transition-all duration-300 overflow-hidden' : 'overflow-visible'}`}>
            {/* 对话头部 */}
            <div className={`flex items-center justify-between ${isCollapsed ? 'w-full h-full p-0' : 'p-4'} bg-gradient-to-r from-gray-50/80 to-gray-100/80 ${isCollapsed ? 'rounded-full' : 'rounded-t-3xl'} ${isCollapsed ? 'border-0' : 'border-b border-gray-100/50'}`}>
              {!isCollapsed && (
                <h3 className="text-lg font-semibold text-gray-900">设计对话</h3>
              )}
              <div className={`flex items-center ${isCollapsed ? 'w-full h-full justify-center' : 'space-x-2'}`}>
                {isCollapsed ? (
                  <button
                    onClick={handleCollapse}
                    className="w-full h-full flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-full transition-colors"
                    title="展开对话"
                  >
                    <MessageCircle className="h-6 w-6" />
                  </button>
                ) : (
                  <>
                    <button
                      onClick={handleNewSession}
                      className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                      title="新会话"
                    >
                      <div className="w-5 h-5 flex items-center justify-center text-gray-400 hover:text-gray-600 font-bold text-lg">+</div>
                    </button>
                    
                    {/* 历史会话下拉框 */}
                    <div className="relative">
                      <button
                        onClick={() => {
                          setIsHistoryDropdownOpen(!isHistoryDropdownOpen);
                          if (!isHistoryDropdownOpen) {
                            loadConversations();
                          }
                        }}
                        className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                        title="历史会话"
                      >
                        <History className="h-4 w-4" />
                      </button>
                  
                  {isHistoryDropdownOpen && (
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 w-80 bg-white rounded-xl shadow-lg border border-gray-200 z-[10000] max-h-96 overflow-hidden">
                      <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-blue-600" />
                          <h3 className="font-medium text-gray-900">历史会话</h3>
                        </div>
                      </div>
                      
                      <div className="overflow-y-auto max-h-80">
                        {isLoadingConversations ? (
                          <div className="p-8 text-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p className="text-sm text-gray-500">加载中...</p>
                          </div>
                        ) : conversations.length === 0 ? (
                          <div className="p-8 text-center">
                            <History className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                            <p className="text-sm text-gray-500">暂无对话历史</p>
                          </div>
                        ) : (
                          conversations.map((conversation) => (
                            <div
                              key={conversation.id}
                              onClick={() => handleHistoryConversationClick(conversation)}
                              className="p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors"
                            >
                              <div className="flex items-start space-x-3">
                                {/* 预览图片 */}
                                {conversation.previewImage && (
                                  <div className="flex-shrink-0">
                                    <div className="w-12 h-16 rounded-lg overflow-hidden bg-gray-100">
                                      <Image
                                        src={conversation.previewImage}
                                        alt="设计预览"
                                        width={48}
                                        height={64}
                                        className="w-full h-full object-cover"
                                      />
                                    </div>
                                  </div>
                                )}

                                {/* 对话内容 */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-start justify-between mb-1">
                                    <h4 className="text-sm font-medium text-gray-900 truncate">
                                      {conversation.title}
                                    </h4>
                                    <div className="flex items-center space-x-1 text-xs text-gray-500 flex-shrink-0">
                                      <Clock className="h-3 w-3" />
                                      <span>{formatTime(conversation.timestamp)}</span>
                                    </div>
                                  </div>

                                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                                    {conversation.lastMessage}
                                  </p>

                                  <div className="flex items-center space-x-3 text-xs text-gray-500">
                                    <div className="flex items-center space-x-1">
                                      <History className="h-3 w-3" />
                                      <span>{conversation.messageCount}</span>
                                    </div>
                                    {conversation.designCount > 0 && (
                                      <div className="flex items-center space-x-1">
                                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                                        <span>{conversation.designCount}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                      
                      {conversations.length > 0 && (
                        <div className="p-3 border-t border-gray-200 bg-gray-50">
                          <button
                            onClick={() => {
                              setIsHistoryDropdownOpen(false);
                              router.push('/conversations');
                            }}
                            className="w-full text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
                          >
                            查看全部对话
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                
                <button
                  onClick={handleCollapse}
                  className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  title="收起"
                >
                  <ChevronDown className="h-4 w-4" />
                </button>
                </>
              )}
              </div>
            </div>

            {/* 收起时隐藏内容 */}
            {!isCollapsed && (
              <>
                {/* 消息列表 */}
                <div className="flex-1 overflow-y-auto p-4 space-y-3 scrollbar-hide">
                  {messages.map((message) => (
                    <div key={message.id}>
                      {/* 只显示普通消息 */}
                      {message.type !== 'autonomous_progress' && (
                        <div
                          className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-[80%] rounded-2xl px-4 py-3 shadow-sm ${
                              message.role === 'user'
                                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white'
                                : 'bg-white text-gray-900 border border-gray-100'
                            }`}
                          >
                            <p className="text-sm leading-relaxed">{message.content}</p>
                            {message.imageUrl && (
                              <div className="mt-3">
                                <div className="relative w-48 h-64 rounded-xl overflow-hidden shadow-sm">
                                  <Image
                                    src={message.imageUrl}
                                    alt="Generated design"
                                    fill
                                    className="object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjI1NiIgdmlld0JveD0iMCAwIDE5MiAyNTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMjU2IiBmaWxsPSIjRjlGQUZCIiByeD0iMTIiLz4KPHA+CjxwYXRoIGQ9Ik05NiAxMDBIOTZWMTU2SDk2VjEwMFoiIHN0cm9rZT0iI0Q1RDlERCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+Cjx0ZXh0IHg9IjUwJSIgeT0iNjAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTIiPuacjeijheivpuiuoeWbvjwvdGV4dD4KPC9zdmc+';
                                    }}
                                  />
                                </div>
                              </div>
                            )}
                            <div className={`text-xs mt-2 ${message.role === 'user' ? 'text-purple-200' : 'text-gray-400'}`}>
                              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {/* 正在生成指示器 */}
                  {isGenerating && (
                    <div className="flex justify-start">
                      <div className="bg-white border border-gray-100 rounded-2xl px-4 py-3 shadow-sm">
                        <div className="flex items-center space-x-3">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                          <span className="text-sm text-gray-600">正在为您精心设计...</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>

                {/* 编辑图片缩略图 */}
                {editingImage && (
                  <div className="px-4 pt-4">
                    <div className="relative inline-block">
                      <Image
                        src={editingImage.image_url}
                        alt="正在编辑的设计"
                        width={80}
                        height={100}
                        className="rounded-lg shadow-sm object-cover"
                      />
                      <button
                        onClick={handleCancelEdit}
                        className="absolute -top-2 -right-2 bg-gray-800 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-gray-900 transition-colors"
                        title="取消编辑"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}

                {/* 输入区域 */}
                <div className="p-4 bg-gradient-to-r from-gray-50/50 to-gray-100/50 rounded-b-3xl border-t border-gray-100/50">
                  {conversationState === 'confirming' ? (
                    <div className="text-center text-gray-500 py-4">
                      <p>请确认上方的设计需求后继续对话</p>
                    </div>
                  ) : (
                    <div className="flex space-x-3">
                      <textarea
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={
                          conversationState === 'generating'
                            ? "正在生成设计中..."
                            : editingImage
                            ? "请描述您想要的修改，例如：把颜色改成红色、调整布局、添加元素..."
                            : "描述您想要的创意需求..."
                        }
                        className="flex-1 px-4 py-3 border-0 bg-white rounded-2xl focus:ring-2 focus:ring-blue-500 focus:outline-none resize-none shadow-sm transition-all"
                        rows={2}
                        disabled={isGenerating || conversationState === 'generating'}
                      />
                      <button
                        onClick={handleSendMessage}
                        disabled={!inputMessage.trim() || isGenerating || conversationState === 'generating'}
                        className="disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-2xl transition-all shadow-md bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg"
                      >
                        {isGenerating ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Send className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

// 主组件，包装DesignProvider
function DesignerWorkspaceWithRedirect() {
  const searchParams = useSearchParams();
  const [isClient, setIsClient] = useState(false);
  const [conversationId, setConversationId] = useState<string | undefined>(undefined);

  // 确保在客户端运行
  useEffect(() => {
    setIsClient(true);
  }, []);

  const projectIdFromUrl = isClient ? searchParams?.get('project_id') : null;

  // 设置conversationId
  useEffect(() => {
    if (isClient && projectIdFromUrl) {
      console.log('🔍 设置conversationId为project_id:', projectIdFromUrl);
      setConversationId(projectIdFromUrl);
    }
  }, [isClient, projectIdFromUrl]);
  
  // 如果没有project_id参数，直接重定向到首页
  if (isClient && !projectIdFromUrl) {
    console.log('🔄 没有project_id参数，重定向到首页');
    redirect('/');
  }
  
  // 如果还没有初始化，显示loading
  if (!isClient) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    );
  }
  
  // 等待conversationId设置完成
  if (!conversationId) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在初始化...</p>
        </div>
      </div>
    );
  }

  console.log('🔗 传递conversationId到DesignProvider:', conversationId);

  return (
    <DesignProvider conversationId={conversationId}>
      <DesignerWorkspaceInner conversationId={conversationId} />
    </DesignProvider>
  );
}

export default function DesignerWorkspace() {
  return (
    <SearchParamsWrapper>
      <DesignerWorkspaceWithRedirect />
    </SearchParamsWrapper>
  );
}