import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ dimensions: string[] }> }
) {
  const { dimensions } = await params;
  const [width, height] = dimensions;
  
  // 创建一个简单的SVG占位符
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="#F3F4F6"/>
      <rect x="20" y="20" width="${parseInt(width) - 40}" height="${parseInt(height) - 40}" fill="none" stroke="#D1D5DB" stroke-width="2" stroke-dasharray="10,5"/>
      <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="16">
        服装设计图
      </text>
      <text x="50%" y="60%" text-anchor="middle" dominant-baseline="middle" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="12">
        ${width} × ${height}
      </text>
    </svg>
  `;

  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000',
    },
  });
}
