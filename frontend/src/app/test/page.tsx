'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

export default function TestPage() {
  const [conversationId, setConversationId] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const createConversation = async () => {
    try {
      setLoading(true);
      setError('');
      
      const conversation = await apiClient.createConversation({
        title: '测试会话',
        status: 'active',
        meta_data: {
          test: true,
          created_at: new Date().toISOString()
        }
      });
      
      setConversationId(conversation.id);
      setResponse(conversation);
    } catch (err: any) {
      setError(`创建会话失败: ${err.message}`);
      console.error('Create conversation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testConversation = async () => {
    if (!conversationId || !message.trim()) return;

    try {
      setLoading(true);
      setError('');
      
      console.log('🧪 测试对话API:', {
        conversationId,
        message: message.trim()
      });

      const result = await apiClient.processConversation(conversationId, {
        content: message.trim(),
        conversation_id: conversationId,
        message_type: 'user'
      });

      console.log('🎯 API响应:', result);
      setResponse(result);
    } catch (err: any) {
      setError(`对话处理失败: ${err.message}`);
      console.error('Process conversation error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">API测试页面</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">创建会话</h2>
          <button
            onClick={createConversation}
            disabled={loading}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '创建中...' : '创建会话'}
          </button>
          {conversationId && (
            <div className="mt-4">
              <p className="text-green-600">会话ID: {conversationId}</p>
            </div>
          )}
        </div>

        {conversationId && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">测试对话</h2>
            <div className="space-y-4">
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="输入测试消息..."
                className="w-full p-3 border border-gray-300 rounded-md"
                rows={3}
              />
              <button
                onClick={testConversation}
                disabled={loading || !message.trim()}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
              >
                {loading ? '处理中...' : '发送消息'}
              </button>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <h3 className="font-bold">错误:</h3>
            <p>{error}</p>
          </div>
        )}

        {response && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">响应结果</h2>
            <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
              {JSON.stringify(response, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}