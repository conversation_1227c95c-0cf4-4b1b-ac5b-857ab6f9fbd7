'use client'

import React, { useState, useEffect } from 'react'
import { Brain, Cog, CheckCircle, AlertCircle, Sparkles, Loader2 } from 'lucide-react'

interface AutonomousGenerationStatus {
  phase: 'thinking' | 'executing' | 'tool_completed' | 'tool_error' | 'completed'
  action: string
  progress: number
  current_tool?: string
  iteration?: number
  total_tools_called?: number
  tools_used?: number
  tool_result_summary?: string
  error?: string
  timestamp?: string
}

interface AutonomousProgressDisplayProps {
  status: AutonomousGenerationStatus | null
  conversationId: string
}

interface ProcessStep {
  id: string;
  phase: string;
  action: string;
  tool?: string;
  result?: string;
  timestamp: string;
  status: 'completed' | 'current' | 'pending';
}

export const AutonomousProgressDisplay: React.FC<AutonomousProgressDisplayProps> = ({
  status,
  conversationId
}) => {
  const [processSteps, setProcessSteps] = useState<ProcessStep[]>([]);
  const [showCompleted, setShowCompleted] = useState(false);

  // 当状态更新时，添加新的处理步骤
  useEffect(() => {
    if (!status) return;

    const timestamp = status.timestamp || new Date().toISOString();
    const stepId = status.current_tool && status.iteration
      ? `step-${status.current_tool}-${status.iteration}-${Date.now()}`
      : `step-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    setProcessSteps(prev => {
      // 如果是最终完成状态，添加完成步骤
      if (status.phase === 'completed' && status.progress >= 100) {
        const hasCompletedStep = prev.some(step => step.phase === 'completed');
        if (!hasCompletedStep) {
          const completedStep: ProcessStep = {
            id: 'completed-final',
            phase: 'completed',
            action: '🎉 设计生成完成！',
            timestamp,
            status: 'completed'
          };
          setShowCompleted(true);
          return [...prev, completedStep];
        }
        return prev;
      }

      // 对于thinking阶段，避免重复添加
      if (status.phase === 'thinking' && !status.current_tool) {
        const hasThinkingStep = prev.some(step =>
          step.phase === 'thinking' && !step.tool
        );
        if (hasThinkingStep) {
          return prev; // 不添加重复的thinking步骤
        }
      }

      // 处理工具执行状态的特殊逻辑
      if (status.phase === 'executing' && status.current_tool) {
        // 检查是否已经有相同工具和相同iteration的执行步骤
        const existingExecutingIndex = prev.findIndex(step =>
          step.phase === 'executing' &&
          step.tool === status.current_tool &&
          step.status === 'current' &&
          step.id.includes(`${status.current_tool}-${status.iteration}`)
        );

        if (existingExecutingIndex !== -1) {
          // 如果已经有相同工具和iteration的执行步骤，不重复添加
          return prev;
        }
      }

      // 处理工具完成状态 - 添加新的完成步骤，不覆盖执行步骤
      if (status.phase === 'tool_completed' && status.current_tool) {
        // 检查是否已经有相同工具和iteration的完成步骤
        const hasCompletedStep = prev.some(step =>
          step.phase === 'tool_completed' &&
          step.tool === status.current_tool &&
          step.id.includes(`${status.current_tool}-${status.iteration}`)
        );

        if (hasCompletedStep) {
          return prev; // 避免重复添加完成步骤
        }

        // 创建新的完成步骤，不修改现有的执行步骤
        const completedStep: ProcessStep = {
          id: stepId,
          phase: 'tool_completed',
          action: status.action,
          tool: status.current_tool,
          result: status.tool_result_summary,
          timestamp,
          status: 'completed'
        };

        return [...prev, completedStep];
      }

      // 处理工具错误状态 - 添加新的错误步骤，不覆盖执行步骤
      if (status.phase === 'tool_error' && status.current_tool) {
        // 检查是否已经有相同工具和iteration的错误步骤
        const hasErrorStep = prev.some(step =>
          step.phase === 'tool_error' &&
          step.tool === status.current_tool &&
          step.id.includes(`${status.current_tool}-${status.iteration}`)
        );

        if (hasErrorStep) {
          return prev; // 避免重复添加错误步骤
        }

        // 创建新的错误步骤，不修改现有的执行步骤
        const errorStep: ProcessStep = {
          id: stepId,
          phase: 'tool_error',
          action: status.action,
          tool: status.current_tool,
          result: `错误: ${status.error || '工具执行失败'}`,
          timestamp,
          status: 'completed' // 错误也算是完成状态
        };

        return [...prev, errorStep];
      }

      // 创建新步骤
      const newStep: ProcessStep = {
        id: stepId,
        phase: status.phase,
        action: status.action,
        tool: status.current_tool,
        result: status.tool_result_summary,
        timestamp,
        status: status.phase === 'tool_completed' ? 'completed' : 'current'
      };

      // 添加新步骤
      return [...prev, newStep];
    });
  }, [status]);

  if (!status && !showCompleted) {
    return null;
  }

  const getStepIcon = (phase: string, stepStatus: string) => {
    if (phase === 'completed') {
      return <Sparkles className="w-4 h-4 text-green-500" />;
    }
    if (phase === 'tool_completed') {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    if (phase === 'tool_error') {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    if (stepStatus === 'completed') return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (stepStatus === 'current') {
      switch (phase) {
        case 'thinking': return <Brain className="w-4 h-4 text-blue-500 animate-pulse" />;
        case 'executing': return <Cog className="w-4 h-4 text-blue-500 animate-spin" />;
        default: return <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />;
      }
    }
    return <div className="w-4 h-4 rounded-full bg-gray-300" />;
  };

  const getStepColor = (phase: string, stepStatus: string) => {
    if (phase === 'completed') {
      return 'text-green-600 bg-green-50 border-green-200 ring-2 ring-green-200';
    }
    if (phase === 'tool_completed') {
      return 'text-green-600 bg-green-50 border-green-200';
    }
    if (phase === 'tool_error') {
      return 'text-red-600 bg-red-50 border-red-200';
    }
    switch (stepStatus) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'current': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-400 bg-gray-50 border-gray-200';
    }
  };

  const getConnectorColor = (step: ProcessStep, nextStep?: ProcessStep) => {
    // 如果当前步骤已完成，连接线为绿色
    if (step.status === 'completed') return 'bg-green-300';
    // 如果当前步骤正在进行中，连接线为蓝色
    if (step.status === 'current') return 'bg-blue-300';
    // 默认为灰色
    return 'bg-gray-300';
  };

  const getToolDisplayName = (toolName?: string) => {
    const toolNames: Record<string, string> = {
      'generate_optimized_fashion_prompt': '规划设计步骤',
      'generate_fashion_image': '图像生成',
      'evaluate_design_concept': '设计评估',
      'generate_poster_design': '海报设计生成',
      'generate_logo_design': 'Logo设计生成',
      'analyze_design_requirements': '需求分析'
    }
    return toolName ? toolNames[toolName] || toolName : ''
  }

  // 如果没有状态但显示完成状态，使用默认值
  const currentStatus = status || { progress: 100, phase: 'completed', action: '设计生成完成！' };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm">
      <div className="flex items-center gap-2 mb-4">
        <Brain className="w-5 h-5 text-blue-500" />
        <h3 className="font-medium text-gray-800">智能设计生成</h3>
        <div className="ml-auto text-sm text-gray-500">
          {currentStatus.progress}% 完成
        </div>
      </div>

      {/* 链式流程显示 */}
      <div className="space-y-0">
        {processSteps.map((step, index) => (
          <div key={step.id} className="relative pb-3">
            {/* 连接线 - 从图标中心延伸到下一个步骤 */}
            {index < processSteps.length - 1 && (
              <div
                className={`absolute left-4 top-8 w-0.5 bottom-0 ${getConnectorColor(step)}`}
              />
            )}

            {/* 步骤内容 */}
            <div className="flex items-start gap-3">
              {/* 图标 */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border ${getStepColor(step.phase, step.status)} relative z-10`}>
                {getStepIcon(step.phase, step.status)}
              </div>

              {/* 内容 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm text-gray-800">
                    {step.action}
                  </span>
                  {step.status === 'current' && (
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  )}
                </div>

                {step.tool && (
                  <div className="text-xs text-gray-500 mb-1">
                    🔧 {getToolDisplayName(step.tool)}
                  </div>
                )}

                {step.result && (
                  <div className="text-xs text-gray-600 bg-gray-50 rounded px-2 py-1 mb-1">
                    {step.result}
                  </div>
                )}

                <div className="text-xs text-gray-400">
                  {new Date(step.timestamp).toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 总体进度 */}
      <div className="mt-4 pt-3 border-t border-gray-100">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <div className="flex-1 bg-gray-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full transition-all duration-500 ${
                currentStatus.progress >= 100 ? 'bg-green-500' : 'bg-blue-500'
              }`}
              style={{ width: `${currentStatus.progress}%` }}
            />
          </div>
          <span className={`text-xs font-medium ${
            currentStatus.progress >= 100 ? 'text-green-600' : 'text-gray-600'
          }`}>
            {currentStatus.progress}%
            {currentStatus.progress >= 100 && ' 完成'}
          </span>
        </div>


      </div>

      {/* 错误显示 */}
      {status?.error && (
        <div className="mt-3 text-sm text-red-700 bg-red-50 p-2 rounded border border-red-200">
          <AlertCircle className="w-4 h-4 inline mr-1" />
          {status.error}
        </div>
      )}
    </div>
  )
}

// Hook for WebSocket connection and status management
export const useAutonomousGenerationStatus = (conversationId: string) => {
  const [status, setStatus] = useState<AutonomousGenerationStatus | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    console.log('useAutonomousGenerationStatus - conversationId:', conversationId);
    if (!conversationId) {
      console.log('useAutonomousGenerationStatus - No conversationId, skipping WebSocket connection');
      return;
    }

    const wsUrl = `ws://localhost:8000/ws/${conversationId}`
    console.log('useAutonomousGenerationStatus - Connecting to:', wsUrl);
    const ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      setIsConnected(true)
      setError(null)
      console.log(`WebSocket connected for conversation: ${conversationId}`)
    }

    ws.onmessage = (event) => {
      console.log('WebSocket message received:', event.data)
      try {
        const message = JSON.parse(event.data)
        console.log('Parsed WebSocket message:', message)

        if (message.type === 'autonomous_generation_status') {
          console.log('Setting autonomous status:', message.data)
          setStatus(message.data)
        } else if (message.type === 'connection_established') {
          console.log('WebSocket connection established:', message.message)
        } else if (message.type === 'tool_call_update') {
          console.log('Tool call update:', message.data)
        } else if (message.type === 'generation_complete') {
          console.log('Generation complete:', message.data)
          // Clear status when generation is complete
          setTimeout(() => setStatus(null), 3000)
        } else {
          console.log('Unknown WebSocket message type:', message.type)
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error, 'Raw data:', event.data)
      }
    }

    ws.onclose = () => {
      setIsConnected(false)
      console.log('WebSocket disconnected')
    }

    ws.onerror = (error) => {
      setError('WebSocket连接错误')
      setIsConnected(false)
      console.error('WebSocket error:', error)
    }

    return () => {
      ws.close()
    }
  }, [conversationId])

  return {
    status,
    isConnected,
    error,
    clearStatus: () => setStatus(null)
  }
}
