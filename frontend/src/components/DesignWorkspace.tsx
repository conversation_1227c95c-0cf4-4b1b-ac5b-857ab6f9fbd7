'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient, DesignProject } from '@/lib/api';
import { Send, Sparkles, RotateCcw, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';


import DesignCanvas from './DesignCanvas';
import DramaCanvas from './DramaCanvas';
import { DesignProvider, useDesignGeneration, useDesignContext } from '@/contexts/DesignContext';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  imageUrl?: string;
  timestamp: Date;
  type?: 'chat' | 'design_request' | 'design_result';
}

interface DesignRequirements {
  style: string;
  colors: string[];
  category: string;
  materials?: string[];
  occasion?: string;
  target_gender?: string;
  key_features?: string[];
  mood?: string;
}

// 内部组件，使用设计context
function DesignWorkspaceInner() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { handleDesignGenerated } = useDesignGeneration();
  const { clearAllDesigns, canvasRef, saveCanvasState } = useDesignContext();
  const [inputMessage, setInputMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const [currentProject, setCurrentProject] = useState<DesignProject | null>(null);
  const [isLoadingProject, setIsLoadingProject] = useState(true);
  const [conversationState, setConversationState] = useState<'idle' | 'generating'>('idle');

  const [conversationId, setConversationId] = useState<string | undefined>(undefined);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 短剧制作相关状态
  const [isDramaMode, setIsDramaMode] = useState(false);
  const [dramaData, setDramaData] = useState<any>(null);





  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // WebSocket监听设计图实时推送
  useEffect(() => {
    if (!conversationId) {
      console.log('⏳ No conversation ID yet, waiting for backend to provide one');
      return;
    }

    console.log(`🔌 Setting up WebSocket connection for conversation: ${conversationId}`);
    const wsUrl = `ws://localhost:8000/ws/${conversationId}`;

    // 添加小延迟确保后端已经准备好处理这个conversation_id
    let ws: WebSocket | null = null;

    const connectTimer = setTimeout(() => {
      ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log(`✅ WebSocket connected for design updates: ${conversationId}`);
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', message);

          if (message.type === 'design_generated') {
            // 实时添加设计图到画布
            const designData = message.data;
            console.log('🎨 Real-time design generated:', designData);

            handleDesignGenerated({
              image_url: designData.image_url,
              design_prompt: designData.design_prompt,
              agent_type: designData.agent_type,
              task_id: designData.task_id,
              design_id: designData.design_id,
              metadata: designData.metadata
            });
          } else if (message.type === 'conversation_result') {
            // 处理对话结果（AI回复消息）
            const resultData = message.data;
            console.log('💬 Conversation result received:', resultData);

            if (resultData.messages && resultData.messages.length > 0) {
              const newMessages = resultData.messages
                .filter((msg: any) => msg.role === 'assistant')
                .map((msg: any) => ({
                  id: Date.now().toString() + Math.random(),
                  role: 'assistant' as const,
                  content: msg.content,
                  timestamp: new Date(),
                  type: msg.type || 'chat'
                }));

              setMessages(prev => [...prev, ...newMessages]);
            }

            // 如果有设计结果，也添加到画布
            if (resultData.image_url) {
              handleDesignGenerated({
                image_url: resultData.image_url,
                design_prompt: resultData.design_prompt || resultData.optimized_prompt,
                agent_type: 'workflow',
                task_id: 'workflow_task',
                design_id: `design_${Date.now()}`,
                metadata: resultData.metadata || {}
              });
            }

            // 设置生成完成状态
            setIsGenerating(false);
            setConversationState('idle');
          } else if (message.type === 'task_progress') {
            // 处理任务进度更新
            console.log('📊 Task progress update:', message.data);
          } else if (message.type === 'collaboration_status') {
            // 处理协作状态更新
            console.log('🤝 Collaboration status update:', message.data);
          } else if (message.type === 'canvas_state_update') {
            // 处理画布状态更新（来自其他用户）
            console.log('🖼️ Canvas state update from other user:', message.data);
            // 这里可以添加画布状态同步逻辑
          } else if (message.type === 'artifact_added') {
            // 处理新制品添加（来自其他用户）
            console.log('📦 New artifact added by other user:', message.data);
            // 这里可以添加新制品到画布
          } else if (message.type === 'artifact_updated') {
            // 处理制品更新（来自其他用户）
            console.log('🔄 Artifact updated by other user:', message.data);
            // 这里可以更新制品位置和状态
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log(`🔌 WebSocket disconnected for conversation: ${conversationId}`);
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
    }, 100); // 100ms延迟

    return () => {
      clearTimeout(connectTimer);
      if (ws) {
        console.log(`🔌 Closing WebSocket connection for: ${conversationId}`);
        ws.close();
      }
    };
  }, [conversationId, handleDesignGenerated]);

  // 意图识别函数
  const detectIntent = (message: string): 'chat' | 'design_request' => {
    const designKeywords = [
      '设计', '创建', '生成', '制作', '想要', '需要',
      '海报', '标志', 'logo', '服装', '衣服', '裙子', '上衣', '裤子', '外套',
      '风格', '颜色', '材质', '款式', '宣传', '品牌', '视觉'
    ];

    const lowerMessage = message.toLowerCase();
    const hasDesignKeyword = designKeywords.some(keyword =>
      lowerMessage.includes(keyword) || message.includes(keyword)
    );

    // 如果是第一条消息或包含设计关键词，认为是设计请求
    return (messages.length === 0 || hasDesignKeyword) ? 'design_request' : 'chat';
  };





  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize or get current project
  useEffect(() => {
    const initializeProject = async () => {
      // Don't initialize if auth is still loading or user is not authenticated
      if (authLoading || !isAuthenticated) {
        setIsLoadingProject(false);
        return;
      }

      try {
        setIsLoadingProject(true);

        // Try to get existing projects
        const projects = await apiClient.getProjects(0, 1);

        let project: DesignProject;
        if (projects.length > 0) {
          // Use the most recent project
          project = projects[0];
        } else {
          // Create a new project
          project = await apiClient.createProject({
            title: '我的设计项目',
            description: '使用AI创建的服装设计',
            is_public: false,
            tags: ['AI设计'],
          });
        }

        setCurrentProject(project);
        setConversationId(project.id);

        // Load existing chat messages
        if (project.id) {
          const chatMessages = await apiClient.getChatMessages(project.id);
          const formattedMessages: Message[] = chatMessages.map(msg => ({
            id: msg.id,
            role: msg.role,
            content: msg.content,
            imageUrl: msg.image_url,
            timestamp: new Date(msg.created_at),
          }));
          setMessages(formattedMessages);

          // 加载现有的设计概念到canvas
          if (project.design_concepts && project.design_concepts.length > 0) {
            project.design_concepts.forEach(concept => {
              handleDesignGenerated({
                image_url: concept.image_url,
                design_prompt: concept.prompt,
                concept_id: concept.id,
                created_at: concept.created_at
              });
            });
          }

          // 加载历史设计产物到canvas
          try {
            const artifactsResponse = await apiClient.getConversationArtifacts(project.id);
            if (artifactsResponse.success && artifactsResponse.data.artifacts.length > 0) {
              console.log(`🎨 Loading ${artifactsResponse.data.artifacts.length} historical artifacts for conversation ${project.id}`);
              
              artifactsResponse.data.artifacts.forEach(artifact => {
                if (artifact.file_url) {
                  handleDesignGenerated({
                    image_url: artifact.file_url,
                    design_prompt: artifact.metadata?.design_prompt || artifact.artifact_name,
                    agent_type: artifact.metadata?.agent_type,
                    task_id: artifact.task_id,
                    design_id: artifact.id,
                    metadata: {
                      ...artifact.metadata,
                      artifact_type: artifact.artifact_type,
                      created_at: artifact.created_at,
                      is_historical: true
                    }
                  });
                }
              });
            }
          } catch (error) {
            console.error('Failed to load historical artifacts:', error);
          }
        }
      } catch (error) {
        console.error('Failed to initialize project:', error);
      } finally {
        setIsLoadingProject(false);
      }
    };

    initializeProject();
  }, [authLoading, isAuthenticated]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating || !currentProject) return;

    const messageContent = inputMessage;
    setInputMessage('');

    // 检查是否是图片编辑请求
    const isImageEditRequest = editingImage !== null;

    // 如果是图片编辑请求，需要先添加图片消息，再添加用户消息
    if (isImageEditRequest && editingImage) {
      const imageMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: `请编辑这张设计图：`,
        imageUrl: editingImage.image_url,
        timestamp: new Date(),
        type: 'chat'
      };

      const userMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'user',
        content: messageContent,
        timestamp: new Date(),
        type: 'chat'
      };

      setMessages(prev => [...prev, imageMessage, userMessage]);
    } else {
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: messageContent,
        timestamp: new Date(),
        type: 'chat'
      };

      setMessages(prev => [...prev, userMessage]);
    }
    setIsGenerating(true);

    try {
      console.log('=== 发送消息 ===');
      console.log('消息内容:', messageContent);
      console.log('是否为图片编辑请求:', isImageEditRequest);
      console.log('编辑图片信息:', editingImage);

      // 构建请求数据
      const requestData: any = {
        content: messageContent,
        conversation_id: conversationId, // 由后端生成或返回
        message_type: 'user'
      };

      // 如果是图片编辑请求，添加编辑相关信息
      if (isImageEditRequest && editingImage) {
        requestData.edit_image_url = editingImage.image_url;
        requestData.edit_image_id = editingImage.id;
        requestData.message_type = 'image_edit';
        console.log('添加图片编辑参数:', {
          edit_image_url: editingImage.image_url,
          edit_image_id: editingImage.id
        });

        // 立即清除编辑状态，让缩略图消失
        setEditingImage(null);
      }

      // 使用工作流处理所有消息
      const response = await apiClient.processConversation(currentProject.id, requestData);

      console.log('Workflow response:', response); // 调试日志

      // 处理异步响应
      if (response.success && response.data) {
        const responseData = response.data;
        console.log('Response data:', responseData);

        // 立即获取conversation_id并建立WebSocket连接
        if (responseData.conversation_id && responseData.conversation_id !== conversationId) {
          console.log('🔌 Setting conversation ID and establishing WebSocket:', responseData.conversation_id);
          setConversationId(responseData.conversation_id);
        }

        // 如果是异步处理模式，等待WebSocket推送结果
        if (responseData.status === 'processing') {
          console.log('⏳ Conversation processing started, waiting for WebSocket results...');
          console.log('🚫 Skipping synchronous response processing to avoid duplicates');
          // 不需要在这里处理消息，等待WebSocket推送
          return;
        }

        // 兼容同步响应模式（如果有的话）
        console.log('🔄 Processing synchronous response (legacy mode)');
        console.log('🔍 Raw responseData:', responseData);

        // 正确提取工作流数据 - 数据在 responseData.result 中
        const workflowData = responseData.result || responseData;

        // 只有在非异步处理模式下才处理同步响应
        if (workflowData.messages && workflowData.messages.length > 0) {
          const newMessages = workflowData.messages
            .filter((msg: any) => msg.role === 'assistant')
            .map((msg: any) => {
              const message: Message = {
                id: Date.now().toString() + Math.random(),
                role: 'assistant' as const,
                content: msg.content,
                timestamp: new Date(),
                type: msg.type || 'chat'
              };

              // 如果是设计相关的完成状态且有图片，添加图片到消息中
              if (workflowData.current_state === 'completed' && workflowData.image_url) {
                const isDesignIntent = workflowData.intent === 'design_request' ||
                                      workflowData.intent === 'design_modification' ||
                                      workflowData.intent === 'requirement_confirmation' ||
                                      workflowData.intent === 'image_edit';

                if (isDesignIntent) {
                  message.imageUrl = workflowData.image_url;
                  message.type = 'design_result';
                }
              }

              return message;
            });

          console.log('Adding messages:', newMessages); // 调试日志
          setMessages(prev => [...prev, ...newMessages]);
        }

        // 调试：打印接收到的数据
        console.log('🔍 Received workflowData:', workflowData);
        console.log('🔍 Agent type:', workflowData.agent_type);
        console.log('🔍 Production phase:', workflowData.production_phase);
        console.log('🔍 Script concept:', workflowData.script_concept);
        console.log('🔍 Character design:', workflowData.character_design);

        // 检测是否是短剧制作模式
        const isDramaWorkflow = workflowData.agent_type === 'DramaProductionAgent' ||
                               workflowData.production_phase ||
                               workflowData.script_concept ||
                               workflowData.character_design ||
                               workflowData.scene_plan ||
                               workflowData.video_plan ||
                               workflowData.post_production;

        console.log('🎭 Is drama workflow:', isDramaWorkflow);

        if (isDramaWorkflow) {
          console.log('🎬 Switching to drama mode and updating drama data');
          setIsDramaMode(true);

          // 更新短剧数据
          const newDramaData = {
            script_concept: workflowData.script_concept,
            character_design: workflowData.character_design,
            scene_plan: workflowData.scene_plan,
            video_plan: workflowData.video_plan,
            post_production: workflowData.post_production,
            production_phase: workflowData.production_phase,
            current_stage: workflowData.current_stage
          };

          console.log('🎭 Setting drama data:', newDramaData);
          setDramaData(newDramaData);
        }

        // 处理不同的工作流状态
        if (workflowData.current_state === 'design_generation') {
          setConversationState('generating');

          // 如果有设计结果，添加到canvas（设计生成完成时）
          if (workflowData.image_url && !isDramaWorkflow) {
            console.log('Design generation completed, adding to canvas:', workflowData.image_url);
            handleDesignGenerated(workflowData);
            setConversationState('idle'); // 设计完成后回到空闲状态
          }
        } else if (workflowData.current_state === 'completed') {
          setConversationState('idle');

          // 只有在设计相关的意图时才添加设计图到画布，避免普通聊天时重复添加
          const isDesignIntent = workflowData.intent === 'design_request' ||
                                workflowData.intent === 'design_modification' ||
                                workflowData.intent === 'requirement_confirmation' ||
                                workflowData.intent === 'image_edit';

          if (workflowData.image_url && isDesignIntent && !isDramaWorkflow) {
            console.log('Workflow completed with design result, adding to canvas:', workflowData.image_url);
            handleDesignGenerated(workflowData);
          }
        }
      } else {
        console.error('Workflow response failed:', response); // 调试日志
        // 处理错误响应
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '抱歉，处理您的消息时出现了问题，请稍后再试。',
          timestamp: new Date(),
          type: 'chat'
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '抱歉，发生了错误。请稍后重试。',
        timestamp: new Date(),
        type: 'chat'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };



  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 当前编辑的图片状态
  const [editingImage, setEditingImage] = useState<any>(null);

  // 处理AI编辑
  const handleAIEdit = (design: any) => {
    console.log('=== 开始AI编辑流程 ===');
    console.log('设计信息:', design);

    // 设置当前正在编辑的图片（不立即加入消息历史）
    setEditingImage(design);

    // 清空输入框，让用户输入修改要求
    setInputMessage('');

    console.log('编辑状态已设置，等待用户输入修改要求...');
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingImage(null);
    setInputMessage('');
    console.log('已取消图片编辑');
  };

  // 防抖保存的ref
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 位置变更处理（带防抖）
  const handlePositionChange = (designs: any[]) => {
    console.log('🔄 设计位置已变更，准备保存状态');
    console.log('📊 当前设计数据:', designs.map(d => ({ id: d.id, position: d.position })));

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 防抖保存：200ms后执行保存（拖拽结束后快速保存）
    saveTimeoutRef.current = setTimeout(() => {
      console.log('💾 执行防抖保存');
      const viewState = canvasRef.current?.getViewState() || { scale: 1, offset_x: 0, offset_y: 0 };
      saveCanvasState(designs, viewState);
    }, 200);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);



  const handleReset = () => {
    setMessages([]);
    clearAllDesigns();
    setInputMessage('');
  };

  // 加载状态
  if (isLoadingProject) {
    return (
      <div className="h-[calc(100vh-140px)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在初始化设计工作台...</p>
        </div>
      </div>
    );
  }

  // 初始状态 - 没有对话时显示
  if (messages.length === 0) {
    return (
      <div className="h-[calc(100vh-64px)] flex items-center justify-center">
        <div className="max-w-2xl mx-auto text-center">
          {/* 欢迎界面 */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              告诉我您想要什么样的设计
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              用自然语言描述您的创意需求，我将与您一起创造独特的设计作品
            </p>
          </div>

          {/* 输入区域 */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <div className="space-y-4">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="例如：我想要一张现代科技风格的海报，蓝白配色，用于产品发布会..."
                className="w-full h-24 px-4 py-3 border-0 bg-gray-50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:outline-none focus:bg-white resize-none text-base transition-all"
                disabled={isGenerating}
              />

              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isGenerating}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white py-3 px-6 rounded-xl transition-all flex items-center justify-center space-x-2 shadow-sm"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>生成中...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5" />
                    <span>开始设计</span>
                  </>
                )}
              </button>
            </div>

            {/* 示例提示 */}
            <div className="mt-6">
              <p className="text-sm text-gray-500 mb-3">试试这些示例：</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {[
                  '设计一张科技海报',
                  '创建品牌Logo',
                  '设计一件连衣裙',
                  '制作宣传海报',
                  '设计商务名片'
                ].map((example) => (
                  <button
                    key={example}
                    onClick={() => setInputMessage(example)}
                    className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg text-sm transition-colors border border-gray-200 hover:border-gray-300"
                  >
                    {example}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 对话界面 - 有对话时显示
  return (
    <div className="h-full w-full flex flex-col overflow-hidden">
      {/* 设计Canvas区域 - 延伸到最左边 */}
      <div className="flex-1 relative overflow-hidden">
        {isDramaMode ? (
          <DramaCanvas
            dramaData={dramaData}
            className="w-full h-full"
            onArtifactSelect={(artifactId) => {
              console.log('Drama artifact selected:', artifactId);
            }}
            onArtifactDelete={(artifactId) => {
              console.log('Drama artifact deleted:', artifactId);
            }}
          />
        ) : (
          <DesignCanvas
            ref={canvasRef}
            className="w-full h-full"
            maxDesigns={20}
            autoArrange={true}
            gridSnap={false}
            onAIEdit={handleAIEdit}
            onPositionChange={handlePositionChange}
          />
        )}

        {/* 设计对话浮层 - 在画布上面，靠左显示，延伸到底部 */}
        <div className="absolute top-6 left-6 bottom-6 w-80 bg-white rounded-3xl shadow-xl border border-gray-100 flex flex-col overflow-hidden z-10 backdrop-blur-sm">
          {/* 对话头部 */}
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/80 to-gray-100/80 rounded-t-3xl border-b border-gray-100/50">
            <h3 className="text-lg font-semibold text-gray-900">设计对话</h3>
            <button
              onClick={handleReset}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              title="重新开始"
            >
              <RotateCcw className="h-4 w-4" />
            </button>
          </div>

          {/* 消息列表 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3 scrollbar-hide">




          {messages.map((message) => (
            <div key={message.id}>
              {/* 只显示普通消息 */}
              {message.type !== 'autonomous_progress' && (
                <div
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-2xl px-4 py-3 shadow-sm ${
                      message.role === 'user'
                        ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white'
                        : 'bg-white text-gray-900 border border-gray-100'
                    }`}
                  >
                    <p className="text-sm leading-relaxed">{message.content}</p>
                    {message.imageUrl && (
                      <div className="mt-3">
                        <div className="relative w-48 h-64 rounded-xl overflow-hidden shadow-sm">
                          <Image
                            src={message.imageUrl}
                            alt="Generated design"
                            fill
                            className="object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjI1NiIgdmlld0JveD0iMCAwIDE5MiAyNTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMjU2IiBmaWxsPSIjRjlGQUZCIiByeD0iMTIiLz4KPHA+CjxwYXRoIGQ9Ik05NiAxMDBIOTZWMTU2SDk2VjEwMFoiIHN0cm9rZT0iI0Q1RDlERCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+Cjx0ZXh0IHg9IjUwJSIgeT0iNjAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTIiPuacjeijheivpuiuoeWbvjwvdGV4dD4KPC9zdmc+';
                            }}
                          />
                        </div>
                      </div>
                    )}
                    <div className={`text-xs mt-2 ${message.role === 'user' ? 'text-purple-200' : 'text-gray-400'}`}>
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}

          {/* 正在生成指示器 */}
          {isGenerating && (
            <div className="flex justify-start">
              <div className="bg-white border border-gray-100 rounded-2xl px-4 py-3 shadow-sm">
                <div className="flex items-center space-x-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-sm text-gray-600">正在为您精心设计...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>



        {/* 编辑图片缩略图 */}
        {editingImage && (
          <div className="px-4 pt-4">
            <div className="relative inline-block">
              <Image
                src={editingImage.image_url}
                alt="正在编辑的设计"
                width={80}
                height={100}
                className="rounded-lg shadow-sm object-cover"
              />
              <button
                onClick={handleCancelEdit}
                className="absolute -top-2 -right-2 bg-gray-800 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-gray-900 transition-colors"
                title="取消编辑"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* 输入区域 */}
        <div className="p-4 bg-gradient-to-r from-gray-50/50 to-gray-100/50 rounded-b-3xl border-t border-gray-100/50">
          {conversationState === 'confirming' ? (
            <div className="text-center text-gray-500 py-4">
              <p>请确认上方的设计需求后继续对话</p>
            </div>
          ) : (
            <div className="flex space-x-3">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  conversationState === 'generating'
                    ? "正在生成设计中..."
                    : editingImage
                    ? "请描述您想要的修改，例如：把颜色改成红色、调整布局、添加元素..."
                    : "描述您想要的创意需求..."
                }
                className="flex-1 px-4 py-3 border-0 bg-white rounded-2xl focus:ring-2 focus:ring-blue-500 focus:outline-none resize-none shadow-sm transition-all"
                rows={2}
                disabled={isGenerating || conversationState === 'generating'}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isGenerating || conversationState === 'generating'}
                className="disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-2xl transition-all shadow-md bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg"
              >
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </button>
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}

// 主组件，包装DesignProvider（已废弃，使用/app/designer/page.tsx代替）
export default function DesignWorkspace_DEPRECATED() {
  const searchParams = useSearchParams();
  const [conversationId, setConversationId] = useState<string | undefined>(undefined);
  const [isClient, setIsClient] = useState(false);

  // 确保在客户端运行
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 从URL参数获取project_id作为conversationId
  useEffect(() => {
    if (!isClient) return;

    // 从URL参数中获取project_id
    let id = searchParams?.get('project_id');

    console.log('🔍 从URL获取project_id:', id);

    // 如果URL中没有project_id，生成一个默认的用于测试
    if (!id) {
      id = 'default-project-' + Date.now();
      console.log('🆔 生成默认project_id用于测试:', id);
    }

    console.log('✅ 设置conversationId为:', id);
    setConversationId(id);
  }, [isClient, searchParams]);

  console.log('🔗 DesignWorkspace传递conversationId到DesignProvider:', conversationId);

  // 等待conversationId设置完成再渲染DesignProvider
  if (!conversationId) {
    console.log('⏳ 等待conversationId设置...');
    return <div>Loading...</div>;
  }

  return (
    <DesignProvider conversationId={conversationId}>
      <DesignWorkspaceInner />
    </DesignProvider>
  );
}
