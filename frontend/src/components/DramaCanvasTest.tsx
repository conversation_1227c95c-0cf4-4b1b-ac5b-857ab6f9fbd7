'use client';

import React, { useState } from 'react';
import DramaCanvas from './DramaCanvas';

// 模拟短剧制作数据
const mockDramaData = {
  script_concept: {
    script: `### 剧本标题：《家务大战》

### 故事梗概
在繁华都市的一间小公寓里，两个性格迥异的室友李明和张浩因为家务分配问题闹得不可开交。李明是个爱干净的完美主义者，而张浩则是个大大咧咧的懒汉。一次偶然的机会，他们决定通过一场"家务挑战"来决定谁来负责哪部分家务。结果出人意料，两人都在过程中发现了对方的优点，最终握手言欢，共同承担起家务，加深了彼此之间的友谊。

### 主要角色介绍
1. **李明**：22岁，高瘦，戴眼镜，穿着整洁。对生活有很高的要求，尤其是卫生方面。
2. **张浩**：23岁，圆脸，微胖，留着小胡子，穿着随意。性格开朗，大大咧咧。
3. **小猫"豆豆"**：一只黑白相间的小猫，活泼可爱。`,
    storyboard: {
      shots: [
        {
          shot_id: "Shot_01",
          scene_location: "公寓客厅",
          scene_time: "白天",
          characters: ["李明", "张浩"],
          shot_type: "全景",
          camera_movement: "固定",
          duration: 10,
          composition: "客厅内杂乱无章，李明坐在沙发上，张浩躺在沙发上玩手机",
          dialogue: "李明：张浩，你看看这屋子里，简直就像垃圾场一样！",
          emotion: "紧张",
          action_description: "李明皱眉看着张浩"
        },
        {
          shot_id: "Shot_02",
          scene_location: "公寓客厅",
          scene_time: "白天",
          characters: ["李明", "张浩"],
          shot_type: "中景",
          camera_movement: "固定",
          duration: 8,
          composition: "李明转向张浩，表情严肃",
          dialogue: "张浩：哎呀，别那么认真嘛，等会儿我再收拾。",
          emotion: "轻松",
          action_description: "张浩继续玩手机"
        },
        {
          shot_id: "Shot_03",
          scene_location: "公寓厨房",
          scene_time: "白天",
          characters: ["李明", "张浩"],
          shot_type: "中景",
          camera_movement: "固定",
          duration: 12,
          composition: "两人站在厨房中央，准备开始家务挑战",
          dialogue: "李明：这样吧，我们来个家务挑战！",
          emotion: "激动",
          action_description: "两人面对面站立"
        }
      ],
      parsed: true,
      total_duration: 30,
      shot_count: 3
    }
  },
  character_design: {
    character_descriptions: {
      characters: [
        {
          name: "李明",
          age: 22,
          gender: "男",
          personality: "内向，注重细节，对生活有较高的要求",
          appearance: "高瘦，身高约180cm，戴着圆形黑框眼镜",
          clothing: "整洁的白衬衫和蓝色牛仔裤",
          role_in_story: "推动矛盾发展，和解的关键人物"
        },
        {
          name: "张浩",
          age: 23,
          gender: "男",
          personality: "开朗，大大咧咧，不太注意个人卫生",
          appearance: "圆脸，微胖，身高约175cm，留着小胡子",
          clothing: "休闲T恤和运动裤",
          role_in_story: "矛盾的另一方，最终学会理解"
        },
        {
          name: "小猫豆豆",
          age: 2,
          gender: "未知",
          personality: "活泼可爱，偶尔调皮",
          appearance: "黑白相间的小猫，毛茸茸的",
          clothing: "无",
          role_in_story: "调节气氛，增加趣味性"
        }
      ],
      parsed: true
    },
    character_images: [
      {
        character_name: "李明",
        image_url: "https://example.com/images/liming.jpg",
        prompt: "A 22 year old man, tall and slim, wearing glasses, white shirt and jeans",
        metadata: {
          generation_time: 2.5,
          model: "stable-diffusion",
          provider: "dashscope"
        }
      },
      {
        character_name: "张浩",
        image_url: "https://example.com/images/zhanghao.jpg",
        prompt: "A 23 year old man, round face, slightly chubby, wearing casual t-shirt",
        metadata: {
          generation_time: 2.3,
          model: "stable-diffusion",
          provider: "dashscope"
        }
      }
    ]
  },
  scene_plan: {
    scene_descriptions: {
      scenes: [
        {
          shot_id: "Shot_01",
          scene_location: "公寓客厅",
          scene_time: "白天",
          environment_description: "客厅光线柔和，透过窗户洒进来的自然光与室内灯光相结合",
          props_needed: ["沙发", "茶几", "散落的衣物", "手机"],
          lighting_setup: "自然光和室内柔和灯光",
          mood_atmosphere: "平静而略带紧张"
        },
        {
          shot_id: "Shot_02",
          scene_location: "公寓客厅",
          scene_time: "白天",
          environment_description: "环境与前一镜头相同，李明的表情更加严肃",
          props_needed: ["沙发", "茶几", "手机"],
          lighting_setup: "继续保持自然光和室内柔和灯光",
          mood_atmosphere: "紧张"
        },
        {
          shot_id: "Shot_03",
          scene_location: "公寓厨房",
          scene_time: "白天",
          environment_description: "厨房整洁，准备进行家务挑战",
          props_needed: ["厨房用具", "洗碗池", "扫帚"],
          lighting_setup: "明亮的厨房灯光",
          mood_atmosphere: "激动"
        }
      ],
      parsed: true
    },
    scene_images: [
      {
        shot_id: "Shot_01",
        scene_location: "公寓客厅",
        image_url: "https://example.com/images/scene_01.jpg",
        prompt: "Living room scene with two young men, one sitting seriously, one lying casually with phone",
        metadata: {
          generation_time: 3.2,
          model: "stable-diffusion",
          provider: "dashscope"
        }
      },
      {
        shot_id: "Shot_02",
        scene_location: "公寓客厅",
        image_url: "https://example.com/images/scene_02.jpg",
        prompt: "Same living room, man with glasses looking serious at his roommate",
        metadata: {
          generation_time: 3.1,
          model: "stable-diffusion",
          provider: "dashscope"
        }
      },
      {
        shot_id: "Shot_03",
        scene_location: "公寓厨房",
        image_url: "https://example.com/images/scene_03.jpg",
        prompt: "Kitchen scene with two men standing face to face, ready for challenge",
        metadata: {
          generation_time: 3.5,
          model: "stable-diffusion",
          provider: "dashscope"
        }
      }
    ]
  },
  video_plan: {
    video_segments: [
      {
        shot_id: "Shot_01",
        video_url: "https://example.com/videos/shot_01.mp4",
        duration: 10,
        motion_type: "静态",
        source_image: "https://example.com/images/scene_01.jpg",
        metadata: {
          generation_time: 15.2,
          model: "runway-ml",
          provider: "runway"
        }
      },
      {
        shot_id: "Shot_02",
        video_url: "https://example.com/videos/shot_02.mp4",
        duration: 8,
        motion_type: "缓慢移动",
        source_image: "https://example.com/images/scene_02.jpg",
        metadata: {
          generation_time: 14.8,
          model: "runway-ml",
          provider: "runway"
        }
      },
      {
        shot_id: "Shot_03",
        video_url: "https://example.com/videos/shot_03.mp4",
        duration: 12,
        motion_type: "快速移动",
        source_image: "https://example.com/images/scene_03.jpg",
        metadata: {
          generation_time: 16.1,
          model: "runway-ml",
          provider: "runway"
        }
      }
    ]
  },
  post_production: {
    production_results: {
      voiceover: {
        audio_url: "https://example.com/audio/voiceover.mp3",
        duration: 30,
        metadata: {
          generation_time: 5.2,
          model: "azure-tts",
          provider: "azure"
        }
      },
      background_music: {
        audio_url: "https://example.com/audio/background_music.mp3",
        duration: 30,
        metadata: {
          generation_time: 8.1,
          model: "suno-ai",
          provider: "suno"
        }
      },
      audio_mix: {
        audio_url: "https://example.com/audio/mixed_audio.mp3",
        duration: 30,
        metadata: {
          mixing_time: 2.0,
          method: "ffmpeg",
          status: "success"
        }
      },
      final_drama: {
        video_url: "https://example.com/videos/final_drama.mp4",
        duration: 30,
        metadata: {
          merging_time: 5.5,
          method: "ffmpeg",
          status: "success",
          final_format: "MP4",
          resolution: "1920x1080"
        }
      }
    }
  }
};

const DramaCanvasTest: React.FC = () => {
  const [selectedArtifact, setSelectedArtifact] = useState<string | null>(null);

  return (
    <div className="w-full h-screen bg-gray-50">
      <div className="h-full flex flex-col">
        {/* 标题栏 */}
        <div className="bg-white shadow-sm border-b p-4">
          <h1 className="text-2xl font-bold text-gray-900">短剧制作画布测试</h1>
          <p className="text-gray-600 mt-1">展示短剧制作过程中的所有产物</p>
        </div>

        {/* 画布区域 */}
        <div className="flex-1">
          <DramaCanvas
            dramaData={mockDramaData}
            onArtifactSelect={(artifactId) => {
              console.log('Selected artifact:', artifactId);
              setSelectedArtifact(artifactId);
            }}
            onArtifactDelete={(artifactId) => {
              console.log('Deleted artifact:', artifactId);
            }}
          />
        </div>

        {/* 状态栏 */}
        <div className="bg-white border-t p-3">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div>
              选中产物: {selectedArtifact || '无'}
            </div>
            <div className="flex space-x-4">
              <span>剧本: ✅</span>
              <span>角色: ✅ (2张图片)</span>
              <span>场景: ✅ (3张图片)</span>
              <span>视频: ✅ (3个片段)</span>
              <span>音频: ✅ (配音+音乐)</span>
              <span>最终: ✅</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DramaCanvasTest;
