'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { MessageSquare, Calendar, Image as ImageIcon, ChevronDown, History } from 'lucide-react';
import Image from 'next/image';

interface ConversationItem {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  designCount: number;
  previewImage?: string;
}

interface ConversationDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
}

export default function ConversationDropdown({ isOpen, onToggle }: ConversationDropdownProps) {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [conversations, setConversations] = useState<ConversationItem[]>([]);
  const [isLoadingConversations, setIsLoadingConversations] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 加载对话历史
  useEffect(() => {
    if (!isLoading && isAuthenticated && isOpen) {
      loadConversations();
    }
  }, [isLoading, isAuthenticated, isOpen]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onToggle();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onToggle]);

  const loadConversations = async () => {
    try {
      setIsLoadingConversations(true);
      
      // 获取所有会话，后端已经通过SQL多表关联查询优化了性能
      const conversations = await apiClient.getConversations(0, 20);
      
      // 直接使用后端返回的数据，无需额外查询
      const optimizedConversations = conversations.map((conversation) => {
        // 从meta_data中获取预计算的数据
        const metaData = conversation.meta_data || {};
        const messageCount = metaData.message_count || 0;
        const designCount = metaData.design_count || 0;
        const previewImage = metaData.preview_image;
        const lastMessage = metaData.last_message || '新会话';
        
        return {
          id: conversation.id,
          title: conversation.title,
          lastMessage: lastMessage,
          timestamp: new Date(conversation.updated_at),
          messageCount: messageCount,
          designCount: designCount,
          previewImage: previewImage
        };
      });
      
      // 按时间倒序排列，只显示最近10个
      optimizedConversations.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      setConversations(optimizedConversations.slice(0, 10));
    } catch (error) {
      console.error('Failed to load conversations:', error);
    } finally {
      setIsLoadingConversations(false);
    }
  };

  // 点击对话项，跳转到设计器页面并恢复对话
  const handleConversationClick = (conversation: ConversationItem) => {
    onToggle(); // 关闭下拉菜单
    router.push(`/designer?project_id=${conversation.id}`);
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 48) {
      return '昨天';
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      ref={dropdownRef}
      className="absolute top-full right-0 mt-2 w-96 bg-white rounded-xl shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden"
    >
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <History className="h-4 w-4 text-blue-600" />
          <h3 className="font-medium text-gray-900">最近对话</h3>
        </div>
      </div>
      
      <div className="overflow-y-auto max-h-80">
        {isLoadingConversations ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">加载中...</p>
          </div>
        ) : conversations.length === 0 ? (
          <div className="p-8 text-center">
            <MessageSquare className="h-8 w-8 text-gray-300 mx-auto mb-2" />
            <p className="text-sm text-gray-500">暂无对话历史</p>
          </div>
        ) : (
          conversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => handleConversationClick(conversation)}
              className="p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors"
            >
              <div className="flex items-start space-x-3">
                {/* 预览图片 */}
                {conversation.previewImage && (
                  <div className="flex-shrink-0">
                    <div className="w-12 h-16 rounded-lg overflow-hidden bg-gray-100">
                      <Image
                        src={conversation.previewImage}
                        alt="设计预览"
                        width={48}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}

                {/* 对话内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {conversation.title}
                    </h4>
                    <div className="flex items-center space-x-1 text-xs text-gray-500 flex-shrink-0">
                      <Calendar className="h-3 w-3" />
                      <span>{formatTime(conversation.timestamp)}</span>
                    </div>
                  </div>

                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                    {conversation.lastMessage}
                  </p>

                  <div className="flex items-center space-x-3 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <MessageSquare className="h-3 w-3" />
                      <span>{conversation.messageCount}</span>
                    </div>
                    {conversation.designCount > 0 && (
                      <div className="flex items-center space-x-1">
                        <ImageIcon className="h-3 w-3" />
                        <span>{conversation.designCount}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      
      {conversations.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => {
              onToggle();
              router.push('/conversations');
            }}
            className="w-full text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
          >
            查看全部对话
          </button>
        </div>
      )}
    </div>
  );
}