'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Heart, Download, Eye, Edit, Trash2, Calendar, Tag, Palette } from 'lucide-react';
import { DesignCategory } from '@/types';

interface DesignItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  category: DesignCategory;
  tags: string[];
  colors: string[];
  createdAt: Date;
  isLiked: boolean;
  views: number;
}

interface DesignGalleryProps {
  viewMode: 'grid' | 'list';
  searchQuery: string;
  showFilters: boolean;
  onCloseFilters: () => void;
}

// 模拟数据
const mockDesigns: DesignItem[] = [
  {
    id: '1',
    title: '科技产品海报',
    description: '现代科技风格的产品宣传海报，蓝白配色',
    imageUrl: '/api/placeholder/400/600',
    category: DesignCategory.POSTER,
    tags: ['科技', '产品', '现代'],
    colors: ['#2563EB', '#FFFFFF'],
    createdAt: new Date('2024-01-15'),
    isLiked: true,
    views: 128,
  },
  {
    id: '2',
    title: '企业品牌Logo',
    description: '简约现代的企业品牌标识设计',
    imageUrl: '/api/placeholder/400/600',
    category: DesignCategory.LOGO,
    tags: ['品牌', 'Logo', '企业'],
    colors: ['#2C3E50', '#FFFFFF'],
    createdAt: new Date('2024-01-10'),
    isLiked: false,
    views: 89,
  },
  {
    id: '3',
    title: '活动宣传海报',
    description: '充满活力的活动宣传海报设计',
    imageUrl: '/api/placeholder/400/600',
    category: DesignCategory.POSTER,
    tags: ['活动', '宣传', '活力'],
    colors: ['#FF6B6B', '#4ECDC4'],
    createdAt: new Date('2024-01-08'),
    isLiked: true,
    views: 156,
  },
  {
    id: '4',
    title: '商务名片设计',
    description: '专业的商务名片设计，简约大方',
    imageUrl: '/api/placeholder/400/600',
    category: DesignCategory.BUSINESS_CARD,
    tags: ['商务', '名片', '专业'],
    colors: ['#8B0000', '#FFD700'],
    createdAt: new Date('2024-01-05'),
    isLiked: false,
    views: 203,
  },
  {
    id: '5',
    title: '夏日清新连衣裙',
    description: '适合约会的优雅连衣裙，淡蓝色设计',
    imageUrl: '/api/placeholder/400/600',
    category: DesignCategory.DRESS,
    tags: ['夏季', '约会', '优雅'],
    colors: ['#87CEEB', '#FFFFFF'],
    createdAt: new Date('2024-01-03'),
    isLiked: true,
    views: 174,
  },
  {
    id: '6',
    title: '品牌横幅设计',
    description: '现代简约的品牌横幅，视觉冲击力强',
    imageUrl: '/api/placeholder/400/600',
    category: DesignCategory.BANNER,
    tags: ['品牌', '横幅', '现代'],
    colors: ['#6366F1', '#EC4899'],
    createdAt: new Date('2024-01-01'),
    isLiked: false,
    views: 92,
  },
];

const categories = [
  { value: 'all', label: '全部类别' },
  { value: DesignCategory.POSTER, label: '海报设计' },
  { value: DesignCategory.LOGO, label: 'Logo设计' },
  { value: DesignCategory.BANNER, label: '横幅设计' },
  { value: DesignCategory.BUSINESS_CARD, label: '名片设计' },
  { value: DesignCategory.DRESS, label: '服装设计' },
];

const sortOptions = [
  { value: 'newest', label: '最新创建' },
  { value: 'oldest', label: '最早创建' },
  { value: 'most_viewed', label: '最多浏览' },
  { value: 'most_liked', label: '最多喜欢' },
];

export default function DesignGallery({ viewMode, searchQuery, showFilters, onCloseFilters }: DesignGalleryProps) {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // 获取所有标签
  const allTags = useMemo(() => {
    const tags = new Set<string>();
    mockDesigns.forEach(design => {
      design.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }, []);

  // 筛选和排序设计
  const filteredDesigns = useMemo(() => {
    let filtered = mockDesigns.filter(design => {
      // 搜索筛选
      if (searchQuery && !design.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !design.description.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // 类别筛选
      if (selectedCategory !== 'all' && design.category !== selectedCategory) {
        return false;
      }

      // 标签筛选
      if (selectedTags.length > 0 && !selectedTags.some(tag => design.tags.includes(tag))) {
        return false;
      }

      return true;
    });

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return b.createdAt.getTime() - a.createdAt.getTime();
        case 'oldest':
          return a.createdAt.getTime() - b.createdAt.getTime();
        case 'most_viewed':
          return b.views - a.views;
        case 'most_liked':
          return (b.isLiked ? 1 : 0) - (a.isLiked ? 1 : 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchQuery, selectedCategory, selectedTags, sortBy]);

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const handleDesignClick = (designId: string) => {
    // TODO: 导航到设计详情页面或编辑页面
    console.log('View design:', designId);
  };

  const handleLikeToggle = (designId: string) => {
    // TODO: 切换喜欢状态
    console.log('Toggle like:', designId);
  };

  const handleDownload = (designId: string) => {
    // TODO: 下载设计
    console.log('Download design:', designId);
  };

  const handleEdit = (designId: string) => {
    // TODO: 编辑设计
    router.push(`/?edit=${designId}`);
  };

  const handleDelete = (designId: string) => {
    // TODO: 删除设计
    console.log('Delete design:', designId);
  };

  return (
    <div className="space-y-6">
      {/* 筛选面板 */}
      {showFilters && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">筛选条件</h3>
            <button
              onClick={onCloseFilters}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* 类别筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">类别</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* 排序 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">排序</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* 标签筛选 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">标签</label>
              <div className="flex flex-wrap gap-2">
                {allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1 text-sm rounded-full transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 结果统计 */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          找到 <span className="font-semibold text-gray-900">{filteredDesigns.length}</span> 个设计作品
        </p>
      </div>

      {/* 设计网格/列表 */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredDesigns.map(design => (
            <DesignCard
              key={design.id}
              design={design}
              onView={() => handleDesignClick(design.id)}
              onLike={() => handleLikeToggle(design.id)}
              onDownload={() => handleDownload(design.id)}
              onEdit={() => handleEdit(design.id)}
              onDelete={() => handleDelete(design.id)}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredDesigns.map(design => (
            <DesignListItem
              key={design.id}
              design={design}
              onView={() => handleDesignClick(design.id)}
              onLike={() => handleLikeToggle(design.id)}
              onDownload={() => handleDownload(design.id)}
              onEdit={() => handleEdit(design.id)}
              onDelete={() => handleDelete(design.id)}
            />
          ))}
        </div>
      )}

      {/* 空状态 */}
      {filteredDesigns.length === 0 && (
        <div className="text-center py-12">
          <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Palette className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">没有找到设计作品</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || selectedCategory !== 'all' || selectedTags.length > 0
              ? '尝试调整筛选条件或搜索关键词'
              : '开始创建您的第一个设计作品吧！'
            }
          </p>
          <button
            onClick={() => router.push('/')}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all"
          >
            开始设计
          </button>
        </div>
      )}
    </div>
  );
}

// 设计卡片组件
function DesignCard({ design, onView, onLike, onDownload, onEdit, onDelete }: {
  design: DesignItem;
  onView: () => void;
  onLike: () => void;
  onDownload: () => void;
  onEdit: () => void;
  onDelete: () => void;
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-lg transition-all group">
      {/* 图片区域 */}
      <div className="relative aspect-[3/4] bg-gray-100">
        <Image
          src={design.imageUrl}
          alt={design.title}
          fill
          className="object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjYwMCIgdmlld0JveD0iMCAwIDQwMCA2MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNjAwIiBmaWxsPSIjRjlGQUZCIiByeD0iMTIiLz4KPHA+CjxwYXRoIGQ9Ik0yMDAgMjUwSDIwMFYzNTBIMjAwVjI1MFoiIHN0cm9rZT0iI0Q1RDlERCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+Cjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiPuacjeijheivpuiuoeWbvjwvdGV4dD4KPC9zdmc+';
          }}
        />
        
        {/* 悬停操作按钮 */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
          <button
            onClick={onView}
            className="bg-white/90 hover:bg-white p-2 rounded-full transition-colors"
            title="查看详情"
          >
            <Eye className="h-4 w-4 text-gray-700" />
          </button>
          <button
            onClick={onEdit}
            className="bg-white/90 hover:bg-white p-2 rounded-full transition-colors"
            title="编辑"
          >
            <Edit className="h-4 w-4 text-gray-700" />
          </button>
          <button
            onClick={onDownload}
            className="bg-white/90 hover:bg-white p-2 rounded-full transition-colors"
            title="下载"
          >
            <Download className="h-4 w-4 text-gray-700" />
          </button>
        </div>

        {/* 喜欢按钮 */}
        <button
          onClick={onLike}
          className="absolute top-3 right-3 bg-white/90 hover:bg-white p-2 rounded-full transition-colors"
        >
          <Heart className={`h-4 w-4 ${design.isLiked ? 'text-red-500 fill-current' : 'text-gray-700'}`} />
        </button>
      </div>

      {/* 信息区域 */}
      <div className="p-4 space-y-3">
        <div>
          <h3 className="font-semibold text-gray-900 line-clamp-1">{design.title}</h3>
          <p className="text-sm text-gray-600 line-clamp-2 mt-1">{design.description}</p>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-1">
          {design.tags.slice(0, 2).map(tag => (
            <span
              key={tag}
              className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
            >
              {tag}
            </span>
          ))}
          {design.tags.length > 2 && (
            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
              +{design.tags.length - 2}
            </span>
          )}
        </div>

        {/* 底部信息 */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <Calendar className="h-3 w-3" />
            <span>{design.createdAt.toLocaleDateString()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Eye className="h-3 w-3" />
            <span>{design.views}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// 设计列表项组件
function DesignListItem({ design, onView, onLike, onDownload, onEdit, onDelete }: {
  design: DesignItem;
  onView: () => void;
  onLike: () => void;
  onDownload: () => void;
  onEdit: () => void;
  onDelete: () => void;
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-lg transition-all">
      <div className="flex space-x-4">
        {/* 缩略图 */}
        <div className="relative w-24 h-32 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
          <Image
            src={design.imageUrl}
            alt={design.title}
            fill
            className="object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iMTI4IiB2aWV3Qm94PSIwIDAgOTYgMTI4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iOTYiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjlGQUZCIiByeD0iOCIvPgo8cGF0aCBkPSJNNDggNDBINDhWODhINDhWNDBaIiBzdHJva2U9IiNENUQ5REQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjx0ZXh0IHg9IjUwJSIgeT0iNzAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTAiPuacjeijheivpuiuoeWbvjwvdGV4dD4KPC9zdmc+';
            }}
          />
        </div>

        {/* 内容区域 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">{design.title}</h3>
              <p className="text-gray-600 line-clamp-2 mt-1">{design.description}</p>
              
              {/* 标签和颜色 */}
              <div className="flex items-center space-x-4 mt-3">
                <div className="flex flex-wrap gap-1">
                  {design.tags.map(tag => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center space-x-1">
                  {design.colors.slice(0, 3).map((color, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* 底部信息 */}
              <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{design.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Eye className="h-4 w-4" />
                  <span>{design.views} 次浏览</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={onLike}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Heart className={`h-4 w-4 ${design.isLiked ? 'text-red-500 fill-current' : 'text-gray-400'}`} />
              </button>
              <button
                onClick={onView}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                title="查看详情"
              >
                <Eye className="h-4 w-4 text-gray-400" />
              </button>
              <button
                onClick={onEdit}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                title="编辑"
              >
                <Edit className="h-4 w-4 text-gray-400" />
              </button>
              <button
                onClick={onDownload}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                title="下载"
              >
                <Download className="h-4 w-4 text-gray-400" />
              </button>
              <button
                onClick={onDelete}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                title="删除"
              >
                <Trash2 className="h-4 w-4 text-red-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
