'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Play, Pause, Volume2, VolumeX, Download, Eye, FileText, Image, Video, Music, ZoomIn, ZoomOut } from 'lucide-react';
import { DesignItem, ArtifactType, DesignCategory, CANVAS_ZONES, ArtifactClassificationService } from '../types/artifacts';

// 短剧画布专用接口
interface DramaCanvasProps {
  dramaData?: any; // 短剧制作数据
  onArtifactSelect?: (artifactId: string | null) => void;
  onArtifactDelete?: (artifactId: string) => void;
  className?: string;
}

// 产物卡片组件
const ArtifactCard: React.FC<{
  artifact: DesignItem;
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
  isSelected: boolean;
}> = ({ artifact, onSelect, onDelete, isSelected }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handlePlay = () => {
    if (artifact.type === ArtifactType.VIDEO && videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    } else if (artifact.type === ArtifactType.AUDIO && audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    } else if (audioRef.current) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const getTypeIcon = () => {
    switch (artifact.type) {
      case ArtifactType.IMAGE:
        return <Image className="w-4 h-4" />;
      case ArtifactType.VIDEO:
        return <Video className="w-4 h-4" />;
      case ArtifactType.AUDIO:
        return <Music className="w-4 h-4" />;
      case ArtifactType.TEXT:
      case ArtifactType.DOCUMENT:
        return <FileText className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const renderContent = () => {
    switch (artifact.type) {
      case ArtifactType.IMAGE:
        return (
          <div className="relative">
            <img
              src={(artifact as any).image_url}
              alt={artifact.title}
              className="w-full h-32 object-cover rounded"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-image.png';
              }}
            />
            <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded px-2 py-1">
              <span className="text-white text-xs">图片</span>
            </div>
          </div>
        );

      case ArtifactType.VIDEO:
        return (
          <div className="relative">
            <video
              ref={videoRef}
              src={(artifact as any).video_url}
              className="w-full h-32 object-cover rounded"
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onError={(e) => {
                console.error('Video load error:', e);
              }}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <button
                onClick={handlePlay}
                className="bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-70 transition-all"
              >
                {isPlaying ? (
                  <Pause className="w-6 h-6 text-white" />
                ) : (
                  <Play className="w-6 h-6 text-white" />
                )}
              </button>
            </div>
            <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded px-2 py-1">
              <span className="text-white text-xs">
                {(artifact as any).duration ? `${(artifact as any).duration}s` : '视频'}
              </span>
            </div>
            <div className="absolute bottom-2 right-2">
              <button
                onClick={handleMute}
                className="bg-black bg-opacity-50 rounded p-1 hover:bg-opacity-70 transition-all"
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4 text-white" />
                ) : (
                  <Volume2 className="w-4 h-4 text-white" />
                )}
              </button>
            </div>
          </div>
        );

      case ArtifactType.AUDIO:
        return (
          <div className="relative bg-gradient-to-r from-purple-400 to-pink-400 h-32 rounded flex items-center justify-center">
            <audio
              ref={audioRef}
              src={(artifact as any).audio_url}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onError={(e) => {
                console.error('Audio load error:', e);
              }}
            />
            <div className="text-center">
              <button
                onClick={handlePlay}
                className="bg-white bg-opacity-20 rounded-full p-3 hover:bg-opacity-30 transition-all mb-2"
              >
                {isPlaying ? (
                  <Pause className="w-8 h-8 text-white" />
                ) : (
                  <Play className="w-8 h-8 text-white" />
                )}
              </button>
              <div className="text-white text-sm">
                {(artifact as any).duration ? `${(artifact as any).duration}s` : '音频'}
              </div>
            </div>
            <div className="absolute bottom-2 right-2">
              <button
                onClick={handleMute}
                className="bg-white bg-opacity-20 rounded p-1 hover:bg-opacity-30 transition-all"
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4 text-white" />
                ) : (
                  <Volume2 className="w-4 h-4 text-white" />
                )}
              </button>
            </div>
          </div>
        );

      case ArtifactType.TEXT:
      case ArtifactType.DOCUMENT:
        return (
          <div className="bg-gray-50 h-32 rounded p-3 overflow-hidden">
            <div className="text-sm text-gray-700 line-clamp-6">
              {(artifact as any).content?.substring(0, 200) + '...' || '文本内容'}
            </div>
            <div className="absolute top-2 right-2 bg-blue-500 rounded px-2 py-1">
              <span className="text-white text-xs">
                {artifact.type === ArtifactType.TEXT ? '文本' : '文档'}
              </span>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-gray-200 h-32 rounded flex items-center justify-center">
            <span className="text-gray-500">未知类型</span>
          </div>
        );
    }
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-md p-3 cursor-pointer transition-all hover:shadow-lg ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      }`}
      onClick={() => onSelect(artifact.id)}
      style={{
        position: 'absolute',
        left: artifact.position.x,
        top: artifact.position.y,
        transform: `scale(${artifact.scale})`,
        transformOrigin: 'top left',
        width: '200px',
        zIndex: isSelected ? 10 : 1
      }}
    >
      {/* 产物内容 */}
      {renderContent()}
      
      {/* 产物信息 */}
      <div className="mt-2">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center space-x-1">
            {getTypeIcon()}
            <span className="text-sm font-medium truncate">{artifact.title}</span>
          </div>
        </div>
        
        {/* 元数据 */}
        {artifact.metadata && (
          <div className="text-xs text-gray-500">
            {artifact.metadata.workflow_stage && (
              <span className="bg-gray-100 rounded px-1 py-0.5 mr-1">
                {artifact.metadata.workflow_stage}
              </span>
            )}
            {artifact.metadata.agent_type && (
              <span className="bg-blue-100 text-blue-600 rounded px-1 py-0.5">
                {artifact.metadata.agent_type}
              </span>
            )}
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      {isSelected && (
        <div className="absolute -top-2 -right-2 flex space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              // 预览功能
            }}
            className="bg-blue-500 text-white rounded-full p-1 hover:bg-blue-600 transition-colors"
          >
            <Eye className="w-3 h-3" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              // 下载功能
            }}
            className="bg-green-500 text-white rounded-full p-1 hover:bg-green-600 transition-colors"
          >
            <Download className="w-3 h-3" />
          </button>
        </div>
      )}
    </div>
  );
};

// 画布区域组件
const CanvasZone: React.FC<{
  zone: any;
  artifacts: DesignItem[];
  isVisible: boolean;
}> = ({ zone, artifacts, isVisible }) => {
  if (!isVisible) return null;

  return (
    <div
      className="absolute border-2 border-dashed border-gray-300 bg-gray-50 bg-opacity-50 rounded-lg"
      style={{
        left: zone.bounds.x,
        top: zone.bounds.y,
        width: zone.bounds.width,
        height: zone.bounds.height
      }}
    >
      <div className="absolute top-2 left-2 bg-white rounded px-2 py-1 text-sm font-medium text-gray-600">
        {zone.name} ({artifacts.length})
      </div>
    </div>
  );
};

// 主画布组件
const DramaCanvas: React.FC<DramaCanvasProps> = ({
  dramaData,
  onArtifactSelect,
  onArtifactDelete,
  className = ''
}) => {
  const [artifacts, setArtifacts] = useState<DesignItem[]>([]);
  const [selectedArtifactId, setSelectedArtifactId] = useState<string | null>(null);
  const [showZones, setShowZones] = useState(true);
  const [canvasScale, setCanvasScale] = useState(0.5); // 默认50%缩放
  const canvasRef = useRef<HTMLDivElement>(null);

  // 从短剧数据转换为画布产物
  const convertDramaDataToArtifacts = useCallback((data: any): DesignItem[] => {
    if (!data) return [];

    const newArtifacts: DesignItem[] = [];
    let artifactIndex = 0;

    // 处理剧本
    if (data.script_concept?.script) {
      const scriptArtifact: DesignItem = {
        id: `script_${Date.now()}`,
        type: ArtifactType.TEXT,
        category: DesignCategory.DRAMA,
        title: '剧本',
        timestamp: new Date(),
        position: { x: 20, y: 20 + artifactIndex * 120 },
        scale: 1,
        isSelected: false,
        content: data.script_concept.script,
        metadata: {
          agent_type: 'DramaProductionAgent',
          workflow_stage: 'script',
          text_type: 'script'
        }
      } as any;
      newArtifacts.push(scriptArtifact);
      artifactIndex++;
    }

    // 处理分镜脚本
    if (data.script_concept?.storyboard) {
      const storyboardArtifact: DesignItem = {
        id: `storyboard_${Date.now()}`,
        type: ArtifactType.TEXT,
        category: DesignCategory.DRAMA,
        title: '分镜脚本',
        timestamp: new Date(),
        position: { x: 20, y: 20 + artifactIndex * 120 },
        scale: 1,
        isSelected: false,
        content: JSON.stringify(data.script_concept.storyboard, null, 2),
        metadata: {
          agent_type: 'DramaProductionAgent',
          workflow_stage: 'script',
          text_type: 'storyboard'
        }
      } as any;
      newArtifacts.push(storyboardArtifact);
      artifactIndex++;
    }

    // 处理角色图片
    if (data.character_design?.character_images) {
      data.character_design.character_images.forEach((charImage: any, index: number) => {
        const characterArtifact: DesignItem = {
          id: `character_${charImage.character_name}_${Date.now()}`,
          type: ArtifactType.IMAGE,
          category: DesignCategory.DRAMA,
          title: `角色: ${charImage.character_name}`,
          timestamp: new Date(),
          position: { x: 340 + (index % 2) * 220, y: 20 + Math.floor(index / 2) * 120 },
          scale: 1,
          isSelected: false,
          image_url: charImage.image_url,
          prompt: charImage.prompt,
          metadata: {
            agent_type: 'DramaProductionAgent',
            workflow_stage: 'character',
            design_type: 'character'
          }
        } as any;
        newArtifacts.push(characterArtifact);
      });
    }

    // 处理场景图片
    if (data.scene_plan?.scene_images) {
      data.scene_plan.scene_images.forEach((sceneImage: any, index: number) => {
        const sceneArtifact: DesignItem = {
          id: `scene_${sceneImage.shot_id}_${Date.now()}`,
          type: ArtifactType.IMAGE,
          category: DesignCategory.DRAMA,
          title: `场景: ${sceneImage.shot_id}`,
          timestamp: new Date(),
          position: { x: 660 + (index % 3) * 220, y: 20 + Math.floor(index / 3) * 120 },
          scale: 1,
          isSelected: false,
          image_url: sceneImage.image_url,
          prompt: sceneImage.prompt,
          metadata: {
            agent_type: 'DramaProductionAgent',
            workflow_stage: 'scene',
            design_type: 'storyboard'
          }
        } as any;
        newArtifacts.push(sceneArtifact);
      });
    }

    // 处理视频片段
    if (data.video_plan?.video_segments) {
      data.video_plan.video_segments.forEach((videoSegment: any, index: number) => {
        const videoArtifact: DesignItem = {
          id: `video_${videoSegment.shot_id}_${Date.now()}`,
          type: ArtifactType.VIDEO,
          category: DesignCategory.DRAMA,
          title: `视频: ${videoSegment.shot_id}`,
          timestamp: new Date(),
          position: { x: 20 + (index % 4) * 220, y: 300 + Math.floor(index / 4) * 140 },
          scale: 1,
          isSelected: false,
          video_url: videoSegment.video_url,
          duration: videoSegment.duration,
          metadata: {
            agent_type: 'DramaProductionAgent',
            workflow_stage: 'video',
            video_type: 'segment'
          }
        } as any;
        newArtifacts.push(videoArtifact);
      });
    }

    // 处理音频文件
    if (data.post_production?.production_results) {
      const results = data.post_production.production_results;

      // 配音
      if (results.voiceover) {
        const voiceoverArtifact: DesignItem = {
          id: `voiceover_${Date.now()}`,
          type: ArtifactType.AUDIO,
          category: DesignCategory.DRAMA,
          title: '配音',
          timestamp: new Date(),
          position: { x: 20, y: 500 },
          scale: 1,
          isSelected: false,
          audio_url: results.voiceover.audio_url,
          duration: results.voiceover.duration,
          metadata: {
            agent_type: 'DramaProductionAgent',
            workflow_stage: 'post_production',
            audio_type: 'voiceover'
          }
        } as any;
        newArtifacts.push(voiceoverArtifact);
      }

      // 背景音乐
      if (results.background_music) {
        const musicArtifact: DesignItem = {
          id: `music_${Date.now()}`,
          type: ArtifactType.AUDIO,
          category: DesignCategory.DRAMA,
          title: '背景音乐',
          timestamp: new Date(),
          position: { x: 250, y: 500 },
          scale: 1,
          isSelected: false,
          audio_url: results.background_music.audio_url,
          duration: results.background_music.duration,
          metadata: {
            agent_type: 'DramaProductionAgent',
            workflow_stage: 'post_production',
            audio_type: 'background_music'
          }
        } as any;
        newArtifacts.push(musicArtifact);
      }

      // 最终视频
      if (results.final_drama) {
        const finalVideoArtifact: DesignItem = {
          id: `final_drama_${Date.now()}`,
          type: ArtifactType.VIDEO,
          category: DesignCategory.DRAMA,
          title: '最终短剧',
          timestamp: new Date(),
          position: { x: 20, y: 650 },
          scale: 1.2, // 最终作品稍大一些
          isSelected: false,
          video_url: results.final_drama.video_url,
          duration: results.final_drama.duration,
          metadata: {
            agent_type: 'DramaProductionAgent',
            workflow_stage: 'post_production',
            video_type: 'final_drama'
          }
        } as any;
        newArtifacts.push(finalVideoArtifact);
      }
    }

    return newArtifacts;
  }, []);

  // 当短剧数据更新时，转换为画布产物
  useEffect(() => {
    if (dramaData) {
      const newArtifacts = convertDramaDataToArtifacts(dramaData);
      setArtifacts(newArtifacts);
    }
  }, [dramaData, convertDramaDataToArtifacts]);

  // 处理产物选择
  const handleArtifactSelect = useCallback((artifactId: string) => {
    setSelectedArtifactId(artifactId);
    onArtifactSelect?.(artifactId);
  }, [onArtifactSelect]);

  // 处理产物删除
  const handleArtifactDelete = useCallback((artifactId: string) => {
    setArtifacts(prev => prev.filter(a => a.id !== artifactId));
    if (selectedArtifactId === artifactId) {
      setSelectedArtifactId(null);
    }
    onArtifactDelete?.(artifactId);
  }, [selectedArtifactId, onArtifactDelete]);

  // 获取每个区域的产物
  const getArtifactsForZone = useCallback((zoneId: string) => {
    return artifacts.filter(artifact => {
      const recommendedZone = ArtifactClassificationService.getRecommendedZone(artifact);
      return recommendedZone === zoneId;
    });
  }, [artifacts]);

  return (
    <div className={`relative w-full h-full bg-gray-100 overflow-hidden ${className}`}>
      {/* 工具栏 */}
      <div className="absolute top-4 left-4 z-20 bg-white rounded-lg shadow-md p-2 flex items-center space-x-2">
        <button
          onClick={() => setShowZones(!showZones)}
          className={`px-3 py-1 rounded text-sm ${
            showZones ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
        >
          显示区域
        </button>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setCanvasScale(Math.max(0.25, canvasScale - 0.25))}
            className="p-1 rounded hover:bg-gray-100"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          <span className="text-sm text-gray-600 min-w-[3rem] text-center">
            {Math.round(canvasScale * 100)}%
          </span>
          <button
            onClick={() => setCanvasScale(Math.min(2, canvasScale + 0.25))}
            className="p-1 rounded hover:bg-gray-100"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 画布内容 */}
      <div
        ref={canvasRef}
        className="relative w-full h-full"
        style={{
          transform: `scale(${canvasScale})`,
          transformOrigin: 'top left',
          width: `${100 / canvasScale}%`,
          height: `${100 / canvasScale}%`
        }}
        onClick={() => setSelectedArtifactId(null)}
      >
        {/* 画布区域 */}
        {showZones && Object.values(CANVAS_ZONES).map(zone => (
          <CanvasZone
            key={zone.id}
            zone={zone}
            artifacts={getArtifactsForZone(zone.id)}
            isVisible={showZones}
          />
        ))}

        {/* 产物卡片 */}
        {artifacts.map(artifact => (
          <ArtifactCard
            key={artifact.id}
            artifact={artifact}
            onSelect={handleArtifactSelect}
            onDelete={handleArtifactDelete}
            isSelected={selectedArtifactId === artifact.id}
          />
        ))}

        {/* 空状态 */}
        {artifacts.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <Video className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">暂无短剧制作产物</h3>
              <p className="text-sm">开始创作短剧后，制作过程中的产物将在此展示</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DramaCanvas;
