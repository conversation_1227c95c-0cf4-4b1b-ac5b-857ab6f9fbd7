'use client';

import { Suspense, useState, useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, ContactShadows } from '@react-three/drei';
import { RotateCcw, ZoomIn, ZoomOut, Download, Settings, Palette, Shirt } from 'lucide-react';
import * as THREE from 'three';
import { apiClient } from '@/lib/api';

// 人体模型组件
function HumanModel({ bodyType = 'standard', pose = 'standing' }) {
  const groupRef = useRef();

  // 根据身体类型调整比例
  const getBodyScale = () => {
    switch (bodyType) {
      case 'slim': return [0.9, 1.0, 0.9];
      case 'curvy': return [1.1, 1.0, 1.1];
      default: return [1.0, 1.0, 1.0];
    }
  };

  // 根据姿势调整旋转
  const getPoseRotation = () => {
    switch (pose) {
      case 'walking': return [0, 0.1, 0];
      case 'sitting': return [0.2, 0, 0];
      default: return [0, 0, 0];
    }
  };

  return (
    <group ref={groupRef} scale={getBodyScale()} rotation={getPoseRotation()}>
      {/* 头部 */}
      <mesh position={[0, 1.6, 0]}>
        <sphereGeometry args={[0.12, 16, 16]} />
        <meshStandardMaterial color="#fdbcb4" />
      </mesh>

      {/* 颈部 */}
      <mesh position={[0, 1.45, 0]}>
        <cylinderGeometry args={[0.06, 0.08, 0.15, 8]} />
        <meshStandardMaterial color="#fdbcb4" />
      </mesh>

      {/* 躯干 */}
      <mesh position={[0, 1.0, 0]}>
        <boxGeometry args={[0.35, 0.8, 0.2]} />
        <meshStandardMaterial color="#fdbcb4" />
      </mesh>

      {/* 左臂 */}
      <group position={[-0.25, 1.2, 0]}>
        <mesh position={[0, -0.2, 0]}>
          <cylinderGeometry args={[0.05, 0.06, 0.4, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
        <mesh position={[0, -0.5, 0]}>
          <cylinderGeometry args={[0.04, 0.05, 0.3, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
      </group>

      {/* 右臂 */}
      <group position={[0.25, 1.2, 0]}>
        <mesh position={[0, -0.2, 0]}>
          <cylinderGeometry args={[0.05, 0.06, 0.4, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
        <mesh position={[0, -0.5, 0]}>
          <cylinderGeometry args={[0.04, 0.05, 0.3, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
      </group>

      {/* 腰部 */}
      <mesh position={[0, 0.5, 0]}>
        <cylinderGeometry args={[0.15, 0.18, 0.2, 8]} />
        <meshStandardMaterial color="#fdbcb4" />
      </mesh>

      {/* 左腿 */}
      <group position={[-0.1, 0.2, 0]}>
        <mesh position={[0, -0.25, 0]}>
          <cylinderGeometry args={[0.08, 0.09, 0.5, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
        <mesh position={[0, -0.65, 0]}>
          <cylinderGeometry args={[0.06, 0.07, 0.4, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
      </group>

      {/* 右腿 */}
      <group position={[0.1, 0.2, 0]}>
        <mesh position={[0, -0.25, 0]}>
          <cylinderGeometry args={[0.08, 0.09, 0.5, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
        <mesh position={[0, -0.65, 0]}>
          <cylinderGeometry args={[0.06, 0.07, 0.4, 8]} />
          <meshStandardMaterial color="#fdbcb4" />
        </mesh>
      </group>
    </group>
  );
}

// 服装组件
function ClothingItem({ designImage, clothingType = 'shirt', color = '#6366f1' }) {
  const [texture, setTexture] = useState(null);

  useEffect(() => {
    if (designImage) {
      const loader = new THREE.TextureLoader();
      loader.load(
        designImage,
        (loadedTexture) => {
          loadedTexture.wrapS = THREE.RepeatWrapping;
          loadedTexture.wrapT = THREE.RepeatWrapping;
          loadedTexture.repeat.set(1, 1);
          setTexture(loadedTexture);
        },
        undefined,
        (error) => {
          console.error('Error loading texture:', error);
        }
      );
    }
  }, [designImage]);

  const getMaterial = () => {
    if (texture) {
      return (
        <meshStandardMaterial
          map={texture}
          roughness={0.7}
          metalness={0.1}
        />
      );
    }
    return (
      <meshStandardMaterial
        color={color}
        roughness={0.8}
        metalness={0.2}
      />
    );
  };

  // 根据服装类型渲染不同的几何体
  const renderClothing = () => {
    switch (clothingType) {
      case 'dress':
        return (
          <group>
            {/* 连衣裙上身 */}
            <mesh position={[0, 1.0, 0.02]}>
              <boxGeometry args={[0.4, 0.6, 0.15]} />
              {getMaterial()}
            </mesh>
            {/* 连衣裙下摆 */}
            <mesh position={[0, 0.3, 0.02]}>
              <cylinderGeometry args={[0.3, 0.2, 0.8, 12]} />
              {getMaterial()}
            </mesh>
          </group>
        );

      case 'pants':
        return (
          <group>
            {/* 左裤腿 */}
            <mesh position={[-0.1, 0.2, 0.02]}>
              <cylinderGeometry args={[0.09, 0.11, 0.9, 8]} />
              {getMaterial()}
            </mesh>
            {/* 右裤腿 */}
            <mesh position={[0.1, 0.2, 0.02]}>
              <cylinderGeometry args={[0.09, 0.11, 0.9, 8]} />
              {getMaterial()}
            </mesh>
            {/* 腰部 */}
            <mesh position={[0, 0.6, 0.02]}>
              <cylinderGeometry args={[0.19, 0.16, 0.15, 8]} />
              {getMaterial()}
            </mesh>
          </group>
        );

      case 'jacket':
        return (
          <group>
            {/* 夹克主体 */}
            <mesh position={[0, 1.0, 0.02]}>
              <boxGeometry args={[0.42, 0.7, 0.18]} />
              {getMaterial()}
            </mesh>
            {/* 左袖 */}
            <mesh position={[-0.3, 1.0, 0.02]}>
              <cylinderGeometry args={[0.07, 0.08, 0.6, 8]} />
              {getMaterial()}
            </mesh>
            {/* 右袖 */}
            <mesh position={[0.3, 1.0, 0.02]}>
              <cylinderGeometry args={[0.07, 0.08, 0.6, 8]} />
              {getMaterial()}
            </mesh>
          </group>
        );

      default: // shirt
        return (
          <group>
            {/* 衬衫主体 */}
            <mesh position={[0, 1.0, 0.02]}>
              <boxGeometry args={[0.38, 0.65, 0.16]} />
              {getMaterial()}
            </mesh>
            {/* 左袖 */}
            <mesh position={[-0.28, 1.1, 0.02]}>
              <cylinderGeometry args={[0.06, 0.07, 0.4, 8]} />
              {getMaterial()}
            </mesh>
            {/* 右袖 */}
            <mesh position={[0.28, 1.1, 0.02]}>
              <cylinderGeometry args={[0.06, 0.07, 0.4, 8]} />
              {getMaterial()}
            </mesh>
          </group>
        );
    }
  };

  return renderClothing();
}

// 完整的3D模型组件
function Complete3DModel({ designImage, modelSettings }) {
  const groupRef = useRef();

  // 添加轻微的动画效果
  useFrame((state) => {
    if (groupRef.current && modelSettings.pose === 'walking') {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  // 根据设计图片智能识别服装类型
  const detectClothingType = (imageUrl) => {
    if (!imageUrl) return 'shirt';

    // 这里可以添加更复杂的图像识别逻辑
    // 目前基于URL或描述进行简单判断
    const url = imageUrl.toLowerCase();
    if (url.includes('dress') || url.includes('连衣裙')) return 'dress';
    if (url.includes('pants') || url.includes('裤子')) return 'pants';
    if (url.includes('jacket') || url.includes('夹克')) return 'jacket';
    return 'shirt';
  };

  const clothingType = detectClothingType(designImage);

  return (
    <group ref={groupRef}>
      {/* 人体模型 */}
      <HumanModel
        bodyType={modelSettings.bodyType}
        pose={modelSettings.pose}
      />

      {/* 服装 */}
      <ClothingItem
        designImage={designImage}
        clothingType={clothingType}
        color={modelSettings.primaryColor || '#6366f1'}
      />

      {/* 配饰（可选） */}
      {modelSettings.showAccessories && (
        <group>
          {/* 简单的鞋子 */}
          <mesh position={[-0.1, -0.9, 0.05]}>
            <boxGeometry args={[0.08, 0.05, 0.15]} />
            <meshStandardMaterial color="#2d2d2d" />
          </mesh>
          <mesh position={[0.1, -0.9, 0.05]}>
            <boxGeometry args={[0.08, 0.05, 0.15]} />
            <meshStandardMaterial color="#2d2d2d" />
          </mesh>
        </group>
      )}
    </group>
  );
}

// 加载组件
function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
        <p className="text-gray-600">加载3D模型中...</p>
      </div>
    </div>
  );
}

export default function ThreeDViewer({ designImage = null, conceptId = null }) {
  const [cameraPosition, setCameraPosition] = useState([2, 2, 5]);
  const [showSettings, setShowSettings] = useState(false);
  const [isRendering, setIsRendering] = useState(false);
  const [modelSettings, setModelSettings] = useState({
    bodyType: 'standard',
    pose: 'standing',
    lighting: 'studio',
    background: 'neutral',
    primaryColor: '#6366f1',
    showAccessories: true,
    renderQuality: 'high',
  });

  const handleReset = () => {
    setCameraPosition([2, 2, 5]);
  };

  const handleRender3D = async () => {
    if (!conceptId) {
      alert('请先生成设计概念');
      return;
    }

    setIsRendering(true);
    try {
      // 调用3D渲染API
      const response = await apiClient.render3D(conceptId, modelSettings);

      if (response.success) {
        console.log('3D rendering completed:', response.data);
        // 可以在这里更新3D模型状态
      } else {
        console.error('3D rendering failed:', response.message);
        alert('3D渲染失败: ' + response.message);
      }
    } catch (error) {
      console.error('3D rendering failed:', error);
      alert('3D渲染失败，请稍后重试');
    } finally {
      setIsRendering(false);
    }
  };

  const handleDownload = () => {
    // 创建canvas截图
    const canvas = document.querySelector('canvas');
    if (canvas) {
      const link = document.createElement('a');
      link.download = '3d-design.png';
      link.href = canvas.toDataURL();
      link.click();
    }
  };

  const handleExport3D = () => {
    // TODO: 实现3D模型导出功能 (GLB/GLTF格式)
    console.log('Export 3D model');
    alert('3D模型导出功能开发中...');
  };

  const bodyTypes = [
    { value: 'slim', label: '纤细' },
    { value: 'standard', label: '标准' },
    { value: 'curvy', label: '丰满' },
  ];

  const poses = [
    { value: 'standing', label: '站立' },
    { value: 'walking', label: '行走' },
    { value: 'sitting', label: '坐姿' },
  ];

  const lightingOptions = [
    { value: 'studio', label: '工作室' },
    { value: 'natural', label: '自然光' },
    { value: 'dramatic', label: '戏剧性' },
    { value: 'dramatic', label: '戏剧性' },
  ];

  const backgroundOptions = [
    { value: 'neutral', label: '中性' },
    { value: 'gradient', label: '渐变' },
    { value: 'environment', label: '环境' },
  ];

  return (
    <div className="h-full flex">
      {/* 3D视图区域 */}
      <div className="flex-1 relative">
        <Canvas
          camera={{ position: cameraPosition, fov: 50 }}
          shadows
          gl={{ preserveDrawingBuffer: true }} // 允许截图
        >
          <Suspense fallback={null}>
            {/* 环境光照 */}
            <Environment
              preset={
                modelSettings.lighting === 'studio' ? 'studio' :
                modelSettings.lighting === 'natural' ? 'park' : 'sunset'
              }
            />

            {/* 主光源 */}
            <directionalLight
              position={[10, 10, 5]}
              intensity={modelSettings.lighting === 'dramatic' ? 1.5 : 1}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
            />

            {/* 补光 */}
            <ambientLight intensity={modelSettings.lighting === 'dramatic' ? 0.2 : 0.4} />

            {/* 背景 */}
            {modelSettings.background === 'gradient' && (
              <mesh position={[0, 0, -5]} scale={[20, 20, 1]}>
                <planeGeometry />
                <meshBasicMaterial color="#f0f0f0" />
              </mesh>
            )}

            {/* 3D模型 */}
            <Complete3DModel
              designImage={designImage}
              modelSettings={modelSettings}
            />

            {/* 地面阴影 */}
            <ContactShadows
              position={[0, -0.9, 0]}
              opacity={0.4}
              scale={10}
              blur={1.5}
              far={0.9}
            />

            {/* 相机控制 */}
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              minDistance={2}
              maxDistance={10}
            />
          </Suspense>
        </Canvas>

        {/* 控制按钮 */}
        <div className="absolute top-4 right-4 flex flex-col space-y-2">
          <button
            onClick={handleReset}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors"
            title="重置视角"
          >
            <RotateCcw className="h-4 w-4 text-gray-700" />
          </button>

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors"
            title="设置"
          >
            <Settings className="h-4 w-4 text-gray-700" />
          </button>

          <button
            onClick={handleRender3D}
            disabled={isRendering}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors disabled:opacity-50"
            title="重新渲染"
          >
            {isRendering ? (
              <div className="animate-spin h-4 w-4 border-2 border-gray-700 border-t-transparent rounded-full" />
            ) : (
              <Palette className="h-4 w-4 text-gray-700" />
            )}
          </button>

          <button
            onClick={handleDownload}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors"
            title="下载截图"
          >
            <Download className="h-4 w-4 text-gray-700" />
          </button>

          <button
            onClick={handleExport3D}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors"
            title="导出3D模型"
          >
            <Shirt className="h-4 w-4 text-gray-700" />
          </button>
        </div>

        {/* 加载状态 */}
        <Suspense fallback={<LoadingSpinner />}>
          <div />
        </Suspense>
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <div className="w-80 bg-white border-l border-gray-200 p-6 space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">3D设置</h3>
            <button
              onClick={() => setShowSettings(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* 身体类型 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              身体类型
            </label>
            <div className="space-y-2">
              {bodyTypes.map((type) => (
                <label key={type.value} className="flex items-center">
                  <input
                    type="radio"
                    name="bodyType"
                    value={type.value}
                    checked={modelSettings.bodyType === type.value}
                    onChange={(e) =>
                      setModelSettings({ ...modelSettings, bodyType: e.target.value })
                    }
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 姿势 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              姿势
            </label>
            <div className="space-y-2">
              {poses.map((pose) => (
                <label key={pose.value} className="flex items-center">
                  <input
                    type="radio"
                    name="pose"
                    value={pose.value}
                    checked={modelSettings.pose === pose.value}
                    onChange={(e) =>
                      setModelSettings({ ...modelSettings, pose: e.target.value })
                    }
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">{pose.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 光照 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              光照
            </label>
            <select
              value={modelSettings.lighting}
              onChange={(e) =>
                setModelSettings({ ...modelSettings, lighting: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {lightingOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 背景 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              背景
            </label>
            <select
              value={modelSettings.background}
              onChange={(e) =>
                setModelSettings({ ...modelSettings, background: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {backgroundOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 主色调 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              主色调
            </label>
            <input
              type="color"
              value={modelSettings.primaryColor}
              onChange={(e) =>
                setModelSettings({ ...modelSettings, primaryColor: e.target.value })
              }
              className="w-full h-10 border border-gray-300 rounded-lg cursor-pointer"
            />
          </div>

          {/* 配饰选项 */}
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={modelSettings.showAccessories}
                onChange={(e) =>
                  setModelSettings({ ...modelSettings, showAccessories: e.target.checked })
                }
                className="mr-2"
              />
              <span className="text-sm text-gray-700">显示配饰</span>
            </label>
          </div>

          {/* 渲染质量 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              渲染质量
            </label>
            <select
              value={modelSettings.renderQuality}
              onChange={(e) =>
                setModelSettings({ ...modelSettings, renderQuality: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="low">低质量</option>
              <option value="medium">中等质量</option>
              <option value="high">高质量</option>
            </select>
          </div>

          {/* 渲染按钮 */}
          <button
            onClick={handleRender3D}
            disabled={isRendering}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-2 px-4 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all disabled:opacity-50"
          >
            {isRendering ? '渲染中...' : '应用设置'}
          </button>

          {/* 导出选项 */}
          <div className="pt-4 border-t border-gray-200 space-y-2">
            <button
              onClick={handleDownload}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors"
            >
              下载截图
            </button>
            <button
              onClick={handleExport3D}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors"
            >
              导出3D模型
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
