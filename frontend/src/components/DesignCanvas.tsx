'use client';

import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Maximize2, RotateCcw, Download, Trash2, Eye, ZoomIn, ZoomOut } from 'lucide-react';
import ThreeDViewer from './ThreeDViewer';

// 设计图片数据结构
export interface DesignItem {
  id: string;
  image_url: string;
  prompt?: string;
  timestamp: Date;
  position: { x: number; y: number };
  scale: number;
  isSelected: boolean;
  metadata?: {
    ai_model?: string;
    generation_params?: any;
    originalWidth?: number;
    originalHeight?: number;
  };
}

// Canvas配置
interface CanvasConfig {
  width: number;
  height: number;
  backgroundColor: string;
  gridSize: number;
  showGrid: boolean;
  maxScale: number;
  minScale: number;
}

// 3D预览状态
interface ThreeDPreviewState {
  isVisible: boolean;
  designId: string | null;
  position: { x: number; y: number };
}

interface DesignCanvasProps {
  onDesignSelect?: (designId: string | null) => void;
  onDesignDelete?: (designId: string) => void;
  onAIEdit?: (design: DesignItem) => void;
  onPositionChange?: (designs: DesignItem[]) => void;
  className?: string;
  maxDesigns?: number;
  autoArrange?: boolean;
  gridSnap?: boolean;
}

// 暴露给父组件的方法
export interface DesignCanvasRef {
  addDesign: (design: {
    id: string;
    image_url: string;
    prompt?: string;
    timestamp?: Date;
    metadata?: any;
    position?: { x: number; y: number };
    scale?: number;
    isSelected?: boolean;
  }) => string;
  clearDesigns: () => void;
  getDesigns: () => DesignItem[];
  selectDesign: (designId: string | null) => void;
  getViewState: () => { scale: number; offset_x: number; offset_y: number };
  setViewState: (viewState: { scale: number; offset_x: number; offset_y: number }) => void;
}

const DesignCanvas = forwardRef<DesignCanvasRef, DesignCanvasProps>(({
  onDesignSelect,
  onDesignDelete,
  onAIEdit,
  onPositionChange,
  className = '',
  maxDesigns = 20,
  autoArrange = true,
  gridSnap = false
}, ref) => {
  // Canvas相关状态
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [designs, setDesigns] = useState<DesignItem[]>([]);
  const [selectedDesignId, setSelectedDesignId] = useState<string | null>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });

  // 图片缓存
  const imageCache = useRef<Map<string, HTMLImageElement>>(new Map());

  // 交互状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragTarget, setDragTarget] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [hoveredDesignId, setHoveredDesignId] = useState<string | null>(null);

  // 画布平移状态
  const [isPanningCanvas, setIsPanningCanvas] = useState(false);
  const [panStartPosition, setPanStartPosition] = useState({ x: 0, y: 0 });
  const [panStartOffset, setPanStartOffset] = useState({ x: 0, y: 0 });

  // 画布缩放状态
  const [canvasZoom, setCanvasZoom] = useState(1);
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [isZooming, setIsZooming] = useState(false);

  // Canvas配置
  const canvasConfig = {
    backgroundColor: '#ffffff',
    gridSize: 20,
    showGrid: false,      // 不显示网格
    cardPadding: 16,      // 卡片内边距
    cardRadius: 12,       // 卡片圆角
    shadowBlur: 8,        // 阴影模糊度
    shadowOffset: 4,      // 阴影偏移
    maxCardSize: 450      // 最大卡片尺寸（用于边界计算）
  };

  // 计算保持宽高比的尺寸
  const calculateImageSize = useCallback((img: HTMLImageElement, scale: number = 1) => {
    // 设置一个合理的基础尺寸，但允许根据图片比例调整
    const baseSize = 200; // 基础尺寸
    const maxSize = 400;   // 最大尺寸限制

    const aspectRatio = img.width / img.height;
    console.log(`Image dimensions: ${img.width}x${img.height}, aspectRatio: ${aspectRatio}`);

    let width, height;

    if (aspectRatio > 1) {
      // 横图：以宽度为基准
      width = Math.min(baseSize * aspectRatio, maxSize);
      height = width / aspectRatio;
      console.log(`Horizontal image: width=${width}, height=${height}`);
    } else {
      // 竖图或方图：以高度为基准
      height = Math.min(baseSize / aspectRatio, maxSize);
      width = height * aspectRatio;
      console.log(`Vertical/Square image: width=${width}, height=${height}`);
    }

    // 应用缩放
    width *= scale;
    height *= scale;

    console.log(`Final size after scale ${scale}: ${width}x${height}`);
    return { width, height };
  }, []);
  
  // 3D预览状态
  const [threeDPreview, setThreeDPreview] = useState<ThreeDPreviewState>({
    isVisible: false,
    designId: null,
    position: { x: 0, y: 0 }
  });

  // 悬浮菜单状态
  const [floatingMenu, setFloatingMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    designId: string | null;
    isClosing: boolean;
  }>({
    visible: false,
    x: 0,
    y: 0,
    designId: null,
    isClosing: false
  });

  // Canvas绘制函数
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置高质量渲染
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Polyfill for roundRect if not supported
    if (!ctx.roundRect) {
      (ctx as any).roundRect = function(x: number, y: number, width: number, height: number, radius: number) {
        this.beginPath();
        this.moveTo(x + radius, y);
        this.lineTo(x + width - radius, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.lineTo(x + width, y + height - radius);
        this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.lineTo(x + radius, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.lineTo(x, y + radius);
        this.quadraticCurveTo(x, y, x + radius, y);
        this.closePath();
      };
    }

    // 获取显示尺寸（CSS像素）
    const displayWidth = canvasSize.width;
    const displayHeight = canvasSize.height;

    // 清空画布
    ctx.clearRect(0, 0, displayWidth, displayHeight);

    // 保存当前变换状态
    ctx.save();

    // 应用画布缩放和偏移
    ctx.translate(canvasOffset.x, canvasOffset.y);
    ctx.scale(canvasZoom, canvasZoom);

    // 绘制背景
    ctx.fillStyle = canvasConfig.backgroundColor;
    ctx.fillRect(-canvasOffset.x / canvasZoom, -canvasOffset.y / canvasZoom,
                 displayWidth / canvasZoom, displayHeight / canvasZoom);

    // 不再绘制网格，保持简洁的白色背景

    // 绘制设计图片（卡片式）
    designs.forEach((design, index) => {
      const img = imageCache.current.get(design.image_url);

      if (img && img.complete) {
        // 计算保持宽高比的尺寸
        const { width, height } = calculateImageSize(img, design.scale);
        console.log(`Drawing design ${design.id}: calculated size ${width}x${height}`);
        const cardPadding = canvasConfig.cardPadding;
        const cardWidth = width + cardPadding * 2;
        const cardHeight = height + cardPadding * 2;
        console.log(`Card size: ${cardWidth}x${cardHeight}`);

        const cardX = design.position.x - cardWidth / 2;
        const cardY = design.position.y - cardHeight / 2;
        const imgX = cardX + cardPadding;
        const imgY = cardY + cardPadding;

        // 绘制卡片阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
        ctx.shadowBlur = canvasConfig.shadowBlur;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = canvasConfig.shadowOffset;

        // 如果选中或悬停，增强阴影效果
        if (design.isSelected) {
          ctx.shadowColor = 'rgba(139, 92, 246, 0.3)';
          ctx.shadowBlur = 20;
          ctx.shadowOffsetY = 8;
        } else if (hoveredDesignId === design.id) {
          ctx.shadowColor = 'rgba(0, 0, 0, 0.15)';
          ctx.shadowBlur = 12;
          ctx.shadowOffsetY = 6;
        }

        // 绘制卡片背景
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.roundRect(cardX, cardY, cardWidth, cardHeight, canvasConfig.cardRadius);
        ctx.fill();

        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 绘制图片（带圆角）
        ctx.save();
        ctx.beginPath();
        ctx.roundRect(imgX, imgY, width, height, canvasConfig.cardRadius - 4);
        ctx.clip();
        ctx.drawImage(img, imgX, imgY, width, height);
        ctx.restore();

        // 绘制选中边框
        if (design.isSelected) {
          ctx.strokeStyle = '#8b5cf6';
          ctx.lineWidth = 3;
          ctx.beginPath();
          ctx.roundRect(cardX - 2, cardY - 2, cardWidth + 4, cardHeight + 4, canvasConfig.cardRadius + 2);
          ctx.stroke();
        }

        // 绘制悬停边框
        if (hoveredDesignId === design.id && !design.isSelected) {
          ctx.strokeStyle = '#e5e7eb';
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.roundRect(cardX - 1, cardY - 1, cardWidth + 2, cardHeight + 2, canvasConfig.cardRadius + 1);
          ctx.stroke();
        }
      } else {
        console.log(`Image not ready for design ${design.id}:`, img?.complete, img?.width, img?.height);
        
        // 如果图片加载失败，显示占位符
        if (!img || !img.complete) {
          const { width, height } = { width: 200, height: 200 }; // 占位符尺寸
          const cardPadding = canvasConfig.cardPadding;
          const cardWidth = width + cardPadding * 2;
          const cardHeight = height + cardPadding * 2;

          const cardX = design.position.x - cardWidth / 2;
          const cardY = design.position.y - cardHeight / 2;

          // 绘制卡片阴影
          ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
          ctx.shadowBlur = canvasConfig.shadowBlur;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = canvasConfig.shadowOffset;

          // 绘制卡片背景
          ctx.fillStyle = design.isSelected ? '#f3f4f6' : '#ffffff';
          ctx.beginPath();
          ctx.roundRect(cardX, cardY, cardWidth, cardHeight, canvasConfig.cardRadius);
          ctx.fill();

          // 重置阴影
          ctx.shadowColor = 'transparent';
          ctx.shadowBlur = 0;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;

          // 绘制选中边框
          if (design.isSelected) {
            ctx.strokeStyle = '#8b5cf6';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.roundRect(cardX - 2, cardY - 2, cardWidth + 4, cardHeight + 4, canvasConfig.cardRadius + 2);
            ctx.stroke();
          }

          // 绘制占位符内容
          ctx.fillStyle = '#9ca3af';
          ctx.font = '14px system-ui, -apple-system, sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          const centerX = design.position.x;
          const centerY = design.position.y;
          ctx.fillText('图片加载中...', centerX, centerY - 10);
          ctx.font = '12px system-ui, -apple-system, sans-serif';
          ctx.fillStyle = '#d1d5db';
          ctx.fillText(design.prompt || '设计图片', centerX, centerY + 10);
        }
      }
    });

    // 绘制空状态提示
    if (designs.length === 0) {
      // 绘制一个优雅的空状态卡片
      const emptyCardWidth = 320;
      const emptyCardHeight = 200;
      const emptyCardX = (displayWidth / canvasZoom - emptyCardWidth) / 2 - canvasOffset.x / canvasZoom;
      const emptyCardY = (displayHeight / canvasZoom - emptyCardHeight) / 2 - canvasOffset.y / canvasZoom;

      // 绘制空状态卡片阴影
      ctx.shadowColor = 'rgba(0, 0, 0, 0.05)';
      ctx.shadowBlur = 20;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 8;

      // 绘制空状态卡片背景
      ctx.fillStyle = '#fafafa';
      ctx.beginPath();
      ctx.roundRect(emptyCardX, emptyCardY, emptyCardWidth, emptyCardHeight, 16);
      ctx.fill();

      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // 绘制边框
      ctx.strokeStyle = '#f0f0f0';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.roundRect(emptyCardX, emptyCardY, emptyCardWidth, emptyCardHeight, 16);
      ctx.stroke();

      // 绘制文字
      ctx.fillStyle = '#6b7280';
      ctx.font = '20px system-ui, -apple-system, sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      const centerX = displayWidth / 2 / canvasZoom - canvasOffset.x / canvasZoom;
      const centerY = displayHeight / 2 / canvasZoom - canvasOffset.y / canvasZoom;
      ctx.fillText('设计画布', centerX, centerY - 20);

      ctx.font = '14px system-ui, -apple-system, sans-serif';
      ctx.fillStyle = '#9ca3af';
      ctx.fillText('对话中生成的设计将在这里展示', centerX, centerY + 10);

      ctx.font = '12px system-ui, -apple-system, sans-serif';
      ctx.fillStyle = '#d1d5db';
      ctx.fillText('支持拖拽移动和3D预览', centerX, centerY + 35);
    }

    // 恢复变换状态
    ctx.restore();
  }, [designs, hoveredDesignId, canvasConfig, canvasZoom, canvasOffset]);

  // 加载图片到缓存
  const loadImage = useCallback((url: string, retryCount = 0): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      if (imageCache.current.has(url)) {
        const cachedImg = imageCache.current.get(url)!;
        console.log('Using cached image:', url);
        resolve(cachedImg);
        return;
      }

      console.log('Loading new image:', url, `(attempt ${retryCount + 1})`);
      const img = new window.Image();

      img.onload = () => {
        console.log('Image loaded successfully:', url, img.width, img.height);
        imageCache.current.set(url, img);

        // 更新设计项的原始尺寸信息
        setDesigns(prev => prev.map(design => {
          if (design.image_url === url && !design.metadata?.originalWidth) {
            return {
              ...design,
              metadata: {
                ...design.metadata,
                originalWidth: img.width,
                originalHeight: img.height
              }
            };
          }
          return design;
        }));

        // 图片加载完成后重新绘制Canvas
        setTimeout(() => drawCanvas(), 0);
        resolve(img);
      };

      img.onerror = (error) => {
        console.warn('Failed to load image:', url, error);

        // 重试机制：最多重试2次
        if (retryCount < 2) {
          console.log(`Retrying image load: ${url} (attempt ${retryCount + 2})`);
          setTimeout(() => {
            loadImage(url, retryCount + 1).then(resolve).catch(reject);
          }, 1000 * (retryCount + 1)); // 递增延迟
        } else {
          // 不要抛出错误，而是返回一个占位图片
          console.log('Using placeholder image for:', url);
          const placeholderImg = new window.Image();
          placeholderImg.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjlGQUZCIiByeD0iMTIiLz4KPHN2ZyB4PSI3NSIgeT0iNzUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnpNMTMgMTdoLTJ2LTZoMnY2em0wLThoLTJWN2gydjJ6IiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo8L3N2Zz4=';
          placeholderImg.width = 200;
          placeholderImg.height = 200;
          imageCache.current.set(url, placeholderImg);
          resolve(placeholderImg);
        }
      };

      img.src = url;
    });
  }, [drawCanvas]);

  // 添加设计
  const addDesign = useCallback((newDesign: {
    id: string;
    image_url: string;
    prompt?: string;
    timestamp?: Date;
    metadata?: any;
    position?: { x: number; y: number }; // 添加可选的位置参数
    scale?: number; // 添加可选的缩放参数
    isSelected?: boolean; // 添加可选的选中状态
  }) => {


    let position: { x: number; y: number };

    if (newDesign.position) {
      // 如果提供了位置，直接使用（用于恢复保存的状态）
      position = newDesign.position;
      console.log('🎨 使用提供的位置:', position);
    } else {
      // 否则计算新位置 - 考虑当前画布视图的中心
      const viewCenterX = (canvasSize.width / 2 - canvasOffset.x) / canvasZoom;
      const viewCenterY = (canvasSize.height / 2 - canvasOffset.y) / canvasZoom;

      // 智能自动排列：避免重叠
      position = { x: viewCenterX, y: viewCenterY };
      if (autoArrange && designs.length > 0) {
        // 尝试找到一个不重叠的位置（基于当前视图中心）
        position = findAvailablePosition(designs, canvasSize, canvasConfig, { x: viewCenterX, y: viewCenterY });
      }
      console.log('🎨 计算的新位置:', position);
    }

    const designItem: DesignItem = {
      id: newDesign.id,
      image_url: newDesign.image_url,
      prompt: newDesign.prompt,
      timestamp: newDesign.timestamp || new Date(),
      position,
      scale: newDesign.scale || 1,
      isSelected: newDesign.isSelected || false,
      metadata: {
        ...newDesign.metadata
      }
    };

    console.log('🎨 创建的设计项:', {
      id: designItem.id,
      position: designItem.position,
      scale: designItem.scale,
      isSelected: designItem.isSelected
    });

    // 先添加设计项到状态
    setDesigns(prev => {
      // 检查是否已存在相同ID的设计
      const existingIndex = prev.findIndex(d => d.id === designItem.id);
      if (existingIndex !== -1) {
        // 更新现有设计而不是添加新的
        const updated = [...prev];
        updated[existingIndex] = designItem;
        return updated;
      }

      // 添加新设计
      const newDesigns = [...prev, designItem];
      if (newDesigns.length > maxDesigns) {
        return newDesigns.slice(-maxDesigns);
      }
      return newDesigns;
    });

    // 异步加载图片
    loadImage(newDesign.image_url).catch(error => {
      console.warn('Failed to load image, using placeholder:', error);
      // 错误已经在loadImage函数中处理，这里不需要额外处理
    });

    return designItem.id;
  }, [designs, canvasSize, autoArrange, maxDesigns, loadImage, canvasOffset, canvasZoom]);

  // 清空设计
  const clearDesigns = useCallback(() => {
    setDesigns([]);
    setSelectedDesignId(null);
    imageCache.current.clear();
  }, []);

  // 选中设计
  const selectDesign = useCallback((designId: string | null) => {
    setSelectedDesignId(designId);
    setDesigns(prev => prev.map(design => ({
      ...design,
      isSelected: design.id === designId
    })));
    onDesignSelect?.(designId);
  }, [onDesignSelect]);

  // 获取当前视图状态
  const getViewState = useCallback(() => ({
    scale: canvasZoom,
    offset_x: canvasOffset.x,
    offset_y: canvasOffset.y
  }), [canvasZoom, canvasOffset]);

  // 设置视图状态
  const setViewState = useCallback((viewState: { scale: number; offset_x: number; offset_y: number }) => {
    setCanvasZoom(viewState.scale);
    setCanvasOffset({ x: viewState.offset_x, y: viewState.offset_y });
  }, []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    addDesign,
    clearDesigns,
    getDesigns: () => designs,
    selectDesign,
    getViewState,
    setViewState
  }), [addDesign, clearDesigns, designs, selectDesign, getViewState, setViewState]);

  // 获取鼠标在Canvas中的位置（考虑缩放和偏移）
  const getCanvasMousePosition = useCallback((event: MouseEvent | React.MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const rawX = event.clientX - rect.left;
    const rawY = event.clientY - rect.top;

    // 转换为画布坐标系（考虑缩放和偏移）
    return {
      x: (rawX - canvasOffset.x) / canvasZoom,
      y: (rawY - canvasOffset.y) / canvasZoom
    };
  }, [canvasZoom, canvasOffset]);

  // 检查点击是否在设计卡片上
  const getDesignAtPosition = useCallback((x: number, y: number): DesignItem | null => {
    // 从后往前检查（后面的设计在上层）
    for (let i = designs.length - 1; i >= 0; i--) {
      const design = designs[i];
      const img = imageCache.current.get(design.image_url);

      if (img && img.complete) {
        const { width, height } = calculateImageSize(img, design.scale);
        const cardPadding = canvasConfig.cardPadding;
        const cardWidth = width + cardPadding * 2;
        const cardHeight = height + cardPadding * 2;

        const left = design.position.x - cardWidth / 2;
        const right = design.position.x + cardWidth / 2;
        const top = design.position.y - cardHeight / 2;
        const bottom = design.position.y + cardHeight / 2;

        if (x >= left && x <= right && y >= top && y <= bottom) {
          return design;
        }
      }
    }
    return null;
  }, [designs, calculateImageSize]);

  // Canvas点击事件
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    // 如果正在缩放或平移，不处理点击事件
    if (isZooming || isPanningCanvas) return;

    const { x, y } = getCanvasMousePosition(event);
    const clickedDesign = getDesignAtPosition(x, y);

    if (clickedDesign) {
      selectDesign(clickedDesign.id);

      // 计算悬浮菜单位置（卡片右侧贴边）
      const img = imageCache.current.get(clickedDesign.image_url);
      if (img && img.complete) {
        const { width, height } = calculateImageSize(img, clickedDesign.scale);
        const cardPadding = canvasConfig.cardPadding;
        const cardWidth = width + cardPadding * 2;
        const cardHeight = height + cardPadding * 2;

        // 菜单紧贴卡片右边缘，垂直居中对齐（转换为屏幕坐标）
        const canvasMenuX = clickedDesign.position.x + cardWidth / 2; // 卡片右边缘（画布坐标）
        const canvasMenuY = clickedDesign.position.y; // 卡片中心（画布坐标）

        // 转换为屏幕坐标
        const menuX = canvasMenuX * canvasZoom + canvasOffset.x;
        const menuY = canvasMenuY * canvasZoom + canvasOffset.y;

        console.log('Menu positioning:', {
          designCenter: clickedDesign.position,
          cardWidth,
          cardHeight,
          menuX,
          menuY,
          cardLeftEdge: clickedDesign.position.x - cardWidth / 2,
          cardRightEdge: clickedDesign.position.x + cardWidth / 2
        });

        setFloatingMenu({
          visible: true,
          x: menuX,
          y: menuY,
          designId: clickedDesign.id,
          isClosing: false
        });
      }
    } else {
      selectDesign(null);
      closeFloatingMenu();
    }
  }, [getCanvasMousePosition, getDesignAtPosition, selectDesign, calculateImageSize, isZooming, isPanningCanvas, canvasZoom, canvasOffset]);

  // Canvas鼠标按下事件
  const handleCanvasMouseDown = useCallback((event: React.MouseEvent) => {
    const { x, y } = getCanvasMousePosition(event);
    const clickedDesign = getDesignAtPosition(x, y);

    if (clickedDesign) {
      // 开始拖拽设计卡片
      setIsDragging(true);
      setDragTarget(clickedDesign.id);
      setDragOffset({
        x: x - clickedDesign.position.x,
        y: y - clickedDesign.position.y
      });

      selectDesign(clickedDesign.id);
      event.preventDefault();
    } else {
      // 点击空白区域，开始平移画布
      setIsPanningCanvas(true);
      setPanStartPosition({
        x: event.clientX,
        y: event.clientY
      });
      setPanStartOffset({ ...canvasOffset });
      event.preventDefault();
    }
  }, [getCanvasMousePosition, getDesignAtPosition, selectDesign, canvasOffset]);



  // 用于存储最新的设计位置（解决React状态更新时序问题）
  const latestDesignsRef = useRef<DesignItem[]>([]);

  // 同步designs状态到ref
  useEffect(() => {
    latestDesignsRef.current = designs;
  }, [designs]);

  // 全局鼠标移动事件
  const handleGlobalMouseMove = useCallback((event: MouseEvent) => {
    if (isDragging && dragTarget) {
      // 拖拽设计卡片 - 移除自动跟随逻辑
      const { x, y } = getCanvasMousePosition(event);
      const newPosition = {
        x: x - dragOffset.x,
        y: y - dragOffset.y
      };

      // 更新卡片位置
      setDesigns(prev => {
        const updated = prev.map(design =>
          design.id === dragTarget ? { ...design, position: newPosition } : design
        );
        // 同时更新ref，确保有最新数据
        latestDesignsRef.current = updated;
        return updated;
      });
    } else if (isPanningCanvas) {
      // 平移画布
      const deltaX = event.clientX - panStartPosition.x;
      const deltaY = event.clientY - panStartPosition.y;

      setCanvasOffset({
        x: panStartOffset.x + deltaX,
        y: panStartOffset.y + deltaY
      });
    }
  }, [isDragging, dragTarget, dragOffset, getCanvasMousePosition, isPanningCanvas, panStartPosition, panStartOffset]);

  // 全局鼠标抬起事件
  const handleGlobalMouseUp = useCallback(() => {
    const wasDragging = isDragging;

    // 移除智能边界管理，图片卡片可以自由移动到边界之外
    setIsDragging(false);
    setDragTarget(null);
    setDragOffset({ x: 0, y: 0 });
    setIsPanningCanvas(false);
    setPanStartPosition({ x: 0, y: 0 });
    setPanStartOffset({ x: 0, y: 0 });

    // 如果刚完成拖拽，触发位置变更回调
    if (wasDragging && onPositionChange) {
      // 使用最新的设计数据（从ref获取，避免React状态更新时序问题）
      const latestDesigns = latestDesignsRef.current.length > 0 ? latestDesignsRef.current : designs;
      console.log('🎯 拖拽结束，触发位置保存，当前设计数量:', latestDesigns.length);
      console.log('📍 当前设计位置:', latestDesigns.map(d => ({ id: d.id, position: d.position })));
      onPositionChange(latestDesigns);
    }
  }, [isDragging, onPositionChange, designs]);

  // Canvas鼠标移动事件（用于悬停效果）
  const handleCanvasMouseMove = useCallback((event: React.MouseEvent) => {
    if (isDragging || isPanningCanvas) return;

    const { x, y } = getCanvasMousePosition(event);
    const hoveredDesign = getDesignAtPosition(x, y);

    setHoveredDesignId(hoveredDesign?.id || null);
  }, [isDragging, isPanningCanvas, getCanvasMousePosition, getDesignAtPosition]);

  // 删除设计
  const deleteDesign = useCallback((designId: string) => {
    setDesigns(prev => prev.filter(design => design.id !== designId));
    if (selectedDesignId === designId) {
      setSelectedDesignId(null);
    }
    onDesignDelete?.(designId);
  }, [selectedDesignId, onDesignDelete]);

  // 处理右键菜单（禁用，使用点击菜单代替）
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    // 右键菜单已禁用，使用点击显示悬浮菜单
  }, []);

  // 优雅关闭菜单
  const closeFloatingMenu = useCallback(() => {
    setFloatingMenu(prev => ({ ...prev, isClosing: true }));
    setTimeout(() => {
      setFloatingMenu({ visible: false, x: 0, y: 0, designId: null, isClosing: false });
    }, 200); // 等待动画完成
  }, []);

  // 处理AI改图
  const handleAIEdit = useCallback((designId: string) => {
    console.log('AI改图功能，设计ID:', designId);

    // 找到对应的设计
    const design = designs.find(d => d.id === designId);
    if (!design) {
      console.error('Design not found:', designId);
      return;
    }

    // 触发AI编辑事件，传递给父组件处理
    if (onAIEdit) {
      onAIEdit(design);
    }

    closeFloatingMenu();
  }, [designs, closeFloatingMenu]);

  // 处理3D预览
  const handle3DPreview = useCallback((designId: string) => {
    if (threeDPreview.isVisible && threeDPreview.designId === designId) {
      // 关闭3D预览
      setThreeDPreview({
        isVisible: false,
        designId: null,
        position: { x: 0, y: 0 }
      });
    } else {
      // 打开3D预览
      setThreeDPreview({
        isVisible: true,
        designId,
        position: { x: 0, y: 0 }
      });
    }
    closeFloatingMenu();
  }, [threeDPreview, closeFloatingMenu]);

  // 处理设计删除
  const handleDesignDelete = useCallback((designId: string) => {
    deleteDesign(designId);
    closeFloatingMenu();
  }, [deleteDesign, closeFloatingMenu]);

  // 缩放控制函数
  const handleZoomIn = useCallback(() => {
    setIsZooming(true);
    closeFloatingMenu(); // 关闭悬浮菜单
    setCanvasZoom(prev => Math.min(prev * 1.2, 3)); // 最大3倍缩放

    // 延迟重置缩放状态
    setTimeout(() => setIsZooming(false), 300);
  }, []);

  const handleZoomOut = useCallback(() => {
    setIsZooming(true);
    closeFloatingMenu(); // 关闭悬浮菜单
    setCanvasZoom(prev => Math.max(prev / 1.2, 0.25)); // 最小0.25倍缩放

    // 延迟重置缩放状态
    setTimeout(() => setIsZooming(false), 300);
  }, []);

  const handleResetZoom = useCallback(() => {
    setIsZooming(true);
    closeFloatingMenu(); // 关闭悬浮菜单
    setCanvasZoom(1);
    setCanvasOffset({ x: 0, y: 0 });

    // 延迟重置缩放状态
    setTimeout(() => setIsZooming(false), 300);
  }, []);

  




  // 监听Canvas尺寸变化并处理高DPI
  useEffect(() => {
    const updateCanvasSize = () => {
      const container = containerRef.current;
      const canvas = canvasRef.current;
      if (!container || !canvas) return;

      const { width, height } = container.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;

      // 设置Canvas的实际像素尺寸（考虑设备像素比）
      canvas.width = width * dpr;
      canvas.height = height * dpr;

      // 设置Canvas的CSS显示尺寸
      canvas.style.width = width + 'px';
      canvas.style.height = height + 'px';

      // 缩放Canvas上下文以匹配设备像素比
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.scale(dpr, dpr);
      }

      setCanvasSize({ width, height });

      console.log('Canvas size updated:', { width, height, dpr, actualWidth: canvas.width, actualHeight: canvas.height });
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    return () => window.removeEventListener('resize', updateCanvasSize);
  }, []);

  // 监听全局鼠标事件
  useEffect(() => {
    if (isDragging || isPanningCanvas) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, isPanningCanvas, handleGlobalMouseMove, handleGlobalMouseUp]);



  // 重绘Canvas - 当设计、选中状态或悬停状态变化时
  useEffect(() => {
    drawCanvas();
  }, [drawCanvas, designs, selectedDesignId, hoveredDesignId]);

  // 监听点击关闭悬浮菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 如果点击的不是Canvas，关闭菜单
      if (!canvasRef.current?.contains(event.target as Node)) {
        closeFloatingMenu();
      }
    };

    if (floatingMenu.visible) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [floatingMenu.visible]);

  // 暴露添加设计的方法给父组件
  useEffect(() => {
    // 这里可以通过ref或者其他方式暴露addDesign方法
  }, [addDesign]);

  // 调试信息
  useEffect(() => {
    console.log('DesignCanvas rendered, designs count:', designs.length);
  }, [designs.length]);

  return (
    <div className={`relative w-full h-full overflow-hidden ${className}`}>
      {/* 添加动画样式 */}
      <style jsx>{`
        @keyframes slideInFromRight {
          from {
            opacity: 0;
            transform: translateX(10px) translateY(-50%) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateX(0) translateY(-50%) scale(1);
          }
        }

        @keyframes slideOutToRight {
          from {
            opacity: 1;
            transform: translateX(0) translateY(-50%) scale(1);
          }
          to {
            opacity: 0;
            transform: translateX(10px) translateY(-50%) scale(0.95);
          }
        }

        @keyframes buttonPulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
        }
      `}</style>

      {/* Canvas容器 */}
      <div
        ref={containerRef}
        className="w-full h-full relative"
        style={{ minHeight: '400px' }}
      >
        {/* HTML Canvas元素 */}
        <canvas
          ref={canvasRef}
          className={`w-full h-full ${
            isDragging ? 'cursor-grabbing' :
            isPanningCanvas ? 'cursor-grabbing' :
            hoveredDesignId ? 'cursor-pointer' : 'cursor-grab'
          }`}
          style={{
            imageRendering: 'auto'
          }}
          onClick={handleCanvasClick}
          onMouseDown={handleCanvasMouseDown}
          onMouseMove={handleCanvasMouseMove}
          onMouseLeave={() => setHoveredDesignId(null)}
          onContextMenu={handleContextMenu}
          />

        {/* 拖拽提示 */}
        {isDragging && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 pointer-events-none">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">拖拽移动设计</span>
            </div>
          </div>
        )}

        {/* 缩放控制 */}
        <div className="absolute bottom-4 right-4 flex flex-col gap-2 z-40">
          <button
            onClick={handleZoomIn}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors border border-gray-200/50"
            title="放大"
          >
            <ZoomIn className="h-4 w-4 text-gray-700" />
          </button>
          <button
            onClick={handleZoomOut}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors border border-gray-200/50"
            title="缩小"
          >
            <ZoomOut className="h-4 w-4 text-gray-700" />
          </button>
          <button
            onClick={handleResetZoom}
            className="bg-white/90 hover:bg-white p-2 rounded-lg shadow-lg transition-colors border border-gray-200/50"
            title="重置缩放"
          >
            <RotateCcw className="h-4 w-4 text-gray-700" />
          </button>

          {/* 缩放比例显示 */}
          <div className="bg-white/90 px-3 py-1 rounded-lg shadow-lg border border-gray-200/50 text-xs text-gray-600 text-center w-12">
            {Math.round(canvasZoom * 100)}%
          </div>
        </div>

      </div>

      {/* 悬浮菜单 - 缩放时隐藏 */}
      {floatingMenu.visible && floatingMenu.designId && !isZooming && (
        <FloatingMenu
          x={floatingMenu.x}
          y={floatingMenu.y}
          designId={floatingMenu.designId}
          isClosing={floatingMenu.isClosing}
          onAIEdit={handleAIEdit}
          on3DPreview={handle3DPreview}
          onClose={closeFloatingMenu}
        />
      )}

      {/* 3D预览弹窗 */}
      {threeDPreview.isVisible && threeDPreview.designId && (
        <ThreeDPreviewModal
          designId={threeDPreview.designId}
          design={designs.find(d => d.id === threeDPreview.designId)}
          onClose={() => setThreeDPreview({ isVisible: false, designId: null, position: { x: 0, y: 0 } })}
        />
      )}
    </div>
  );
});

DesignCanvas.displayName = 'DesignCanvas';

// 辅助函数：寻找可用位置
function findAvailablePosition(
  existingDesigns: DesignItem[],
  canvasSize: { width: number; height: number },
  canvasConfig: any,
  preferredCenter?: { x: number; y: number }
): { x: number; y: number } {
  // 使用preferredCenter作为起始点，如果没有提供则使用画布中心
  const centerX = preferredCenter?.x || canvasSize.width / 2;
  const centerY = preferredCenter?.y || canvasSize.height / 2;

  const gridSize = 120; // 增大网格间距以适应更大的卡片
  const minDistance = 200; // 最小间距

  // 螺旋式搜索可用位置，从中心开始向外扩展
  const maxRadius = 10;
  for (let radius = 0; radius <= maxRadius; radius++) {
    const positions = [];

    if (radius === 0) {
      positions.push({ x: centerX, y: centerY });
    } else {
      // 在当前半径的圆周上生成位置
      const angleStep = Math.PI / 4; // 45度间隔
      for (let angle = 0; angle < 2 * Math.PI; angle += angleStep) {
        const x = centerX + radius * gridSize * Math.cos(angle);
        const y = centerY + radius * gridSize * Math.sin(angle);
        positions.push({ x, y });
      }
    }

    // 检查每个位置是否可用
    for (const pos of positions) {
      const hasConflict = existingDesigns.some(design => {
        const distance = Math.sqrt(
          Math.pow(design.position.x - pos.x, 2) +
          Math.pow(design.position.y - pos.y, 2)
        );
        return distance < minDistance;
      });

      if (!hasConflict) {
        return pos;
      }
    }
  }

  // 如果螺旋搜索失败，在中心附近随机放置
  const attempts = 20;
  for (let i = 0; i < attempts; i++) {
    const angle = Math.random() * 2 * Math.PI;
    const distance = Math.random() * 300 + 100; // 100-400像素范围
    const x = centerX + distance * Math.cos(angle);
    const y = centerY + distance * Math.sin(angle);

    const hasConflict = existingDesigns.some(design => {
      const distance = Math.sqrt(
        Math.pow(design.position.x - x, 2) +
        Math.pow(design.position.y - y, 2)
      );
      return distance < minDistance;
    });

    if (!hasConflict) {
      return { x, y };
    }
  }

  // 最后回退到preferredCenter或画布中心位置
  return {
    x: centerX,
    y: centerY
  };
}

export default DesignCanvas;

// 设计图片项组件
interface DesignImageItemProps {
  design: DesignItem;
  onSelect: (designId: string, event?: React.MouseEvent) => void;
  onDragStart: (designId: string, event: React.MouseEvent) => void;
  onContextMenu: (event: React.MouseEvent, designId: string) => void;
  on3DPreview: (designId: string, event: React.MouseEvent) => void;
  isDragging: boolean;
}

function DesignImageItem({
  design,
  onSelect,
  onDragStart,
  onContextMenu,
  on3DPreview,
  isDragging
}: DesignImageItemProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`absolute cursor-move transition-all duration-200 ${
        design.isSelected
          ? 'ring-2 ring-blue-500 ring-offset-2 shadow-lg'
          : isHovered
            ? 'ring-1 ring-gray-300 shadow-md'
            : 'shadow-sm'
      } ${isDragging ? 'z-50 shadow-2xl rotate-2' : 'z-10'} ${
        isHovered ? 'scale-105' : ''
      }`}
      style={{
        left: design.position.x - 120, // 图片宽度的一半
        top: design.position.y - 160,  // 图片高度的一半
        transform: `scale(${design.scale}) ${isDragging ? 'rotate(2deg)' : ''}`,
        transformOrigin: 'center'
      }}
      onClick={(e) => onSelect(design.id, e)}
      onMouseDown={(e) => onDragStart(design.id, e)}
      onContextMenu={(e) => onContextMenu(e, design.id)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative group">
        <Image
          src={design.image_url}
          alt={design.prompt || 'Design'}
          width={240}
          height={320}
          className="rounded-lg shadow-lg object-cover"
          draggable={false}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjMyMCIgdmlld0JveD0iMCAwIDI0MCAzMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyNDAiIGhlaWdodD0iMzIwIiBmaWxsPSIjRjlGQUZCIiByeD0iOCIvPgo8dGV4dCB4PSI1MCUiIHk9IjUwJSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0iIzlDQTNBRiIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0Ij7mnI3liqHorr7orqHlm748L3RleHQ+Cjwvc3ZnPg==';
          }}
        />
        
        {/* 悬停工具栏 */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={(e) => on3DPreview(design.id, e)}
            className="p-1.5 bg-white/90 hover:bg-white rounded-md shadow-sm transition-colors mr-1"
            title="3D预览"
          >
            <Maximize2 className="w-4 h-4 text-gray-600" />
          </button>
        </div>

        {/* 设计信息 */}
        {design.isSelected && (
          <div className="absolute -bottom-8 left-0 right-0 bg-white/95 backdrop-blur-sm rounded-md p-2 shadow-sm">
            <p className="text-xs text-gray-600 truncate">
              {design.prompt || `设计 ${design.id.slice(0, 8)}`}
            </p>
            <p className="text-xs text-gray-400">
              {design.timestamp.toLocaleTimeString()}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// 悬浮菜单组件
interface FloatingMenuProps {
  x: number;
  y: number;
  designId: string;
  isClosing?: boolean;
  onAIEdit: (designId: string) => void;
  on3DPreview: (designId: string) => void;
  onClose: () => void;
}

function FloatingMenu({ x, y, designId, isClosing = false, onAIEdit, on3DPreview, onClose }: FloatingMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleMenuClick = (action: string, event: React.MouseEvent) => {
    event.stopPropagation();

    switch (action) {
      case 'ai-edit':
        onAIEdit(designId);
        break;
      case '3d':
        on3DPreview(designId);
        break;
    }

    onClose();
  };

  return (
    <div
      ref={menuRef}
      className={`absolute z-50 bg-white/98 backdrop-blur-lg rounded-xl shadow-2xl border border-gray-200/50 flex flex-col gap-0.5 p-1.5 min-w-[140px] transition-all duration-300 ease-out ${
        isClosing ? 'animate-out slide-out-to-right-2 fade-out' : 'animate-in slide-in-from-right-2 fade-in'
      }`}
      style={{
        left: x,
        top: y,
        transform: 'translateY(-50%)', // 垂直居中对齐
        boxShadow: '0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
        animation: isClosing
          ? 'slideOutToRight 0.2s ease-in forwards'
          : 'slideInFromRight 0.3s ease-out forwards'
      }}
    >
      {/* AI改图按钮 */}
      <div
        className="group flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 transition-all duration-200 hover:shadow-sm cursor-pointer"
        onClick={(e) => handleMenuClick('ai-edit', e)}
      >
        <div className="flex items-center justify-center w-7 h-7 rounded-md text-gray-600 group-hover:text-blue-700 transition-all duration-200">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors duration-200 whitespace-nowrap select-none">
          AI改图
        </span>
      </div>

      {/* 分隔线 */}
      <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent my-1"></div>

      {/* 3D预览按钮 */}
      <div
        className="group flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 transition-all duration-200 hover:shadow-sm cursor-pointer"
        onClick={(e) => handleMenuClick('3d', e)}
      >
        <div className="flex items-center justify-center w-7 h-7 rounded-md text-gray-600 group-hover:text-blue-700 transition-all duration-200">
          <Maximize2 className="w-4 h-4" strokeWidth={2} />
        </div>
        <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors duration-200 whitespace-nowrap select-none">
          3D预览
        </span>
      </div>
    </div>
  );
}

// 3D预览模态框组件
interface ThreeDPreviewModalProps {
  designId: string;
  design?: DesignItem;
  onClose: () => void;
}

function ThreeDPreviewModal({ designId, design, onClose }: ThreeDPreviewModalProps) {
  if (!design) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">3D预览</h3>
            <p className="text-sm text-gray-500 truncate">
              {design.prompt || `设计 ${design.id.slice(0, 8)}`}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 3D视图区域 */}
        <div className="flex-1 relative">
          <ThreeDViewer
            designImage={design.image_url}
            conceptId={design.id}
          />
        </div>

        {/* 底部工具栏 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              生成时间: {design.timestamp.toLocaleString()}
            </div>
            <div className="flex gap-2">
              <button className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
                下载图片
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
