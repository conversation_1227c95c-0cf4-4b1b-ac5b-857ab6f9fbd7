/**
 * API client for backend communication
 * Follows Single Responsibility Principle - handles only API communication
 */

import axios, { AxiosInstance } from 'axios';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Canvas state types
export interface CanvasArtifactPosition {
  artifact_id: string;
  position_x: number;
  position_y: number;
  scale: number;
  rotation: number;
  z_index: number;
  is_selected: boolean;
  is_visible: boolean;
  metadata: Record<string, any>;
}

export interface CanvasStateData {
  artifacts: CanvasArtifactPosition[];
  view_state: {
    scale: number;
    offset_x: number;
    offset_y: number;
  };
  canvas_metadata: Record<string, any>;
}

export interface CanvasStateResponse {
  id: string;
  conversation_id: string;
  user_id: string;
  canvas_data: CanvasStateData;
  created_at: string;
  updated_at: string;
}

// Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
}

export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Conversation {
  id: string;
  user_id: string;
  title: string;
  status: string;
  meta_data: Record<string, any>;
  created_at: string;
  updated_at: string;
  messages?: ChatMessage[];
  design_concepts?: DesignConcept[];
}

export interface DesignRequest {
  id: string;
  conversation_id: string;
  description: string;
  category?: string;
  style?: string;
  colors: string[];
  materials: string[];
  occasion?: string;
  target_gender?: string;
  size?: string;
  additional_requirements?: string;
  created_at: string;
}

export interface DesignConcept {
  id: string;
  conversation_id: string;
  request_id?: string;
  image_url: string;
  prompt: string;
  ai_model: string;
  version: number;
  is_active: boolean;
  ai_metadata: Record<string, any>;
  created_at: string;
}

export interface ChatMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant';
  content: string;
  image_url?: string;
  message_metadata: Record<string, any>;
  created_at: string;
}

export interface Token {
  access_token: string;
  token_type: string;
  expires_in: number;
}

// API Client Class
class ApiClient {
  private client: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 300000,  // Reduce from 300s to 60s
      headers: {
        'Content-Type': 'application/json',
      },
      decompress: true,  // Enable response decompression
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearToken();
          // Redirect to login or handle unauthorized
        }
        return Promise.reject(error);
      }
    );

    // Load token from localStorage on initialization
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('access_token');
    }
  }

  // Token management
  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
    }
  }

  getToken(): string | null {
    return this.token;
  }

  // Auth endpoints
  async register(userData: {
    email: string;
    password: string;
    first_name?: string;
    last_name?: string;
  }): Promise<ApiResponse<{ user: User }>> {
    const response = await this.client.post('/api/v1/auth/register', userData);
    return response.data;
  }

  async login(email: string, password: string): Promise<Token> {
    const formData = new FormData();
    formData.append('username', email);
    formData.append('password', password);

    const response = await this.client.post('/api/v1/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    const token = response.data;
    this.setToken(token.access_token);
    return token;
  }

  async verifyToken(token: string): Promise<ApiResponse> {
    const response = await this.client.post('/api/v1/auth/verify-token', { token });
    return response.data;
  }

  // Conversation endpoints
  async createConversation(conversationData: {
    title: string;
    status?: string;
    meta_data?: Record<string, any>;
  }): Promise<Conversation> {
    const response = await this.client.post('/api/v1/conversations/', conversationData);
    return response.data;
  }

  async getConversations(skip = 0, limit = 100): Promise<Conversation[]> {
    const response = await this.client.get(`/api/v1/conversations/?skip=${skip}&limit=${limit}`);
    return response.data;
  }

  async getConversation(conversationId: string): Promise<Conversation> {
    const response = await this.client.get(`/api/v1/conversations/${conversationId}`);
    return response.data;
  }

  async updateConversation(conversationId: string, conversationData: {
    title?: string;
    status?: string;
    meta_data?: Record<string, any>;
  }): Promise<Conversation> {
    const response = await this.client.put(`/api/v1/conversations/${conversationId}`, conversationData);
    return response.data;
  }



  async render3D(conceptId: string, renderSettings: any): Promise<ApiResponse<any>> {
    const response = await this.client.post('/api/v1/design/render-3d', {
      concept_id: conceptId,
      render_settings: renderSettings
    });
    return response.data;
  }

  // Chat endpoints
  async sendChatMessage(conversationId: string, messageData: {
    role: 'user' | 'assistant';
    content: string;
    image_url?: string;
    message_metadata?: Record<string, any>;
  }): Promise<ChatMessage> {
    const response = await this.client.post(`/api/v1/conversations/${conversationId}/messages`, messageData);
    return response.data;
  }

  async getChatMessages(conversationId: string, skip = 0, limit = 100): Promise<ChatMessage[]> {
    const response = await this.client.get(`/api/v1/conversations/${conversationId}/messages?skip=${skip}&limit=${limit}`);
    return response.data;
  }

  // 统一对话处理接口 - 支持隐式会话创建
  async processConversation(conversationId: string, conversationData: {
    content: string;
    conversation_id?: string;
    message_type?: string;
    requirements?: any;
    edit_image_url?: string;
    edit_image_id?: string;
  }): Promise<ApiResponse<any>> {
    // 使用新的统一接口 /api/v1/conversations/process
    // 如果有conversationId则包含在请求中，否则不传
    const requestData: any = {
      content: conversationData.content,
      message_type: conversationData.message_type || 'user'
    };
    
    // 只有在有值的时候才添加可选参数
    if (conversationId && conversationId !== 'new') {
      requestData.conversation_id = conversationId;
    }
    if (conversationData.requirements) {
      requestData.requirements = conversationData.requirements;
    }
    if (conversationData.edit_image_url) {
      requestData.edit_image_url = conversationData.edit_image_url;
    }
    if (conversationData.edit_image_id) {
      requestData.edit_image_id = conversationData.edit_image_id;
    }
    
    const response = await this.client.post('/api/v1/conversations/process', requestData);
    return response.data;
  }

  
  // 生成会话ID接口 - 用于新对话
  async generateConversationId(): Promise<ApiResponse<{ conversation_id: string }>> {
    const response = await this.client.post('/api/v1/conversations/generate-id');
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; service: string; version: string; environment: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }

  // 获取会话的设计产物
  async getConversationArtifacts(conversationId: string): Promise<ApiResponse<{
    conversation_id: string;
    artifacts: Array<{
      id: string;
      task_id: string;
      artifact_type: string;
      artifact_name: string;
      file_url: string;
      file_path?: string;
      file_size?: number;
      mime_type?: string;
      metadata?: Record<string, any>;
      created_at: string;
      is_temporary: boolean;
      expires_at?: string;
    }>;
    total_artifacts: number;
  }>> {
    const response = await this.client.get(`/api/v1/tasks/conversation/${conversationId}/artifacts`);
    return response.data;
  }

  // Canvas state methods
  async saveCanvasState(conversationId: string, canvasData: CanvasStateData): Promise<CanvasStateResponse> {
    const response = await this.client.post(`/api/v1/canvas/state/${conversationId}`, canvasData);
    return response.data;
  }

  async getCanvasState(conversationId: string): Promise<CanvasStateResponse> {
    const response = await this.client.get(`/api/v1/canvas/state/${conversationId}`);
    return response.data;
  }

  async updateCanvasState(conversationId: string, canvasData: CanvasStateData): Promise<CanvasStateResponse> {
    const response = await this.client.put(`/api/v1/canvas/state/${conversationId}`, canvasData);
    return response.data;
  }

  async deleteCanvasState(conversationId: string): Promise<void> {
    await this.client.delete(`/api/v1/canvas/state/${conversationId}`);
  }

  async clearCanvasState(conversationId: string): Promise<void> {
    await this.client.post(`/api/v1/canvas/state/${conversationId}/clear`);
  }

  async getCanvasArtifacts(conversationId: string): Promise<any[]> {
    const response = await this.client.get(`/api/v1/canvas/state/${conversationId}/artifacts`);
    return response.data;
  }

  async updateArtifactPosition(
    conversationId: string, 
    artifactId: string, 
    positionData: Partial<CanvasArtifactPosition>
  ): Promise<any> {
    const response = await this.client.put(`/api/v1/canvas/state/${conversationId}/artifact/${artifactId}`, positionData);
    return response.data;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export default
export default apiClient;
