# 任务管理系统 (Task Management System)

## 概述

为了解决原有系统中任务数据丢失的问题，我们实现了一个完整的任务管理系统，支持多Agent协作、任务持久化和状态追踪。

## 数据库表结构

### 核心表

1. **tasks** - 任务主表
   - 存储任务基本信息、状态、进度等
   - 支持任务嵌套（父子任务）
   - 关联到具体会话

2. **agent_executions** - Agent执行记录表
   - 记录每个Agent的执行情况
   - 包含输入输出、重试次数等
   - 支持执行顺序控制

3. **task_dependencies** - 任务依赖关系表
   - 定义任务间的依赖关系
   - 支持多种依赖类型
   - 确保任务执行顺序

4. **task_artifacts** - 任务产物表
   - 管理任务产生的文件和输出
   - 支持临时文件和过期清理
   - 按类型分类管理

5. **workflow_templates** - 工作流模板表
   - 预定义的Agent协作模式
   - 支持版本控制
   - 可复用的工作流定义

6. **task_queue** - 任务队列表
   - 分布式任务处理支持
   - 优先级调度和重试机制
   - 工作节点负载均衡

### 更新的现有表

1. **conversations** - 会话表
   - 新增 `current_task_id` 字段
   - 新增 `workflow_state` 字段
   - 关联到当前执行的任务

2. **chat_messages** - 聊天消息表
   - 新增 `task_id` 字段
   - 新增 `related_artifact_ids` 字段
   - 关联到具体任务和产物

## 主要功能

### 1. 任务生命周期管理
- **创建**: 从用户输入或工作流模板创建任务
- **执行**: 多Agent协作执行任务
- **追踪**: 实时进度和状态更新
- **完成**: 结果保存和产物管理

### 2. 多Agent协作
- **模板驱动**: 预定义的工作流模板
- **依赖管理**: Agent间的执行依赖
- **并行执行**: 支持并行和串行执行
- **错误处理**: 重试机制和错误恢复

### 3. 任务持久化
- **状态保存**: 任务状态实时保存到数据库
- **结果存储**: Agent执行结果完整记录
- **产物管理**: 文件和输出统一管理
- **历史追踪**: 完整的任务执行历史

### 4. 工作流模板
- **短剧创作**: 编剧→导演→美术→制作→后期
- **服装设计**: 设计→生成→优化
- **Logo设计**: 概念→生成→风格化

## API接口

### 任务管理
- `POST /api/v1/tasks/` - 创建任务
- `GET /api/v1/tasks/{task_id}` - 获取任务详情
- `GET /api/v1/tasks/conversation/{conversation_id}` - 获取会话的任务列表
- `POST /api/v1/tasks/workflow` - 从工作流模板创建任务

### 任务状态更新
- `PATCH /api/v1/tasks/{task_id}/progress` - 更新任务进度
- `PATCH /api/v1/tasks/{task_id}/status` - 更新任务状态

### Agent执行管理
- `POST /api/v1/tasks/{task_id}/executions` - 创建Agent执行记录
- `GET /api/v1/tasks/queue/pending` - 获取待处理任务

### 产物管理
- `POST /api/v1/tasks/{task_id}/artifacts` - 创建任务产物
- `DELETE /api/v1/tasks/artifacts/cleanup` - 清理临时文件

## 使用示例

### 1. 创建短剧创作任务
```python
from src.models.schemas import TaskWorkflowRequest
from src.services.task_service import TaskManagementService

# 创建工作流请求
workflow_request = TaskWorkflowRequest(
    conversation_id="conversation_uuid",
    task_type="short_film",
    name="职场趣事短剧",
    description="制作一个关于办公室幽默的短剧",
    input_parameters={
        "topic": "职场趣事",
        "duration": 180,
        "style": "轻松幽默"
    },
    use_template=True
)

# 创建任务
service = TaskManagementService(db)
response = await service.create_task_from_workflow(workflow_request)
```

### 2. 更新任务进度
```python
from src.models.schemas import TaskProgressUpdate

progress_update = TaskProgressUpdate(
    task_id="task_uuid",
    progress=50.0,
    status="in_progress",
    message="正在生成角色设计",
    output_data={"current_agent": "art_agent", "step": 3}
)

await service.update_task_progress(progress_update)
```

### 3. 创建任务产物
```python
from src.models.schemas import TaskArtifactCreate

artifact = TaskArtifactCreate(
    task_id="task_uuid",
    agent_execution_id="execution_uuid",
    artifact_type="image",
    artifact_name="角色设计图",
    file_url="https://example.com/character_design.png",
    file_size=1024000,
    mime_type="image/png",
    metadata={"character": "主角", "style": "现代"},
    is_temporary=False
)

await service.create_task_artifact(artifact)
```

## 数据库迁移

运行迁移脚本创建新表：

```bash
python migrate_task_tables_simple.py
```

## 集成指南

### 1. 更新现有服务
在 `conversation_service.py` 中集成任务管理：

```python
from src.services.task_service import TaskManagementService

class ConversationService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.task_service = TaskManagementService(db)
    
    async def process_conversation(self, user_input: str, user_id: str, conversation_id: str = None):
        # 创建任务而不是直接处理
        workflow_request = TaskWorkflowRequest(
            conversation_id=conversation_id,
            task_type="fashion_design",
            name=user_input[:50],
            input_parameters={"user_input": user_input}
        )
        
        return await self.task_service.create_task_from_workflow(workflow_request)
```

### 2. 更新前端API
在前端API客户端中添加任务相关方法：

```typescript
// 创建任务
async createTask(taskData: TaskWorkflowRequest): Promise<TaskWorkflowResponse> {
  return await this.post<TaskWorkflowResponse>('/tasks/workflow', taskData);
}

// 获取任务详情
async getTask(taskId: string): Promise<TaskWithDetailsResponse> {
  return await this.get<TaskWithDetailsResponse>(`/tasks/${taskId}`);
}

// 更新任务进度
async updateTaskProgress(taskId: string, progress: number, status?: string): Promise<APIResponse> {
  return await this.patch<APIResponse>(`/tasks/${taskId}/progress`, {
    task_id: taskId,
    progress,
    status
  });
}
```

### 3. WebSocket状态更新
通过WebSocket实时推送任务状态：

```python
# 在任务状态更新时发送WebSocket消息
async def _broadcast_task_status(self, task_id: str, status: dict):
    message = {
        "type": "task_status_update",
        "task_id": task_id,
        "data": status
    }
    await self.websocket_manager.send_to_conversation(
        task.conversation_id, 
        message
    )
```

## 优势

1. **数据持久化**: 任务状态和结果不再丢失
2. **可追踪性**: 完整的任务执行历史
3. **可扩展性**: 支持复杂的Agent协作模式
4. **可维护性**: 清晰的任务管理架构
5. **可靠性**: 重试机制和错误处理
6. **性能**: 任务队列和并行处理支持

## 后续扩展

1. **任务调度器**: 实现后台任务处理
2. **监控面板**: 任务执行状态可视化
3. **性能优化**: 批量处理和缓存机制
4. **工作流编辑器**: 可视化工作流设计
5. **Agent市场**: 第三方Agent集成