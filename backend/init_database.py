#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database initialization script.

Creates database tables and adds a demo user account.

Demo Account:
- Email: <EMAIL>
- Password: demo123456
- Name: Demo User
- Status: Active and Verified

Usage:
    python init_database.py
"""

import asyncio
import logging
from uuid import uuid4
from passlib.context import Crypt<PERSON>ontext

from src.core.database import db_manager
from src.models.database import User, Base
from sqlalchemy.orm import Session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Password context for hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_demo_user(session: Session) -> None:
    """Create demo user account."""
    try:
        # Check if demo user already exists
        existing_user = session.query(User).filter(User.email == "<EMAIL>").first()
        
        if existing_user:
            logger.info("Demo user already exists")
            return
        
        # Hash the demo password
        hashed_password = pwd_context.hash("demo123456")
        
        # Create demo user
        demo_user = User(
            id=str(uuid4()),
            email="<EMAIL>",
            password_hash=hashed_password,
            first_name="Demo",
            last_name="User",
            is_active=True,
            is_verified=True
        )
        
        session.add(demo_user)
        session.commit()
        
        logger.info("Demo user created successfully")
        logger.info("Email: <EMAIL>")
        logger.info("Password: demo123456")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Error creating demo user: {e}")
        raise

def init_database() -> None:
    """Initialize database with demo user."""
    try:
        logger.info("Initializing database...")
        
        # Initialize database manager
        db_manager.initialize()
        
        # Create tables
        logger.info("Creating database tables...")
        db_manager.create_tables()
        
        # Create demo user using sync session
        logger.info("Creating demo user...")
        session = db_manager.get_session()
        create_demo_user(session)
        session.close()
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

async def init_database_async() -> None:
    """Initialize database using async session."""
    try:
        logger.info("Initializing database (async)...")
        
        # Initialize database manager
        db_manager.initialize()
        
        # Create tables
        logger.info("Creating database tables...")
        db_manager.create_tables()
        
        # Create demo user using async session
        logger.info("Creating demo user...")
        async with db_manager.get_async_session_context() as session:
            # Check if demo user already exists
            from sqlalchemy import select
            result = await session.execute(select(User).where(User.email == "<EMAIL>"))
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                logger.info("Demo user already exists")
                return
            
            # Hash the demo password
            hashed_password = pwd_context.hash("demo123456")
            
            # Create demo user
            demo_user = User(
                id=str(uuid4()),
                email="<EMAIL>",
                password_hash=hashed_password,
                first_name="Demo",
                last_name="User",
                is_active=True,
                is_verified=True
            )
            
            session.add(demo_user)
            await session.commit()
            
            logger.info("Demo user created successfully")
            logger.info("Email: <EMAIL>")
            logger.info("Password: demo123456")
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

if __name__ == "__main__":
    # Use sync version for simplicity
    init_database()