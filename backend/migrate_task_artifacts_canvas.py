"""
Database migration to add canvas columns to task_artifacts table.
This script adds the missing canvas-related fields to the task_artifacts table.
"""

import sqlite3
import json
from datetime import datetime

def migrate_database():
    """Add canvas columns to task_artifacts table."""
    
    # Connect to the database
    conn = sqlite3.connect('fuzhuang.db')
    cursor = conn.cursor()
    
    print("Starting task_artifacts canvas columns migration...")
    
    # Add canvas columns to task_artifacts table
    columns_to_add = [
        ('canvas_position_x', 'INTEGER DEFAULT 0'),
        ('canvas_position_y', 'INTEGER DEFAULT 0'),
        ('canvas_scale', 'REAL DEFAULT 1.0'),
        ('canvas_is_selected', 'BOOLEAN DEFAULT 0'),
        ('canvas_z_index', 'INTEGER DEFAULT 0')
    ]
    
    for column_name, column_def in columns_to_add:
        # Check if column already exists
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM pragma_table_info('task_artifacts') 
            WHERE name = '{column_name}'
        """)
        
        if cursor.fetchone()[0] == 0:
            print(f"Adding column: {column_name}")
            cursor.execute(f"""
                ALTER TABLE task_artifacts 
                ADD COLUMN {column_name} {column_def}
            """)
        else:
            print(f"Column {column_name} already exists")
    
    # Commit the changes
    conn.commit()
    
    print("Task_artifacts canvas columns migration completed successfully!")
    
    # Verify the columns were added
    cursor.execute("PRAGMA table_info(task_artifacts)")
    columns = cursor.fetchall()
    print(f"Table now has {len(columns)} columns:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # Close the connection
    conn.close()

if __name__ == "__main__":
    migrate_database()