"""
Database migration to add canvas state tables.
This script creates the canvas_states and canvas_artifacts tables.
"""

import sqlite3
import json
from datetime import datetime

def migrate_database():
    """Add canvas state tables to the database."""
    
    # Connect to the database
    conn = sqlite3.connect('fuzhuang.db')
    cursor = conn.cursor()
    
    print("Starting canvas state database migration...")
    
    # Create canvas_states table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS canvas_states (
            id TEXT PRIMARY KEY,
            conversation_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            canvas_data TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations (id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            UNIQUE (conversation_id, user_id)
        )
    ''')
    
    # Create canvas_artifacts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS canvas_artifacts (
            id TEXT PRIMARY KEY,
            canvas_state_id TEXT NOT NULL,
            artifact_id TEXT NOT NULL,
            position_x INTEGER NOT NULL DEFAULT 0,
            position_y INTEGER NOT NULL DEFAULT 0,
            scale REAL NOT NULL DEFAULT 1.0,
            rotation REAL NOT NULL DEFAULT 0.0,
            z_index INTEGER NOT NULL DEFAULT 0,
            is_selected BOOLEAN NOT NULL DEFAULT 0,
            is_visible BOOLEAN NOT NULL DEFAULT 1,
            canvas_metadata TEXT DEFAULT '{}',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (canvas_state_id) REFERENCES canvas_states (id),
            FOREIGN KEY (artifact_id) REFERENCES task_artifacts (id),
            UNIQUE (canvas_state_id, artifact_id)
        )
    ''')
    
    # Create indexes for better performance
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_canvas_states_conversation 
        ON canvas_states (conversation_id)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_canvas_states_user 
        ON canvas_states (user_id)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_canvas_artifacts_state 
        ON canvas_artifacts (canvas_state_id)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_canvas_artifacts_artifact 
        ON canvas_artifacts (artifact_id)
    ''')
    
    # Commit the changes
    conn.commit()
    
    print("Canvas state tables created successfully!")
    
    # Verify the tables were created
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('canvas_states', 'canvas_artifacts')")
    tables = cursor.fetchall()
    print(f"Created tables: {[table[0] for table in tables]}")
    
    # Close the connection
    conn.close()
    
    print("Canvas state database migration completed successfully!")

if __name__ == "__main__":
    migrate_database()