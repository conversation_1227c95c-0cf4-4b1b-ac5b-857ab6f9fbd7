## Development

### Code Style
- Follow PEP 8 guidelines
- Use type hints
- Document functions and classes
- Write unit tests

### Testing
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=src
```

### Database Management
数据库表会在应用启动时自动创建。如需重置数据库，删除 `fuzhuang.db` 文件即可。

### Demo Account
系统预置了一个演示账户，方便测试使用：

- **邮箱**: <EMAIL>
- **密码**: demo123456
- **姓名**: Demo User
- **状态**: 已激活并验证

运行初始化脚本创建演示账户：
```bash
python init_database.py
```

## Production Deployment

1. **Environment Setup**
   - Use PostgreSQL instead of SQLite
   - Set proper environment variables
   - Configure CORS for production domains

2. **Docker Deployment**
   ```bash
   docker build -t fashion-ai-backend .
   docker run -p 8000:8000 fashion-ai-backend
   ```

3. **Security Considerations**
   - Use strong SECRET_KEY
   - Enable HTTPS
   - Configure proper CORS origins
   - Set up rate limiting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
