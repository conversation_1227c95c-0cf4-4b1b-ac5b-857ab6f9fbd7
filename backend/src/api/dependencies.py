"""
API dependencies for dependency injection.
Follows Dependency Inversion Principle.
"""

from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db

from ..services.auth_service import AuthService
from ..services.workflow import FashionWorkflow
from ..services.design_service import (
    ChatService,
    DesignConceptService,
)
from ..services.user_service import UserService
from ..services.agent_factory import AgentRouter

# Security
security = HTTPBearer()


async def get_user_service(db: AsyncSession = Depends(get_db)) -> UserService:
    """Get user service dependency."""
    return UserService(db)


async def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """Get auth service dependency."""
    return AuthService(db)




async def get_design_concept_service(db: AsyncSession = Depends(get_db)) -> DesignConceptService:
    """Get design concept service dependency."""
    return DesignConceptService(db)


async def get_chat_service(db: AsyncSession = Depends(get_db)) -> ChatService:
    """Get chat service dependency."""
    return ChatService(db)



async def get_fashion_workflow() -> FashionWorkflow:
    """Get fashion design workflow dependency."""
    return FashionWorkflow()


# Global agent router instance (singleton pattern)
_agent_router_instance = None

async def get_agent_router() -> AgentRouter:
    """Get agent router dependency (singleton)."""
    global _agent_router_instance
    if _agent_router_instance is None:
        _agent_router_instance = AgentRouter()
    return _agent_router_instance


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> UUID:
    """Get current authenticated user ID."""
    token = credentials.credentials
    user_id = await auth_service.verify_token(token)

    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user_id


# Multi-Agent System Dependencies
async def get_agent_router() -> AgentRouter:
    """Get agent router dependency."""
    return AgentRouter()