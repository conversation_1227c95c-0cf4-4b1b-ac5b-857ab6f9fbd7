"""
Canvas state API routes for managing design canvas persistence.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db
from ..api.dependencies import get_current_user_id
from uuid import UUID
from ..services.canvas_service import CanvasStateService
from ..services.websocket_manager import get_design_broadcaster
from ..models.schemas_canvas import (
    CanvasStateCreate, CanvasStateUpdate, CanvasStateResponse,
    CanvasArtifactCreate, CanvasArtifactUpdate, CanvasArtifactResponse,
    CanvasStateData, CanvasArtifactPosition
)

router = APIRouter(prefix="/canvas", tags=["canvas"])


@router.post("/state/{conversation_id}", response_model=CanvasStateResponse)
async def save_canvas_state(
    conversation_id: str,
    canvas_data: CanvasStateData,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Save or update canvas state for a conversation."""

    try:
        print(f"🔧 Canvas API: 开始保存画布状态")
        print(f"📝 conversation_id: {conversation_id}")
        print(f"👤 user_id: {current_user_id}")
        print(f"📊 canvas_data: {canvas_data.model_dump()}")

        service = CanvasStateService(db)
        canvas_state = await service.create_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id),
            canvas_data=canvas_data
        )

        print(f"✅ Canvas API: 画布状态保存成功")

        # 发送WebSocket通知
        design_broadcaster = get_design_broadcaster()
        await design_broadcaster.send_canvas_state_update(
            conversation_id=conversation_id,
            user_id=str(current_user_id),
            canvas_data=canvas_data.model_dump()
        )

        return service.to_response(canvas_state)
    except Exception as e:
        print(f"❌ Canvas API: 保存画布状态失败: {e}")
        print(f"❌ Exception type: {type(e)}")
        import traceback
        print(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/state/{conversation_id}", response_model=CanvasStateResponse)
async def get_canvas_state(
    conversation_id: str,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Get canvas state for a conversation."""
    
    try:
        service = CanvasStateService(db)
        canvas_state = await service.get_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id)
        )
        
        if not canvas_state:
            # 返回空的画布状态而不是404错误
            from ..models.schemas_canvas import CanvasStateData
            from datetime import datetime

            now = datetime.utcnow()
            empty_canvas_data = CanvasStateData(
                artifacts=[],
                view_state={
                    "scale": 1.0,
                    "offset_x": 0.0,
                    "offset_y": 0.0
                },
                canvas_metadata={
                    "version": "1.0",
                    "total_artifacts": 0,
                    "created_at": now.isoformat()
                }
            )
            return CanvasStateResponse(
                id="empty",
                conversation_id=conversation_id,
                user_id=str(current_user_id),
                canvas_data=empty_canvas_data,
                created_at=now,
                updated_at=now
            )

        return service.to_response(canvas_state)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/state/{conversation_id}", response_model=CanvasStateResponse)
async def update_canvas_state(
    conversation_id: str,
    canvas_data: CanvasStateData,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Update canvas state for a conversation."""
    
    try:
        service = CanvasStateService(db)
        
        # Get existing canvas state
        existing_state = await service.get_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id)
        )

        if not existing_state:
            # Create new state if it doesn't exist
            canvas_state = await service.create_canvas_state(
                conversation_id=conversation_id,
                user_id=str(current_user_id),
                canvas_data=canvas_data
            )
        else:
            # Update existing state
            canvas_state = await service.update_canvas_state(
                canvas_state_id=existing_state.id,
                canvas_data=canvas_data
            )
        
        # 发送WebSocket通知
        design_broadcaster = get_design_broadcaster()
        await design_broadcaster.send_canvas_state_update(
            conversation_id=conversation_id,
            user_id=str(current_user_id),
            canvas_data=canvas_data.model_dump()
        )
        
        return service.to_response(canvas_state)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/state/{conversation_id}")
async def delete_canvas_state(
    conversation_id: str,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Delete canvas state for a conversation."""
    
    try:
        service = CanvasStateService(db)
        canvas_state = service.get_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id)
        )
        
        if not canvas_state:
            raise HTTPException(status_code=404, detail="Canvas state not found")
        
        success = service.delete_canvas_state(canvas_state.id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete canvas state")
        
        return {"message": "Canvas state deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/state/{conversation_id}/clear")
async def clear_canvas_state(
    conversation_id: str,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Clear canvas state for a conversation."""
    
    try:
        service = CanvasStateService(db)
        success = service.clear_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id)
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Canvas state not found")
        
        return {"message": "Canvas state cleared successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/state/{conversation_id}/artifacts", response_model=List[CanvasArtifactResponse])
async def get_canvas_artifacts(
    conversation_id: str,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Get all artifacts for a canvas state."""
    
    try:
        service = CanvasStateService(db)
        canvas_state = service.get_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id)
        )
        
        if not canvas_state:
            raise HTTPException(status_code=404, detail="Canvas state not found")
        
        artifacts = service.get_canvas_artifacts(canvas_state.id)
        return [service.artifact_to_response(artifact) for artifact in artifacts]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/state/{conversation_id}/artifact/{artifact_id}", response_model=CanvasArtifactResponse)
async def update_artifact_position(
    conversation_id: str,
    artifact_id: str,
    position_data: CanvasArtifactUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Update position and properties of a specific artifact."""
    
    try:
        service = CanvasStateService(db)
        canvas_state = service.get_canvas_state(
            conversation_id=conversation_id,
            user_id=str(current_user_id)
        )
        
        if not canvas_state:
            raise HTTPException(status_code=404, detail="Canvas state not found")
        
        artifact = service.update_artifact_position(
            canvas_state_id=canvas_state.id,
            artifact_id=artifact_id,
            position_data=position_data
        )
        
        if not artifact:
            raise HTTPException(status_code=404, detail="Artifact not found")
        
        return service.artifact_to_response(artifact)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))