"""
Conversation API routes.
Follows Single Responsibility Principle - handles conversation-related endpoints.
"""

import logging
from typing import List, Optional, Dict
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status

from ..models.schemas import (
    APIResponse,
    ChatMessageResponse,
    ConversationCreate,
    ConversationResponse,
    ConversationUpdate,
)

from ..services.conversation_service import ConversationService
from .dependencies import (
    get_current_user_id,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/conversations", tags=["conversations"])


# Conversation endpoints - 移除显式创建接口，实现隐式创建
# 对话将在首次发送消息时自动创建


@router.get("/", response_model=List[ConversationResponse])
async def get_user_conversations(
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id),
):
    """Get user's conversations."""
    try:
        # Fetch conversations from database using conversation service
        from ..services.conversation_service import ConversationService
        
        conversation_service = ConversationService()
        conversations = await conversation_service.get_user_conversations(
            user_id=str(current_user_id),
            skip=skip,
            limit=limit
        )
        
        return conversations
    except Exception as e:
        logger.error(f"Error getting user conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversations"
        )


@router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
):
    """Get a specific conversation."""
    try:
        # Fetch conversation from database using conversation service
        from ..services.conversation_service import ConversationService
        
        conversation_service = ConversationService()
        conversation = await conversation_service.get_conversation(
            conversation_id=str(conversation_id),
            user_id=str(current_user_id)
        )
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        return conversation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation"
        )


@router.put("/{conversation_id}", response_model=ConversationResponse)
async def update_conversation(
    conversation_id: UUID,
    conversation_data: ConversationUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
):
    """Update a conversation."""
    try:
        # For now, return a placeholder
        # In a real implementation, this would update in database
        conversation = {
            "id": conversation_id,
            "title": conversation_data.title or "Updated Conversation",
            "status": conversation_data.status or "active",
            "meta_data": conversation_data.meta_data or {},
            "user_id": current_user_id,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }
        return conversation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update conversation"
        )


@router.delete("/{conversation_id}", response_model=APIResponse)
async def delete_conversation(
    conversation_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
):
    """Delete a conversation."""
    try:
        # For now, just return success
        # In a real implementation, this would delete from database
        return APIResponse(
            success=True,
            message="Conversation deleted successfully"
        )
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )


@router.post("/generate-id", response_model=APIResponse)
async def generate_conversation_id(
    current_user_id: UUID = Depends(get_current_user_id),
):
    """
    生成会话ID接口 - 用于新对话
    
    前端在创建新对话时，可以预先调用此接口获取一个唯一的会话ID，
    然后在发送消息时使用该ID。这确保了每次对话都有唯一标识符。
    
    返回格式：
    {
        "success": true,
        "data": {
            "conversation_id": "uuid-string"
        },
        "message": "Conversation ID generated successfully"
    }
    """
    try:
        import uuid
        from ..core.database import db_manager
        from ..models.database import Conversation
        
        # 生成唯一的会话ID
        conversation_id = str(uuid.uuid4())
        
        # 在数据库中创建对话记录
        async with db_manager.get_async_session_context() as session:
            conversation = Conversation(
                id=conversation_id,
                user_id=str(current_user_id),
                title="新对话",
                status="active",
                meta_data={"created_by": "generate_id"}
            )
            session.add(conversation)
            await session.commit()
            
        logger.info(f"Created conversation with ID: {conversation_id} for user: {current_user_id}")
        
        return APIResponse(
            success=True,
            data={"conversation_id": conversation_id},
            message="Conversation ID generated and created successfully"
        )
        
    except Exception as e:
        logger.error(f"Error generating conversation ID: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate conversation ID"
        )


# Chat endpoints for conversations
@router.get("/{conversation_id}/messages", response_model=List[ChatMessageResponse])
async def get_conversation_messages(
    conversation_id: UUID,
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id),
):
    """Get chat messages for a conversation."""
    try:
        # Fetch messages from database using conversation service
        from ..services.conversation_service import ConversationService
        
        conversation_service = ConversationService()
        messages = await conversation_service.get_conversation_messages(
            conversation_id=str(conversation_id),
            skip=skip,
            limit=limit
        )
        
        return messages
    except Exception as e:
        logger.error(f"Error getting conversation messages: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation messages"
        )


@router.post("/{conversation_id}/messages", response_model=ChatMessageResponse)
async def send_chat_message(
    conversation_id: UUID,
    message_data: dict,
    current_user_id: UUID = Depends(get_current_user_id),
):
    """Send a chat message to a conversation."""
    try:
        from ..models.schemas import ChatMessageCreate
        from ..core.database import db_manager
        from ..services.design_service import ChatService
        
        # Save message to database
        async with db_manager.get_async_session_context() as session:
            chat_service = ChatService(session)
            
            # Create message data
            message_create = ChatMessageCreate(
                role=message_data.get("role", "user"),
                content=message_data.get("content", ""),
                image_url=message_data.get("image_url"),
                message_metadata=message_data.get("message_metadata", {})
            )
            
            # Save to database
            saved_message = await chat_service.create_message(str(conversation_id), message_create)
            
            # Return the saved message
            return {
                "id": saved_message.id,
                "conversation_id": conversation_id,
                "role": saved_message.role,
                "content": saved_message.content,
                "image_url": saved_message.image_url,
                "message_metadata": saved_message.message_metadata,
                "created_at": saved_message.created_at,
            }
            
    except Exception as e:
        logger.error(f"Error sending chat message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send chat message"
        )


# 统一对话处理接口 - 主要接口，支持隐式对话创建和任务管理
@router.post("/process", response_model=APIResponse)
async def process_conversation_unified(
    request: dict,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    统一对话处理接口 - 支持隐式对话创建和任务管理
    
    这是主要接口，前端只需要调用这个接口即可：
    - 不需要预先创建会话
    - 不需要提供conversation_id
    - 自动处理会话创建和消息发送
    - 集成任务管理系统进行智能编排
    - 支持新会话和现有会话的统一处理
    
    请求参数：
    - content: 用户输入内容（必需）
    - conversation_id: 可选，如果不提供将自动创建新会话
    - message_type: 消息类型（默认为"user"）
    - requirements: 设计需求（可选）
    - edit_image_url: 编辑图片URL（可选）
    - edit_image_id: 编辑图片ID（可选）
    
    系统会自动：
    - 创建智能任务并进行分析
    - 启动多Agent协作编排
    - 返回任务状态和执行进度
    """
    try:
        # 添加请求大小限制
        if len(str(request)) > 1000000:  # 1MB限制
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Request too large"
            )
        
        # 提取请求参数
        user_input = request.get("content", "")
        message_type = request.get("message_type", "user")
        requirements = request.get("requirements")
        edit_image_url = request.get("edit_image_url")
        edit_image_id = request.get("edit_image_id")
        conversation_id = request.get("conversation_id")  # 可选，如果不提供将自动创建

        if not user_input:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content is required"
            )

        # 使用任务管理系统处理对话
        return await _process_with_task_management(
            user_input=user_input,
            user_id=str(current_user_id),
            conversation_id=conversation_id,
            message_type=message_type,
            requirements=requirements,
            edit_image_url=edit_image_url,
            edit_image_id=edit_image_id
        )

    except Exception as e:
        logger.error(f"Error processing conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process conversation"
        )




# Unified Design Conversation Interface - for conversation-based design (保持向后兼容)
@router.post("/{conversation_id}/conversation", response_model=APIResponse)
async def process_conversation(
    conversation_id: UUID,
    request: dict,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    统一的设计对话接口 - 基于会话的版本
    """
    try:
        # 添加请求大小限制
        if len(str(request)) > 1000000:  # 1MB限制
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Request too large"
            )
        
        # 提取请求参数
        user_input = request.get("content", "")
        message_type = request.get("message_type", "user")
        requirements = request.get("requirements")
        edit_image_url = request.get("edit_image_url")
        edit_image_id = request.get("edit_image_id")

        if not user_input:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content is required"
            )

        # 调用统一的对话处理服务
        conversation_service = ConversationService()

        result = await conversation_service.process_conversation(
            user_input=user_input,
            user_id=str(current_user_id),
            conversation_id=str(conversation_id),
            message_type=message_type,
            requirements=requirements,
            edit_image_url=edit_image_url,
            edit_image_id=edit_image_id
        )

        return APIResponse(
            success=True,
            data={
                "status": result.get("status"),
                "message": result.get("message"),
                "result": result.get("result")
            },
            message="Conversation processed successfully"
        )

    except Exception as e:
        logger.error(f"Error processing conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process conversation"
        )
# 辅助方法：使用任务管理系统处理对话
async def _process_with_task_management(
    user_input: str,
    user_id: str,
    conversation_id: Optional[str],
    message_type: str,
    requirements: Optional[Dict],
    edit_image_url: Optional[str],
    edit_image_id: Optional[str]
) -> APIResponse:
    """使用任务管理系统处理对话"""
    
    try:
        from ..core.database import db_manager
        from ..services.task_orchestration_service import TaskOrchestrationService
        from ..services.conversation_service import ConversationService
        import uuid
        
        # 如果没有conversation_id，生成一个
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
        
        # 获取数据库会话
        async with db_manager.get_async_session_context() as session:
            # 创建任务编排服务
            task_orchestration_service = TaskOrchestrationService(session)
            
            # 创建智能任务
            task_response = await task_orchestration_service.create_intelligent_task(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                task_type="intelligent_design",  # 智能设计任务
                priority=5,
                message_type=message_type,
                requirements=requirements,
                edit_image_url=edit_image_url,
                edit_image_id=edit_image_id
            )
            
            # 注意：不再调用conversation_service.process_conversation()，避免重复执行
            # 任务编排服务已经完成了所有必要的处理，包括Agent执行和结果生成
            conversation_result = {
                "status": "completed",
                "message": "Task processing completed through orchestration service",
                "result": task_response.output_results if hasattr(task_response, 'output_results') else None
            }
            
            return APIResponse(
                success=True,
                data={
                    "status": task_response.status,
                    "message": "Task completed successfully" if task_response.status == "completed" else "Task processing completed",
                    "conversation_id": conversation_id,
                    "task_id": str(task_response.task_id),
                    "task_status": task_response.status,
                    "estimated_agents": task_response.estimated_agents,
                    "estimated_duration": task_response.estimated_duration,
                    "conversation_result": conversation_result.get("result"),
                    "task_result": task_response.output_results if hasattr(task_response, 'output_results') else None,
                    "processing_mode": "intelligent_task_orchestration_synchronous"
                },
                message="Conversation processed with synchronous task management"
            )
            
    except Exception as e:
        logger.error(f"Error processing with task management: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task management error: {str(e)}"
        )
