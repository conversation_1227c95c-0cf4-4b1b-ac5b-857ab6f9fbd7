"""
Authentication API routes.
Follows Single Responsibility Principle - handles only auth endpoints.
"""

import logging
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from ..core.config import settings
from pydantic import BaseModel
from ..models.schemas import APIResponse, Token, UserCreate, UserResponse
from ..services.auth_service import AuthService
from ..services.user_service import UserService
from .dependencies import get_auth_service, get_user_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])


@router.post("/register", response_model=APIResponse)
async def register(
    user_data: UserCreate,
    user_service: UserService = Depends(get_user_service)
):
    """Register a new user."""
    try:
        user = await user_service.create_user(user_data)
        return APIResponse(
            success=True,
            message="User registered successfully",
            data={"user": user.model_dump()}
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=Token)
async def login(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    auth_service: AuthService = Depends(get_auth_service)
):
    """Login user and return access token."""
    try:
        user = await auth_service.authenticate_user(form_data.username, form_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        access_token = await auth_service.create_access_token(user.id)
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


class TokenVerifyRequest(BaseModel):
    """Token verification request schema."""
    token: str


@router.post("/verify-token", response_model=APIResponse)
async def verify_token(
    request: TokenVerifyRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Verify access token."""
    try:
        user_id = await auth_service.verify_token(request.token)
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        return APIResponse(
            success=True,
            message="Token is valid",
            data={"user_id": str(user_id)}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token verification failed"
        )
