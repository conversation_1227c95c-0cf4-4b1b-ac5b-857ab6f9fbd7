"""
Task management API endpoints.
Provides RESTful interfaces for task management and workflow operations.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.database import get_db
from src.services.task_service import TaskManagementService
from src.services.task_orchestration_service import TaskOrchestrationService
from src.models.schemas import (
    TaskCreate, TaskUpdate, TaskResponse, TaskWorkflowRequest, TaskWorkflowResponse,
    TaskProgressUpdate, TaskStatusUpdate, TaskWithDetailsResponse,
    AgentExecutionCreate, AgentExecutionResponse, TaskArtifactCreate, TaskArtifactResponse,
    APIResponse, ErrorResponse
)
from .dependencies import get_current_user_id

router = APIRouter(prefix="/api/v1/tasks", tags=["tasks"])


@router.post("/", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new task."""
    
    service = TaskManagementService(db)
    try:
        task = await service.create_task(task_data)
        return task
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/workflow", response_model=TaskWorkflowResponse)
async def create_task_from_workflow(
    workflow_request: TaskWorkflowRequest,
    db: AsyncSession = Depends(get_db)
):
    """Create a task from a workflow template."""
    
    service = TaskManagementService(db)
    try:
        response = await service.create_task_from_workflow(workflow_request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}", response_model=TaskWithDetailsResponse)
async def get_task(
    task_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get task details."""
    
    service = TaskManagementService(db)
    task = await service.get_task(task_id)
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return task


@router.get("/conversation/{conversation_id}", response_model=List[TaskResponse])
async def get_tasks_by_conversation(
    conversation_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get all tasks for a conversation."""
    
    service = TaskManagementService(db)
    tasks = await service.get_tasks_by_conversation(conversation_id)
    return tasks


@router.patch("/{task_id}/progress", response_model=APIResponse)
async def update_task_progress(
    task_id: str,
    progress_update: TaskProgressUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update task progress."""
    
    service = TaskManagementService(db)
    try:
        success = await service.update_task_progress(progress_update)
        if success:
            return APIResponse(
                success=True,
                message="Task progress updated successfully"
            )
        else:
            raise HTTPException(status_code=400, detail="Failed to update task progress")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/{task_id}/status", response_model=APIResponse)
async def update_task_status(
    task_id: str,
    status_update: TaskStatusUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update task status."""
    
    service = TaskManagementService(db)
    try:
        success = await service.update_task_status(status_update)
        if success:
            return APIResponse(
                success=True,
                message="Task status updated successfully"
            )
        else:
            raise HTTPException(status_code=400, detail="Failed to update task status")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/executions", response_model=AgentExecutionResponse)
async def create_agent_execution(
    task_id: str,
    execution_data: AgentExecutionCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create an agent execution record."""
    
    service = TaskManagementService(db)
    try:
        execution = await service.create_agent_execution(execution_data)
        return AgentExecutionResponse.from_orm(execution)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/artifacts", response_model=TaskArtifactResponse)
async def create_task_artifact(
    task_id: str,
    artifact_data: TaskArtifactCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a task artifact."""
    
    service = TaskManagementService(db)
    try:
        artifact = await service.create_task_artifact(artifact_data)
        return TaskArtifactResponse.from_orm(artifact)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/queue/pending", response_model=List[TaskResponse])
async def get_pending_tasks(
    limit: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """Get pending tasks for processing."""
    
    service = TaskManagementService(db)
    try:
        tasks = await service.get_pending_tasks(limit)
        return [TaskResponse.from_orm(task) for task in tasks]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/artifacts/cleanup", response_model=APIResponse)
async def cleanup_temporary_artifacts(
    older_than_days: int = Query(7, ge=1, le=30),
    db: AsyncSession = Depends(get_db)
):
    """Clean up temporary artifacts."""
    
    service = TaskManagementService(db)
    try:
        count = await service.cleanup_temporary_artifacts(older_than_days)
        return APIResponse(
            success=True,
            message=f"Cleaned up {count} temporary artifacts",
            data={"cleaned_count": count}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
# ============================================================================
# Task-Orchestration Integration Endpoints
# ============================================================================

@router.post("/intelligent", response_model=TaskWorkflowResponse)
async def create_intelligent_task(
    request: dict,
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """Create an intelligent task with automatic orchestration."""
    
    try:
        user_input = request.get("user_input", "")
        conversation_id = request.get("conversation_id")
        task_type = request.get("task_type", "fashion_design")
        priority = request.get("priority", 5)
        
        if not user_input:
            raise HTTPException(status_code=400, detail="user_input is required")
        
        # Create task orchestration service
        orchestration_service = TaskOrchestrationService(db)
        
        # Create intelligent task with authenticated user ID
        task_response = await orchestration_service.create_intelligent_task(
            user_input=user_input,
            conversation_id=conversation_id,
            user_id=str(current_user_id),
            task_type=task_type,
            priority=priority,
            **{k: v for k, v in request.items() if k not in ["user_input", "conversation_id", "user_id", "task_type", "priority"]}
        )
        
        return task_response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/orchestration-info", response_model=APIResponse)
async def get_task_orchestration_info(
    task_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get task details with orchestration information."""
    
    try:
        orchestration_service = TaskOrchestrationService(db)
        task_info = await orchestration_service.get_task_with_orchestration_info(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return APIResponse(
            success=True,
            data=task_info,
            message="Task orchestration info retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversation/{conversation_id}/enhanced", response_model=APIResponse)
async def get_conversation_enhanced_tasks(
    conversation_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get enhanced task information for a conversation."""
    
    try:
        orchestration_service = TaskOrchestrationService(db)
        enhanced_tasks = await orchestration_service.get_conversation_tasks_with_status(conversation_id)
        
        return APIResponse(
            success=True,
            data={
                "conversation_id": conversation_id,
                "tasks": enhanced_tasks,
                "total_tasks": len(enhanced_tasks)
            },
            message="Enhanced conversation tasks retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversation/{conversation_id}/artifacts", response_model=APIResponse)
async def get_conversation_artifacts(
    conversation_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get all artifacts for a conversation."""
    
    try:
        task_service = TaskManagementService(db)
        artifacts = await task_service.get_conversation_artifacts(conversation_id)
        
        # Convert to response format
        artifact_responses = []
        for artifact in artifacts:
            artifact_responses.append({
                "id": artifact.id,
                "task_id": artifact.task_id,
                "artifact_type": artifact.artifact_type,
                "artifact_name": artifact.artifact_name,
                "file_url": artifact.file_url,
                "file_path": artifact.file_path,
                "file_size": artifact.file_size,
                "mime_type": artifact.mime_type,
                "metadata": artifact.artifact_metadata,
                "created_at": artifact.created_at,
                "is_temporary": artifact.is_temporary,
                "expires_at": artifact.expires_at
            })
        
        return APIResponse(
            success=True,
            data={
                "conversation_id": conversation_id,
                "artifacts": artifact_responses,
                "total_artifacts": len(artifact_responses)
            },
            message="Conversation artifacts retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/retry", response_model=APIResponse)
async def retry_failed_task(
    task_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Retry a failed task."""
    
    try:
        orchestration_service = TaskOrchestrationService(db)
        success = await orchestration_service.retry_failed_task(task_id)
        
        if success:
            return APIResponse(
                success=True,
                message="Task retry initiated successfully"
            )
        else:
            raise HTTPException(status_code=400, detail="Failed to retry task")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/stats", response_model=APIResponse)
async def get_system_stats(
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive system statistics."""
    
    try:
        orchestration_service = TaskOrchestrationService(db)
        stats = await orchestration_service.get_system_stats()
        
        return APIResponse(
            success=True,
            data=stats,
            message="System statistics retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversation/{conversation_id}/process-with-tasks", response_model=APIResponse)
async def process_conversation_with_tasks(
    conversation_id: str,
    request: dict,
    db: AsyncSession = Depends(get_db),
    current_user_id: UUID = Depends(get_current_user_id)
):
    """Process conversation with integrated task management."""
    
    try:
        user_input = request.get("user_input", "")
        message_type = request.get("message_type", "user")
        requirements = request.get("requirements")
        edit_image_url = request.get("edit_image_url")
        edit_image_id = request.get("edit_image_id")
        
        if not user_input:
            raise HTTPException(status_code=400, detail="user_input is required")
        
        # Create task orchestration service
        orchestration_service = TaskOrchestrationService(db)
        
        # Create intelligent task with authenticated user ID
        task_response = await orchestration_service.create_intelligent_task(
            user_input=user_input,
            conversation_id=conversation_id,
            user_id=str(current_user_id),
            task_type="intelligent_design",
            priority=5,
            message_type=message_type,
            requirements=requirements,
            edit_image_url=edit_image_url,
            edit_image_id=edit_image_id
        )
        
        return APIResponse(
            success=True,
            data={
                "conversation_id": conversation_id,
                "task_id": str(task_response.task_id),
                "task_status": task_response.status,
                "estimated_agents": task_response.estimated_agents,
                "estimated_duration": task_response.estimated_duration,
                "message": "Task created and conversation processing started"
            },
            message="Conversation processed with task management"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
