"""
Design API routes.
Follows Single Responsibility Principle - handles design-related endpoints.
"""

import logging
from typing import List
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status

from ..models.schemas import (
    APIResponse,
    ChatMessageResponse,
    DesignConceptCreate,
    DesignConceptResponse,
)

from ..services.design_service import (
    ChatService,
    DesignConceptService,
)
from .dependencies import (
    get_chat_service,
    get_current_user_id,
    get_design_concept_service,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/design", tags=["design"])


# Design concept endpoints - conversation-based
@router.post("/concepts", response_model=DesignConceptResponse)
async def create_design_concept(
    concept_data: DesignConceptCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    concept_service: DesignConceptService = Depends(get_design_concept_service)
):
    """Create a new design concept."""
    try:
        concept = await concept_service.create_concept(current_user_id, concept_data)
        return concept
    except Exception as e:
        logger.error(f"Error creating design concept: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create design concept"
        )


@router.get("/concepts/{concept_id}", response_model=DesignConceptResponse)
async def get_design_concept(
    concept_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    concept_service: DesignConceptService = Depends(get_design_concept_service),
):
    """Get a specific design concept."""
    try:
        concept = await concept_service.get_concept_by_id(concept_id)
        if not concept:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design concept not found"
            )
        
        return concept
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting design concept: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get design concept"
        )


@router.get("/conversations/{conversation_id}/concepts", response_model=List[DesignConceptResponse])
async def get_conversation_concepts(
    conversation_id: UUID,
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id),
    concept_service: DesignConceptService = Depends(get_design_concept_service),
):
    """Get design concepts for a conversation."""
    try:
        concepts = await concept_service.get_conversation_concepts(
            conversation_id, current_user_id, skip, limit
        )
        return concepts
    except Exception as e:
        logger.error(f"Error getting conversation concepts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation concepts"
        )






@router.post("/render-3d", response_model=APIResponse)
async def render_3d_design(
    request: dict,
    current_user_id: UUID = Depends(get_current_user_id),
    concept_service: DesignConceptService = Depends(get_design_concept_service)
):
    """Render a 3D version of a design concept."""
    try:
        # Extract parameters from request
        concept_id = UUID(request.get("concept_id"))
        render_settings = request.get("render_settings", {})

        # Get the design concept
        concept = await concept_service.get_concept_by_id(concept_id)
        if not concept:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Design concept not found"
            )

        # Verify conversation ownership through the concept
        # Note: In the new model, concepts are linked to conversations, not projects
        # Ownership verification happens at the conversation level

        # For now, return the existing image with 3D metadata
        # In a real implementation, this would generate actual 3D data
        result = {
            "concept_id": str(concept_id),
            "image_url": concept.image_url,
            "render_settings": render_settings,
            "3d_data": {
                "model_type": render_settings.get("clothingType", "shirt"),
                "body_type": render_settings.get("bodyType", "standard"),
                "pose": render_settings.get("pose", "standing"),
                "lighting": render_settings.get("lighting", "studio"),
                "materials": {
                    "primary_color": render_settings.get("primaryColor", "#6366f1"),
                    "texture_url": concept.image_url,
                    "roughness": 0.7,
                    "metalness": 0.1
                }
            },
            "render_time": "2.3s",
            "quality": render_settings.get("renderQuality", "high")
        }

        return APIResponse(
            success=True,
            message="3D rendering completed successfully",
            data=result
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"3D rendering error: {e}")
        return APIResponse(
            success=False,
            message=f"Failed to render 3D design: {str(e)}",
            data=None
        )


# Chat endpoints - simplified for workflow integration
@router.get("/conversations/{conversation_id}/chat", response_model=List[ChatMessageResponse])
async def get_chat_messages(
    conversation_id: UUID,
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id),
    chat_service: ChatService = Depends(get_chat_service)
):
    """Get chat messages for a conversation."""
    try:
        # Note: Ownership verification happens at the conversation level
        # through the conversation service

        messages = await chat_service.get_conversation_messages(conversation_id, skip, limit)
        return messages

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chat messages: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get chat messages"
        )


# Unified Design Conversation Interface - Intelligent Multi-Agent Routing
@router.post("/conversations/{conversation_id}/conversation", response_model=APIResponse)
async def process_conversation(
    conversation_id: UUID,
    request: dict,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    统一的设计对话接口

    API层保持简洁，所有复杂逻辑都封装在ConversationService中：
    - 权限验证
    - 消息保存
    - 智能路由
    - 结果处理
    """
    try:
        # 添加请求大小限制
        if len(str(request)) > 1000000:  # 1MB限制
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Request too large"
            )
        
        # 提取请求参数
        user_input = request.get("content", "")
        message_type = request.get("message_type", "user")
        requirements = request.get("requirements")
        edit_image_url = request.get("edit_image_url")
        edit_image_id = request.get("edit_image_id")

        if not user_input:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content is required"
            )

        # 调用统一的对话处理服务（包含所有业务逻辑）
        from ..services.conversation_service import ConversationService
        conversation_service = ConversationService()

        result = await conversation_service.process_conversation(
            user_input=user_input,
            conversation_id=str(conversation_id),
            user_id=str(current_user_id),
            message_type=message_type,
            requirements=requirements,
            edit_image_url=edit_image_url,
            edit_image_id=edit_image_id
        )

        return APIResponse(
            success=True,
            data=result,
            message="Conversation processed successfully"
        )

    except Exception as e:
        logger.error(f"Error processing conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process conversation"
        )


# ============================================================================
# Multi-Agent Integration Point
# ============================================================================

# The multi-agent system integration should happen at the workflow level,
# not at the API level. This keeps the API simple and puts the intelligence
# in the coordination layer where it belongs.

# Future enhancement: The workflow can decide when to use multi-agent capabilities:
# - Complex requests → Multi-agent collaboration
# - Specific expertise needed → Expert consultation
# - Quality improvement → Peer review
# - Different design types → Specialized agents


# ============================================================================
# System Health Check (Optional - for monitoring)
# ============================================================================

@router.get("/system/health", response_model=APIResponse)
async def get_system_health():
    """Get system health status."""
    try:
        return APIResponse(
            success=True,
            data={
                "status": "healthy",
                "multi_agent_system": "available",
                "timestamp": datetime.now().isoformat()
            },
            message="System is healthy"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="System health check failed"
        )



