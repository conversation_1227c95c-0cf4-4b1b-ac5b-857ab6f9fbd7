"""
Timezone utilities for handling China Standard Time (CST, UTC+8).
Provides centralized timezone management for the application.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Optional

logger = logging.getLogger(__name__)

# China Standard Time (UTC+8)
CHINA_TZ = timezone(timedelta(hours=8), name="CST")


def get_china_time() -> datetime:
    """Get current time in China Standard Time (UTC+8)."""
    return datetime.now(CHINA_TZ)


def utc_to_china_time(utc_time: datetime) -> datetime:
    """Convert UTC time to China Standard Time."""
    if utc_time.tzinfo is None:
        # Assume it's UTC if no timezone info
        utc_time = utc_time.replace(tzinfo=timezone.utc)
    return utc_time.astimezone(CHINA_TZ)


def china_time_to_utc(china_time: datetime) -> datetime:
    """Convert China Standard Time to UTC."""
    if china_time.tzinfo is None:
        # Assume it's China time if no timezone info
        china_time = china_time.replace(tzinfo=CHINA_TZ)
    return china_time.astimezone(timezone.utc)


def format_china_time(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format datetime as China Standard Time string."""
    china_dt = utc_to_china_time(dt) if dt.tzinfo != CHINA_TZ else dt
    return china_dt.strftime(format_str)


def get_utc_now() -> datetime:
    """Get current UTC time - for database storage."""
    return datetime.now(timezone.utc)


def get_china_now_for_db() -> datetime:
    """Get current China time but return naive datetime for database compatibility."""
    return datetime.now(CHINA_TZ).replace(tzinfo=None)


def ensure_china_timezone(dt: datetime) -> datetime:
    """Ensure datetime has China timezone info."""
    if dt.tzinfo is None:
        return dt.replace(tzinfo=CHINA_TZ)
    return dt.astimezone(CHINA_TZ)


def is_china_time(dt: datetime) -> bool:
    """Check if datetime is in China timezone."""
    return dt.tzinfo == CHINA_TZ


def convert_to_china_time_for_display(dt: datetime) -> datetime:
    """Convert any datetime to China time for display purposes."""
    if dt.tzinfo is None:
        # For naive datetimes from database, assume UTC
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(CHINA_TZ)


def get_time_display(dt: Optional[datetime], include_timezone: bool = True) -> str:
    """Get formatted time string for display."""
    if dt is None:
        return ""
    
    # Database now stores China time directly, so just format it
    if include_timezone:
        return dt.strftime("%Y-%m-%d %H:%M:%S CST")
    else:
        return dt.strftime("%Y-%m-%d %H:%M:%S")


def get_current_timestamp() -> str:
    """Get current timestamp string in China timezone."""
    return get_china_time().strftime("%Y-%m-%d %H:%M:%S")


def parse_china_time(time_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """Parse time string as China timezone."""
    naive_dt = datetime.strptime(time_str, format_str)
    return naive_dt.replace(tzinfo=CHINA_TZ)


# Database timestamp functions - these maintain UTC storage
def get_db_timestamp() -> datetime:
    """Get timestamp for database storage (China Standard Time)."""
    return get_china_time().replace(tzinfo=None)


def format_db_timestamp(dt: datetime) -> str:
    """Format database timestamp (UTC) for display in China time."""
    return format_china_time(dt)


# Logging functions with China timezone
def log_with_china_time(message: str, level: str = "info"):
    """Log message with China timestamp."""
    timestamp = get_current_timestamp()
    log_func = getattr(logger, level.lower(), logger.info)
    log_func(f"[{timestamp}] {message}")


# Validation functions
def validate_china_time(dt: datetime) -> bool:
    """Validate if datetime is properly set to China timezone."""
    try:
        china_dt = ensure_china_timezone(dt)
        return china_dt.tzinfo == CHINA_TZ
    except Exception:
        return False


def get_time_difference(from_time: datetime, to_time: Optional[datetime] = None) -> timedelta:
    """Get time difference between two times in China timezone."""
    if to_time is None:
        to_time = get_china_time()
    
    from_china = convert_to_china_time_for_display(from_time)
    to_china = convert_to_china_time_for_display(to_time)
    
    return to_china - from_china


def format_time_difference(td: timedelta) -> str:
    """Format timedelta as human readable string."""
    total_seconds = int(td.total_seconds())
    
    if total_seconds < 60:
        return f"{total_seconds}秒"
    elif total_seconds < 3600:
        minutes = total_seconds // 60
        return f"{minutes}分钟"
    elif total_seconds < 86400:
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟"
    else:
        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        return f"{days}天{hours}小时"


def get_time_ago(dt: datetime) -> str:
    """Get 'time ago' string for display."""
    diff = get_time_difference(dt)
    return format_time_difference(diff) + "前"