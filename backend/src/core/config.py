"""
Application configuration settings.
Follows Single Responsibility Principle - handles only configuration.
"""

from functools import lru_cache
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # App info
    app_name: str = Field(default="Fashion Design AI")
    version: str = Field(default="1.0.0")
    environment: str = Field(default="development")
    debug: bool = Field(default=False)
    secret_key: str = Field(...)
    
    # Database
    database_url: str = Field(default="sqlite:///./fuzhuang.db")
    
    # AI Settings
    qwen_api_key: str = Field(...)
    qwen_base_url: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1")
    qwen_model: str = Field(default="qwen-vl-plus")
    max_tokens: int = Field(default=2000)
    temperature: float = Field(default=0.7)

    # Image Generation Settings
    # Dashscope (阿里云)
    dashscope_api_key: str = Field(default="sk-8d0831aeea0d430682d1a1987a616884")
    dashscope_model: str = Field(default="flux-dev")

    # Liblib Kontext
    liblib_ak: str = Field(default="TbcHnyo0QF7339YzsAHakQ")
    liblib_sk: str = Field(default="Z6RvKnvy6MhAGm_k8ScyY4qWdPHJZyNz")
    liblib_base_url: str = Field(default="https://openapi.liblibai.cloud")

    # Gitee AI
    gitee_api_key: str = Field(default="KH5CXTUKIYGSELVT0KA3RFFNBUMCOLCD83BX5H16")
    gitee_base_url: str = Field(default="https://ai.gitee.com/v1")
    gitee_model: str = Field(default="FLUX_1-Krea-dev")

    # Default image generation provider
    default_image_provider: str = Field(default="gitee")  # dashscope, liblib_kontext, gitee
    
    # Security
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)
    
    # CORS
    allowed_origins: List[str] = Field(default=["http://localhost:3000"])
    
    # File Upload
    max_file_size: int = Field(default=10485760)  # 10MB
    upload_path: str = Field(default="./uploads")
    allowed_file_types: List[str] = Field(default=["image/jpeg", "image/png", "image/webp"])
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


settings = get_settings()
