"""
Database connection and session management.
Follows Dependency Inversion Principle - depends on abstractions.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker

from .config import settings
from ..models.database import Base


class DatabaseManager:
    """Database manager following Singleton pattern."""
    
    _instance = None
    _engine = None
    _async_engine = None
    _session_factory = None
    _async_session_factory = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def initialize(self) -> None:
        """Initialize database connections with optimized pooling."""
        # Sync engine for migrations
        self._engine = create_engine(
            settings.database_url,
            echo=settings.debug,
            pool_pre_ping=True,
            pool_size=10,  # Add connection pool size
            max_overflow=20,  # Allow temporary overflow
            pool_recycle=3600,  # Recycle connections after 1 hour
            pool_timeout=30,  # Timeout for getting connection
        )
        
        # Async engine for application
        async_url = settings.database_url.replace("sqlite://", "sqlite+aiosqlite://")
        self._async_engine = create_async_engine(
            async_url,
            echo=settings.debug,
            pool_pre_ping=True,
            pool_size=20,  # Larger pool for async
            max_overflow=40,
            pool_recycle=3600,
            pool_timeout=30,
        )
        
        # Session factories
        self._session_factory = sessionmaker(
            bind=self._engine,
            autocommit=False,
            autoflush=False
        )
        
        self._async_session_factory = async_sessionmaker(
            bind=self._async_engine,
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )
    
    def create_tables(self) -> None:
        """Create database tables."""
        if self._engine is None:
            raise RuntimeError("Database not initialized")
        Base.metadata.create_all(bind=self._engine)
    
    def get_session(self) -> Session:
        """Get synchronous database session."""
        if self._session_factory is None:
            raise RuntimeError("Database not initialized")
        return self._session_factory()
    
    def get_async_session(self) -> AsyncSession:
        """Get asynchronous database session."""
        if self._async_session_factory is None:
            raise RuntimeError("Database not initialized")
        return self._async_session_factory()
    
    @asynccontextmanager
    async def get_async_session_context(self) -> AsyncGenerator[AsyncSession, None]:
        """Get async session with context management."""
        async with self._async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()


# Global database manager instance
db_manager = DatabaseManager()


def init_database() -> None:
    """Initialize database."""
    db_manager.initialize()
    db_manager.create_tables()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session."""
    async with db_manager.get_async_session_context() as session:
        yield session
