"""
Main FastAPI application.
Follows Open/Closed Principle - extensible without modification.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uuid

from .api.auth import router as auth_router
from .api.design import router as design_router
from .api.conversation import router as conversation_router
from .api.task_routes import router as task_router
from .api.canvas import router as canvas_router
from .core.config import settings
from .core.database import init_database
from .services.websocket_manager import websocket_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting Fashion Design AI Backend...")
    
    # Initialize database
    try:
        init_database()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Fashion Design AI Backend...")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="AI-powered fashion design platform backend API",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Add compression middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)


# Exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal Server Error",
            "message": "An unexpected error occurred"
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.version,
        "environment": settings.environment
    }


# WebSocket endpoint for real-time status updates
@app.websocket("/ws/{conversation_id}")
async def websocket_endpoint(websocket: WebSocket, conversation_id: str):
    """WebSocket endpoint for real-time status updates"""
    connection_id = str(uuid.uuid4())

    try:
        await websocket_manager.connect(websocket, connection_id, conversation_id)
        logger.info(f"WebSocket connected for conversation: {conversation_id}")

        # Send welcome message
        await websocket_manager.send_personal_message(
            {
                "type": "connection_established",
                "conversation_id": conversation_id,
                "connection_id": connection_id,
                "message": "WebSocket连接已建立，准备接收实时状态更新"
            },
            connection_id
        )

        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional)
                data = await websocket.receive_text()
                logger.debug(f"Received WebSocket message: {data}")

                # Echo back or handle client messages if needed
                await websocket_manager.send_personal_message(
                    {"type": "echo", "data": data},
                    connection_id
                )

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                break

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for conversation: {conversation_id}")
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    finally:
        websocket_manager.disconnect(connection_id, conversation_id)


# API routes
app.include_router(auth_router, prefix="/api/v1")
app.include_router(design_router, prefix="/api/v1")
app.include_router(conversation_router, prefix="/api/v1")
app.include_router(task_router)
app.include_router(canvas_router, prefix="/api/v1")


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": f"Welcome to {settings.app_name} API",
        "version": settings.version,
        "docs": "/docs" if settings.debug else "Documentation not available in production"
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )
