#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logo Design Agent

专业Logo设计Agent，提供品牌标识设计服务
基于LogoWorkflow实现Logo设计的完整工作流
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from uuid import uuid4

from .workflow.logo_workflow import LogoWorkflow
from .workflow.base_workflow import ConversationState
from .base_agents import BaseDesignAgent, BaseRequirements, AgentTask, AgentResult, SharedContext, TaskStatus

logger = logging.getLogger(__name__)


class LogoDesignAgent(BaseDesignAgent):
    """Logo设计专业Agent"""

    def __init__(self, agent_id: Optional[str] = None):
        # Logo-specific capabilities
        self.domain_expertise = [
            "brand_identity", "logo_design", "visual_identity",
            "brand_strategy", "symbol_design", "typography_design"
        ]

        super().__init__(agent_id, "LogoDesignAgent")

        self.workflow = LogoWorkflow()
        logger.info(f"LogoDesignAgent initialized with ID: {self.agent_id}")

    def get_capabilities(self) -> List[str]:
        """Return logo design capabilities."""
        base_capabilities = [
            "conversation_processing", "requirement_extraction",
            "design_generation", "design_modification", "collaborative_design"
        ]
        return base_capabilities + self.domain_expertise

    def get_requirements_schema(self) -> type:
        """Return logo requirements schema."""
        return BaseRequirements  # 可以后续创建LogoRequirements

    async def process_conversation(self, conversation_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理Logo设计对话"""
        try:
            user_input = conversation_data.get("user_input", "")
            conversation_id = conversation_data.get("conversation_id")
            user_id = conversation_data.get("user_id", "default_user")

            logger.info(f"Processing logo design conversation: {user_input[:50]}...")

            # 调用Logo工作流处理 - 使用正确的参数格式
            result = await self.workflow.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                # 传递其他参数
                messages=conversation_data.get("messages", []),
                requirements=conversation_data.get("requirements"),
                design_history=conversation_data.get("design_history", []),
                edit_image_url=conversation_data.get("edit_image_url"),
                edit_image_id=conversation_data.get("edit_image_id"),
                message_type=conversation_data.get("message_type", "user")
            )

            logger.info("Logo design conversation processed successfully")
            return result

        except Exception as e:
            logger.error(f"Logo design conversation processing failed: {e}")
            return {
                "messages": [{
                    "role": "assistant",
                    "content": f"抱歉，Logo设计处理过程中出现了问题：{str(e)}。请稍后重试。"
                }],
                "error_message": str(e),
                "current_state": "error"
            }

    async def execute_collaborative_task(self, task: AgentTask, shared_context: SharedContext) -> AgentResult:
        """执行协作任务"""
        import time

        start_time = time.time()
        task_id = task["id"]

        try:
            logger.info(f"Logo agent executing task: {task['description']}")

            # 直接调用workflow，不需要复杂的任务类型判断
            requirements = task["requirements"]
            user_input = requirements.get("user_input", task["description"])
            conversation_id = shared_context["conversation_id"]

            # 直接调用workflow
            logger.info(f"LogoAgent using conversation_id: {conversation_id}")

            result = await self.workflow.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=requirements.get("user_id", "collaborative_user"),
                message_type="user"
            )

            execution_time = time.time() - start_time

            # 从工作流结果中提取artifacts
            artifacts = []

            # 检查是否有image_url（Logo设计结果）
            if "image_url" in result and result["image_url"]:
                logo_artifact = {
                    "id": str(uuid4()),
                    "type": "logo",
                    "image_url": result["image_url"],
                    "description": f"Logo设计 - {result.get('requirements', {}).get('category', '品牌标识')}",
                    "metadata": {
                        "task_type": "logo_design",
                        "agent_type": self.agent_type,
                        "created_by": self.agent_id,
                        "design_prompt": result.get("design_prompt", ""),
                        "requirements": result.get("requirements", {})
                    }
                }
                artifacts.append(logo_artifact)
                logger.info(f"Created logo artifact: {logo_artifact['id']} with image: {result['image_url']}")

            # 如果结果中已经有artifacts，也要包含进来
            if "artifacts" in result and isinstance(result["artifacts"], list):
                artifacts.extend(result["artifacts"])

            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                output=result,
                artifacts=artifacts,
                execution_time=execution_time,
                error_message=None,
                metadata={
                    "task_type": "logo_design",
                    "agent_type": self.agent_type
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Logo agent task execution failed: {e}")

            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                output={},
                artifacts=[],
                execution_time=execution_time,
                error_message=str(e),
                metadata={
                    "task_type": "logo_design",
                    "agent_type": self.agent_type
                }
            )

    def get_capabilities(self) -> List[str]:
        """获取Agent能力列表"""
        return [
            "logo_design",           # Logo设计
            "brand_identity",        # 品牌标识设计
            "corporate_logo",        # 企业Logo
            "product_logo",          # 产品Logo
            "personal_brand",        # 个人品牌Logo
            "logo_redesign",         # Logo重设计
            "brand_consultation"     # 品牌咨询
        ]

    def get_agent_info(self) -> Dict[str, Any]:
        """获取Agent信息"""
        return {
            "name": "LogoDesignAgent",
            "type": self.agent_type,
            "description": "专业Logo设计Agent，提供品牌标识设计服务",
            "capabilities": self.get_capabilities(),
            "version": "1.0.0",
            "status": "active"
        }
