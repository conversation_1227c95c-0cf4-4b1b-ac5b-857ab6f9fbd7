"""
Conversation Service - 统一的对话处理业务层

这个服务层封装了所有对话处理的复杂性，API层不需要知道内部使用了什么技术：
- LangGraph工作流
- 多Agent协作
- 智能路由
- 等等

API层只需要调用这一个服务，内部的技术选择对外透明。
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..utils.timezone_utils import get_time_display

logger = logging.getLogger(__name__)


class ConversationService:
    """统一的对话处理服务，封装所有内部复杂性"""
    
    def __init__(self):
        """初始化对话服务，延迟加载内部组件"""
        self._agent_router = None
        self._workflow = None
        self._initialized = False
    
    def _ensure_initialized(self):
        """确保服务已初始化（延迟加载）"""
        if self._initialized:
            return
        
        try:
            # 延迟导入，避免循环依赖
            from .agent_factory import AgentRouter
            from .workflow.design_workflow import DesignWorkflow
            from .design_service import ChatService
            from ..core.database import db_manager

            # 确保数据库管理器已初始化
            if db_manager._async_session_factory is None:
                db_manager.initialize()

            self._agent_router = AgentRouter()
            self._workflow = DesignWorkflow()

            # 初始化数据库服务（使用数据库会话）
            # 注意：这里需要在实际使用时创建会话，而不是在初始化时
            self._db_manager = db_manager
            self._initialized = True

            logger.info("ConversationService initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize ConversationService: {e}")
            raise
    
    async def process_conversation(
        self,
        user_input: str,
        user_id: str,
        conversation_id: Optional[str] = None,
        message_type: str = "user",
        requirements: Optional[Dict] = None,
        edit_image_url: Optional[str] = None,
        edit_image_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        完整的对话处理流程：自动创建对话 -> 保存用户消息 -> 处理请求 -> 保存AI回复
        
        如果没有提供conversation_id，将自动创建新对话
        现在改为同步执行，直接返回处理结果
        """

        self._ensure_initialized()

        # 现在要求必须提供conversation_id，不自动创建
        if not conversation_id:
            raise ValueError("conversation_id is required. Please call generateConversationId() first.")

        # 同步处理对话
        try:
            result = await self._process_conversation_internal(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                message_type=message_type,
                requirements=requirements,
                edit_image_url=edit_image_url,
                edit_image_id=edit_image_id,
                **kwargs
            )
            
            # 返回处理结果
            return {
                "status": "completed",
                "message": "Conversation processed successfully",
                "result": result
            }
        except Exception as e:
            logger.error(f"Conversation processing failed: {e}")
            return {
                "status": "error",
                "message": "Conversation processing failed",
                "error": str(e)
            }

    async def _process_conversation_internal(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        message_type: str = "user",
        requirements: Optional[Dict] = None,
        edit_image_url: Optional[str] = None,
        edit_image_id: Optional[str] = None,
        **kwargs
    ) -> None:
        """内部对话处理逻辑，异步执行"""

        try:
            from uuid import UUID
            from ..models.schemas import ChatMessageCreate
            from .design_service import ChatService

            # 使用数据库会话
            async with self._db_manager.get_async_session_context() as session:
                # 如果是新对话，先创建对话记录
                # 现在对话应该在调用 generateConversationId() 时已经创建
                # 这里只需要验证对话存在
                from ..models.database import Conversation
                from sqlalchemy import select
                
                conv_result = await session.execute(
                    select(Conversation).where(
                        Conversation.id == conversation_id,
                        Conversation.user_id == user_id
                    )
                )
                conversation = conv_result.scalar_one_or_none()
                
                if not conversation:
                    raise ValueError(f"Conversation {conversation_id} not found for user {user_id}")
                
                logger.info(f"Using existing conversation: {conversation_id}")
                
                chat_service = ChatService(session)

                # 1. 保存用户消息到数据库
                user_message_data = ChatMessageCreate(
                    role="user",
                    content=user_input,
                    image_url=edit_image_url,
                    message_metadata={
                        "message_type": message_type,
                        "requirements": requirements,
                        "edit_image_id": edit_image_id,
                        "conversation_title": user_input[:50] + "..." if len(user_input) > 50 else user_input
                    }
                )
                conversation_uuid = UUID(conversation_id) if isinstance(conversation_id, str) else conversation_id
                user_message = await chat_service.create_message(conversation_uuid, user_message_data)
                logger.info(f"保存用户消息: {user_message.id}")

                # 2. 处理用户输入
                result = await self.process_user_input(
                    user_input=user_input,
                    conversation_id=conversation_id,
                    user_id=user_id,
                    message_type=message_type,
                    requirements=requirements,
                    edit_image_url=edit_image_url,
                    edit_image_id=edit_image_id,
                    **kwargs
                )

                # 3. 保存AI回复到数据库
                ai_content = ""
                ai_metadata = {}

                # 提取AI回复内容和元数据
                if isinstance(result, dict):
                    # 提取消息内容
                    if "messages" in result and result["messages"]:
                        messages = result["messages"]
                        if isinstance(messages, list) and len(messages) > 0:
                            last_message = messages[-1]
                            # 如果是消息对象，提取content字段
                            if isinstance(last_message, dict) and "content" in last_message:
                                ai_content = last_message["content"]
                            else:
                                ai_content = str(last_message)
                        else:
                            ai_content = str(messages)
                    elif "message" in result:
                        ai_content = result["message"]
                    elif "content" in result:
                        ai_content = result["content"]
                    else:
                        ai_content = "AI处理完成"

                    # 保存完整的结果作为元数据
                    ai_metadata = {
                        "result_type": result.get("type", "unknown"),
                        "workflow_state": result.get("workflow_state"),
                        "requirements": result.get("requirements"),
                        "generated_content": result.get("generated_content"),
                        "image_url": result.get("image_url"),
                        "full_result": result  # 保存完整结果以便后续分析
                    }
                else:
                    ai_content = str(result)
                    ai_metadata = {}

                ai_message_data = ChatMessageCreate(
                    role="assistant",
                    content=ai_content,
                    image_url=result.get("image_url") if isinstance(result, dict) else None,
                    message_metadata=ai_metadata
                )
                ai_message = await chat_service.create_message(conversation_uuid, ai_message_data)
                logger.info(f"保存AI回复: {ai_message.id}")

                # 同步处理模式，不需要通过WebSocket推送对话结果
                # await self._broadcast_conversation_result(conversation_id, result)

        except Exception as e:
            logger.error(f"Error in process_conversation: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": "对话处理失败",
                "type": "error",
                "technical_error": str(e)
            }

    async def process_user_input(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        message_type: str = "user",
        requirements: Optional[Dict] = None,
        edit_image_url: Optional[str] = None,
        edit_image_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        处理用户输入，返回AI响应
        
        这个方法内部决定使用什么技术：
        - 简单请求 -> 直接使用LangGraph工作流
        - 复杂请求 -> 使用多Agent协作
        - 专业咨询 -> 路由到专家Agent
        
        API层不需要知道这些细节。
        """
        
        self._ensure_initialized()
        
        try:
            logger.info(f"Processing user input: {user_input[:50]}...")
            
            # 内部智能决策：使用AgentRouter来处理
            # 这里可以根据需要切换到不同的处理策略
            result = await self._agent_router.intelligent_process_request(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                message_type=message_type,
                requirements=requirements,
                edit_image_url=edit_image_url,
                edit_image_id=edit_image_id,
                **kwargs
            )
            
            logger.info("User input processed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            
            # 直接返回错误响应，不使用fallback
            return {
                "success": False,
                "error": "对话处理失败",
                "type": "error",
                "messages": ["抱歉，系统暂时无法处理您的请求，请稍后重试。"],
                "technical_error": str(e)
            }
    
    async def get_conversation_status(
        self,
        conversation_id: str
    ) -> Dict[str, Any]:
        """获取对话状态"""
        
        self._ensure_initialized()
        
        try:
            # 这里可以查询对话状态、工作流状态等
            return {
                "status": "active",
                "conversation_id": conversation_id,
                "available_features": [
                    "fashion_design",
                    "multi_agent_collaboration", 
                    "expert_consultation"
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation status: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def get_user_conversations(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取用户的所有对话 - 使用SQL多表关联查询优化性能"""
        
        self._ensure_initialized()
        
        try:
            from datetime import datetime
            from uuid import UUID
            
            # 使用数据库会话查询用户的对话
            async with self._db_manager.get_async_session_context() as session:
                from ..models.database import Conversation, ChatMessage, DesignConcept
                from sqlalchemy import select, func, desc, literal_column
                
                # 使用优化的多表关联查询一次性获取所有需要的数据
                # 添加子查询来获取最后一条消息内容，避免复杂的case语句
                last_message_subquery = (
                    select(ChatMessage.content)
                    .where(ChatMessage.conversation_id == Conversation.id)
                    .where(ChatMessage.role == 'user')
                    .order_by(desc(ChatMessage.created_at))
                    .limit(1)
                    .correlate(Conversation)
                    .scalar_subquery()
                )
                
                preview_image_subquery = (
                    select(ChatMessage.image_url)
                    .where(ChatMessage.conversation_id == Conversation.id)
                    .where(ChatMessage.image_url.isnot(None))
                    .order_by(desc(ChatMessage.created_at))
                    .limit(1)
                    .correlate(Conversation)
                    .scalar_subquery()
                )
                
                design_preview_subquery = (
                    select(DesignConcept.image_url)
                    .where(DesignConcept.conversation_id == Conversation.id)
                    .where(DesignConcept.image_url.isnot(None))
                    .order_by(desc(DesignConcept.created_at))
                    .limit(1)
                    .correlate(Conversation)
                    .scalar_subquery()
                )
                
                stmt = (
                    select(
                        Conversation.id,
                        Conversation.title,
                        Conversation.status,
                        Conversation.created_at,
                        Conversation.updated_at,
                        # 消息数量
                        func.count(ChatMessage.id).label('message_count'),
                        # 设计数量
                        func.count(DesignConcept.id).label('design_count'),
                        # 最后一条用户消息内容
                        last_message_subquery.label('last_message_content'),
                        # 预览图片
                        preview_image_subquery.label('preview_image'),
                        # 设计预览图片
                        design_preview_subquery.label('design_preview_image')
                    )
                    .outerjoin(
                        ChatMessage, 
                        Conversation.id == ChatMessage.conversation_id
                    )
                    .outerjoin(
                        DesignConcept,
                        Conversation.id == DesignConcept.conversation_id
                    )
                    .where(Conversation.user_id == user_id)
                    .group_by(Conversation.id)
                    .order_by(desc(Conversation.updated_at))
                    .offset(skip)
                    .limit(limit)
                )
                
                result = await session.execute(stmt)
                conversations_data = result.fetchall()
                
                # 构建返回的对话列表
                conversations = []
                for conv_data in conversations_data:
                    conversation_id = conv_data.id
                    title = conv_data.title
                    status = conv_data.status
                    created_at = conv_data.created_at
                    updated_at = conv_data.updated_at
                    message_count = conv_data.message_count or 0
                    design_count = conv_data.design_count or 0
                    
                    # 直接使用查询结果中的最后一条消息内容和预览图片
                    last_message = conv_data.last_message_content or "新会话"
                    preview_image = conv_data.preview_image or conv_data.design_preview_image
                    
                    # Convert user_id to UUID if it's a valid UUID string, otherwise keep as is
                    try:
                        user_uuid = UUID(user_id)
                    except ValueError:
                        user_uuid = user_id  # Keep as string if not a valid UUID
                    
                    conversations.append({
                        "id": UUID(conversation_id),
                        "title": title,
                        "status": status,
                        "meta_data": {
                            "message_count": message_count,
                            "design_count": design_count,
                            "preview_image": preview_image,
                            "last_message": last_message[:100] + "..." if len(last_message) > 100 else last_message
                        },
                        "user_id": user_uuid,
                        "created_at": created_at,
                        "updated_at": updated_at,
                    })
                
                logger.info(f"Retrieved {len(conversations)} conversations for user {user_id} with optimized query")
                return conversations
                
        except Exception as e:
            logger.error(f"Error getting user conversations: {e}")
            # 返回空列表而不是抛出异常
            return []
    
    async def get_conversation(
        self,
        conversation_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """获取特定对话的详细信息"""
        
        self._ensure_initialized()
        
        try:
            from uuid import UUID
            from datetime import datetime
            
            # 使用数据库会话查询对话
            async with self._db_manager.get_async_session_context() as session:
                from ..models.database import Conversation, ChatMessage
                from sqlalchemy import select, func, update
                
                # 查询对话
                conv_result = await session.execute(
                    select(Conversation).where(
                        Conversation.id == conversation_id,
                        Conversation.user_id == user_id
                    )
                )
                conversation = conv_result.scalar_one_or_none()
                
                if not conversation:
                    return None
                
                # 获取消息统计
                stats_result = await session.execute(
                    select(
                        func.count(ChatMessage.id).label('message_count')
                    ).where(ChatMessage.conversation_id == conversation_id)
                )
                message_count = stats_result.scalar() or 0
                
                # 构建返回数据
                meta_data = conversation.meta_data or {}
                meta_data["message_count"] = message_count
                
                # Convert user_id to UUID if it's a valid UUID string, otherwise keep as is
                try:
                    user_uuid = UUID(user_id)
                except ValueError:
                    user_uuid = user_id  # Keep as string if not a valid UUID
                
                result = {
                    "id": conversation_id,  # 保持字符串格式
                    "title": conversation.title,
                    "status": conversation.status,
                    "meta_data": meta_data,
                    "user_id": user_uuid,
                    "created_at": conversation.created_at,
                    "updated_at": conversation.updated_at,
                }
                
                logger.info(f"Retrieved conversation {conversation_id} for user {user_id}")
                return result
                
        except Exception as e:
            logger.error(f"Error getting conversation {conversation_id}: {e}")
            return None

    async def get_conversation_messages(
        self,
        conversation_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取特定对话的所有消息"""
        
        self._ensure_initialized()
        
        try:
            from uuid import UUID
            
            # 使用数据库会话查询消息
            async with self._db_manager.get_async_session_context() as session:
                from ..models.database import ChatMessage
                from sqlalchemy import select, func
                
                # 查询指定对话的所有消息，按创建时间排序
                stmt = (
                    select(ChatMessage)
                    .where(ChatMessage.conversation_id == conversation_id)
                    .order_by(ChatMessage.created_at.asc())
                    .offset(skip)
                    .limit(limit)
                )
                
                result = await session.execute(stmt)
                messages = result.scalars().all()
                
                # 转换为API响应格式
                message_list = []
                for message in messages:
                    message_dict = {
                        "id": message.id,
                        "conversation_id": UUID(conversation_id),
                        "role": message.role,
                        "content": message.content,
                        "image_url": message.image_url,
                        "message_metadata": message.message_metadata or {},
                        "created_at": message.created_at,
                    }
                    message_list.append(message_dict)
                
                logger.info(f"Retrieved {len(message_list)} messages for conversation {conversation_id}")
                return message_list
                
        except Exception as e:
            logger.error(f"Error getting conversation messages for {conversation_id}: {e}")
            return []
    
    def get_supported_features(self) -> Dict[str, Any]:
        """获取支持的功能列表"""
        return {
            "design_types": [
                "fashion",
                "poster", 
                "general"
            ],
            "collaboration_modes": [
                "single_agent",
                "multi_agent",
                "expert_consultation"
            ],
            "capabilities": [
                "natural_language_processing",
                "image_generation",
                "design_modification",
                "style_consultation",
                "trend_analysis"
            ]
        }

    async def _broadcast_conversation_result(self, conversation_id: str, result: Dict[str, Any]):
        """通过WebSocket推送对话结果"""
        try:
            from .websocket_manager import get_design_broadcaster

            broadcaster = get_design_broadcaster()

            # 构建WebSocket消息
            message_data = {
                "conversation_id": conversation_id,
                "messages": result.get("messages", []),
                "image_url": result.get("image_url"),
                "design_prompt": result.get("design_prompt") or result.get("optimized_prompt"),
                "metadata": result.get("metadata", {}),
                "current_state": result.get("current_state"),
                "intent": result.get("intent"),
                "timestamp": result.get("timestamp")
            }

            # 发送对话结果
            await broadcaster.send_conversation_result(conversation_id, message_data)

            logger.info(f"Conversation result broadcasted for: {conversation_id}")

        except Exception as e:
            logger.error(f"Failed to broadcast conversation result: {e}")
            # 不抛出异常，避免影响主流程
