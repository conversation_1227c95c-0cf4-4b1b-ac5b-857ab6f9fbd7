#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Drama Production Agent - Specialized agent for drama and video production.

This module implements a drama production agent that handles complete drama workflows
including script writing, character design, scene planning, and video production.
"""

import json
import logging
import time
from typing import Dict, Any, List, Optional, Type
from uuid import uuid4
from datetime import datetime

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import MemorySaver

from .base_agents import (
    BaseDesignAgent, BaseRequirements, AgentTask, AgentResult, 
    SharedContext, TaskStatus
)
from .workflow import (
    DesignWorkflow, DesignRequirements, ConversationState,
    IntentType, WorkflowState
)
from .drama_models import DramaRequirements
from ..core.config import settings

logger = logging.getLogger(__name__)


# ============================================================================
# Drama Production Agent Implementation
# ============================================================================

class DramaProductionAgent(BaseDesignAgent):
    """Drama production agent with comprehensive production capabilities."""
    
    def __init__(self, agent_id: Optional[str] = None):
        # Drama-specific production capabilities
        self.domain_expertise = [
            "script_writing", "character_design", "scene_planning", "video_production",
            "dramatic_structure", "dialogue_writing", "visual_storytelling",
            "narrative_design", "character_development", "cinematography"
        ]

        super().__init__(agent_id, "DramaProductionAgent")

        # Initialize LLM for drama production
        self.llm = ChatOpenAI(
            base_url=settings.qwen_base_url,
            api_key=settings.qwen_api_key,
            model=settings.qwen_model,
            temperature=0.8,  # Higher temperature for creative work
        )

        # Initialize the drama workflow
        from .workflow.drama_workflow import DramaWorkflow
        self.workflow_engine = DramaWorkflow(design_agent=self)
    
    def get_capabilities(self) -> List[str]:
        """Return drama production capabilities."""
        base_capabilities = [
            "conversation_processing", "requirement_extraction",
            "design_generation", "design_modification", "collaborative_design"
        ]
        return base_capabilities + self.domain_expertise
    
    def get_requirements_schema(self) -> Type[BaseRequirements]:
        """Return drama requirements schema."""
        return DramaRequirements
    
    async def process_conversation(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Process user conversation for drama production."""
        
        try:
            logger.info(f"DramaProductionAgent processing conversation: {conversation_id}")

            # Use drama workflow to process conversation
            result = await self.workflow_engine.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                **kwargs
            )

            # Add drama production specific identification
            result["production_type"] = "drama"
            result["agent_type"] = self.agent_type

            return result

        except Exception as e:
            logger.error(f"Drama production conversation failed: {e}")
            return {
                "error": str(e),
                "agent_type": self.agent_type,
                "conversation_id": conversation_id,
                "production_type": "drama"
            }
    
    async def execute_collaborative_task(
        self,
        task: AgentTask,
        shared_context: SharedContext
    ) -> AgentResult:
        """Execute collaborative drama production tasks by delegating to workflow."""
        
        start_time = time.time()
        task_id = task["id"]
        
        try:
            logger.info(f"Drama agent executing task: {task['description']}")

            # Direct workflow delegation following fashion agent pattern
            requirements = task["requirements"]
            user_input = requirements.get("user_input", task["description"])
            conversation_id = shared_context["conversation_id"]

            # Delegate to workflow for all processing
            result = await self.workflow_engine.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=requirements.get("user_id", "collaborative_user"),
                **{k: v for k, v in requirements.items() if k != "user_input"}
            )

            execution_time = time.time() - start_time

            # Create simple artifact for result
            artifacts = [{
                "id": str(uuid4()),
                "type": "drama_production",
                "description": f"剧情制作 - {requirements.get('task_type', 'drama_production')}",
                "content": result.get("design_content", ""),
                "metadata": {
                    "task_type": requirements.get("task_type", "drama_production"),
                    "agent_type": self.agent_type,
                    "created_by": self.agent_id,
                    "production_phase": self._get_production_phase(requirements.get("task_type", "drama_production")),
                    "created_at": datetime.now().isoformat()
                }
            }]

            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                output=result,
                artifacts=artifacts,
                execution_time=execution_time,
                error_message=None,
                metadata={
                    "task_type": requirements.get("task_type", "drama_production"),
                    "agent_type": self.agent_type,
                    "production_phase": self._get_production_phase(requirements.get("task_type", "drama_production"))
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Drama agent task execution failed: {e}")
            
            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                output={},
                artifacts=[],
                execution_time=execution_time,
                error_message=str(e),
                metadata={
                    "task_type": task.get("requirements", {}).get("task_type", "unknown"),
                    "agent_type": self.agent_type
                }
            )

    # ============================================================================
    # Helper Methods
    # ============================================================================

    def _get_production_phase(self, task_type: str) -> str:
        """Map task type to production phase."""
        phase_mapping = {
            "script_writing": "script",
            "character_design": "character",
            "scene_planning": "scene",
            "video_production": "video",
            "drama_production": "comprehensive"
        }
        return phase_mapping.get(task_type, "unknown")

    def get_agent_info(self) -> Dict[str, Any]:
        """Get drama production agent information."""
        return {
            "name": "DramaProductionAgent",
            "type": self.agent_type,
            "description": "专业剧情制作Agent，提供剧本创作、角色设计、场景规划和视频制作服务",
            "capabilities": self.get_capabilities(),
            "domain_expertise": self.domain_expertise,
            "version": "1.0.0",
            "status": "active"
        }