"""
Task management service for handling multi-agent workflows.
This service provides methods for creating, managing, and tracking tasks.
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import uuid4, UUID

from sqlalchemy import select, update, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.models.database import (
    Task, AgentExecution, TaskDependency, TaskArtifact, 
    WorkflowTemplate, TaskQueue, Conversation, ChatMessage
)
from src.utils.timezone_utils import get_db_timestamp, get_time_display
from src.models.schemas import (
    TaskCreate, TaskUpdate, TaskResponse, AgentExecutionCreate,
    TaskArtifactCreate, TaskWorkflowRequest, TaskWorkflowResponse, TaskProgressUpdate,
    TaskStatusUpdate, TaskWithDetailsResponse
)


class TaskManagementService:
    """Service for managing tasks and multi-agent workflows."""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
    
    async def create_task(self, task_data: TaskCreate) -> TaskResponse:
        """Create a new task."""
        
        task = Task(
            id=str(uuid4()),
            conversation_id=str(task_data.conversation_id),
            parent_task_id=str(task_data.parent_task_id) if task_data.parent_task_id else None,
            task_type=task_data.task_type,
            name=task_data.name,
            description=task_data.description,
            status="pending",
            priority=task_data.priority,
            progress=0.0,
            input_parameters=task_data.input_parameters,
            output_results={},
            created_at=get_db_timestamp()
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        # Update conversation's current task
        await self._update_conversation_current_task(str(task_data.conversation_id), task.id)
        
        return TaskResponse.from_orm(task)
    
    async def create_task_from_workflow(self, workflow_request: TaskWorkflowRequest) -> TaskWorkflowResponse:
        """Create a task from a workflow template."""
        
        # Get workflow template if specified
        template = None
        if workflow_request.use_template and workflow_request.template_id:
            template = await self._get_workflow_template(str(workflow_request.template_id))
        elif workflow_request.use_template:
            template = await self._get_active_workflow_template(workflow_request.task_type)
        
        # Create main task
        task_data = TaskCreate(
            conversation_id=workflow_request.conversation_id,
            task_type=workflow_request.task_type,
            name=workflow_request.name,
            description=workflow_request.description,
            input_parameters=workflow_request.input_parameters,
            priority=workflow_request.priority
        )
        
        task = await self.create_task(task_data)
        
        # If template exists, create agent executions
        if template:
            await self._create_agent_executions_from_template(task.id, template)
            
            # Estimate duration based on template
            estimated_duration = self._estimate_duration_from_template(template)
        else:
            estimated_duration = None
        
        # Add task to queue
        await self._add_task_to_queue(task.id)
        
        return TaskWorkflowResponse(
            task_id=task.id,
            conversation_id=task.conversation_id,
            status=task.status,
            message="Task created successfully" + (" from template" if template else ""),
            estimated_agents=len(template["template_definition"]["agents"]) if template else 0,
            estimated_duration=estimated_duration
        )
    
    async def get_task(self, task_id: str) -> Optional[TaskWithDetailsResponse]:
        """Get task with full details."""
        
        result = await self.db.execute(
            select(Task)
            .options(
                selectinload(Task.agent_executions),
                selectinload(Task.dependencies_as_dependent),
                selectinload(Task.artifacts),
                selectinload(Task.subtasks)
            )
            .where(Task.id == task_id)
        )
        
        task = result.scalar_one_or_none()
        if not task:
            return None
        
        return TaskWithDetailsResponse.from_orm(task)
    
    async def get_tasks_by_conversation(self, conversation_id: str) -> List[TaskResponse]:
        """Get all tasks for a conversation."""
        
        result = await self.db.execute(
            select(Task)
            .where(Task.conversation_id == conversation_id)
            .order_by(Task.created_at.desc())
        )
        
        return [TaskResponse.from_orm(task) for task in result.scalars().all()]
    
    async def update_task_progress(self, progress_update: TaskProgressUpdate) -> bool:
        """Update task progress."""
        
        update_data = {
            "progress": float(progress_update.progress)
        }
        
        if progress_update.status:
            update_data["status"] = progress_update.status
        
        if progress_update.message:
            update_data["error_message"] = progress_update.message
        
        if progress_update.output_data:
            # Merge with existing output results
            result = await self.db.execute(select(Task).where(Task.id == str(progress_update.task_id)))
            task = result.scalar_one_or_none()
            if task:
                output_results = task.output_results or {}
                output_results.update(progress_update.output_data)
                update_data["output_results"] = output_results
        
        await self.db.execute(
            update(Task)
            .where(Task.id == str(progress_update.task_id))
            .values(**update_data)
        )
        
        await self.db.commit()
        return True
    
    async def update_task_status(self, status_update: TaskStatusUpdate) -> bool:
        """Update task status."""
        
        update_data = {
            "status": status_update.status
        }
        
        if status_update.status == "in_progress":
            update_data["started_at"] = get_db_timestamp()
        elif status_update.status in ["completed", "failed", "cancelled"]:
            update_data["completed_at"] = get_db_timestamp()
            # Calculate actual duration
            result = await self.db.execute(select(Task).where(Task.id == str(status_update.task_id)))
            task = result.scalar_one_or_none()
            if task and task.started_at:
                duration = int((get_db_timestamp() - task.started_at).total_seconds())
                update_data["actual_duration"] = duration
        
        if status_update.error_message:
            update_data["error_message"] = status_update.error_message
        
        if status_update.output_results:
            update_data["output_results"] = status_update.output_results
        
        await self.db.execute(
            update(Task)
            .where(Task.id == str(status_update.task_id))
            .values(**update_data)
        )
        
        # Update queue status
        await self.db.execute(
            update(TaskQueue)
            .where(TaskQueue.task_id == str(status_update.task_id))
            .values(queue_status=status_update.status)
        )
        
        await self.db.commit()
        return True
    
    async def create_agent_execution(self, execution_data: AgentExecutionCreate) -> AgentExecution:
        """Create an agent execution record."""
        
        execution = AgentExecution(
            id=str(uuid4()),
            task_id=str(execution_data.task_id),
            agent_type=execution_data.agent_type,
            agent_id=str(execution_data.agent_id) if execution_data.agent_id else None,
            execution_order=execution_data.execution_order,
            status=execution_data.status,
            input_data=execution_data.input_data,
            output_data=execution_data.output_data,
            artifacts=execution_data.artifacts,
            retry_count=execution_data.retry_count,
            max_retries=execution_data.max_retries
        )
        
        self.db.add(execution)
        await self.db.commit()
        await self.db.refresh(execution)
        
        return execution
    
    async def create_task_artifact(self, artifact_data: TaskArtifactCreate) -> TaskArtifact:
        """Create a task artifact."""
        
        artifact = TaskArtifact(
            id=str(uuid4()),
            task_id=str(artifact_data.task_id),
            agent_execution_id=str(artifact_data.agent_execution_id) if artifact_data.agent_execution_id else None,
            artifact_type=artifact_data.artifact_type,
            artifact_name=artifact_data.artifact_name,
            file_url=artifact_data.file_url,
            file_path=artifact_data.file_path,
            file_size=artifact_data.file_size,
            mime_type=artifact_data.mime_type,
            metadata=artifact_data.metadata,
            is_temporary=artifact_data.is_temporary,
            expires_at=artifact_data.expires_at,
            created_at=get_db_timestamp()
        )
        
        self.db.add(artifact)
        await self.db.commit()
        await self.db.refresh(artifact)
        
        return artifact
    
    async def get_conversation_artifacts(self, conversation_id: str) -> List[TaskArtifact]:
        """Get all artifacts for a conversation."""
        
        result = await self.db.execute(
            select(TaskArtifact)
            .join(Task)
            .where(Task.conversation_id == conversation_id)
            .order_by(TaskArtifact.created_at.desc())
        )
        
        return list(result.scalars().all())
    
    async def get_pending_tasks(self, limit: int = 10) -> List[Task]:
        """Get pending tasks for processing."""
        
        result = await self.db.execute(
            select(Task)
            .join(TaskQueue)
            .where(
                and_(
                    Task.status == "pending",
                    TaskQueue.queue_status == "queued"
                )
            )
            .order_by(Task.priority.desc(), Task.created_at.asc())
            .limit(limit)
        )
        
        return list(result.scalars().all())
    
    async def get_next_pending_execution(self, task_id: str) -> Optional[AgentExecution]:
        """Get the next pending agent execution for a task."""
        
        result = await self.db.execute(
            select(AgentExecution)
            .where(
                and_(
                    AgentExecution.task_id == task_id,
                    AgentExecution.status == "pending"
                )
            )
            .order_by(AgentExecution.execution_order.asc())
            .limit(1)
        )
        
        return result.scalar_one_or_none()
    
    # Helper methods
    
    async def _update_conversation_current_task(self, conversation_id: str, task_id: str):
        """Update conversation's current task."""
        
        await self.db.execute(
            update(Conversation)
            .where(Conversation.id == conversation_id)
            .values(current_task_id=task_id)
        )
    
    async def _get_workflow_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow template by ID."""
        
        result = await self.db.execute(
            select(WorkflowTemplate)
            .where(WorkflowTemplate.id == template_id)
        )
        
        template = result.scalar_one_or_none()
        if template:
            return {
                "id": template.id,
                "name": template.name,
                "task_type": template.task_type,
                "version": template.version,
                "template_definition": template.template_definition
            }
        return None
    
    async def _get_active_workflow_template(self, task_type: str) -> Optional[Dict[str, Any]]:
        """Get active workflow template for task type."""
        
        result = await self.db.execute(
            select(WorkflowTemplate)
            .where(
                and_(
                    WorkflowTemplate.task_type == task_type,
                    WorkflowTemplate.is_active == True
                )
            )
            .order_by(WorkflowTemplate.version.desc())
            .limit(1)
        )
        
        template = result.scalar_one_or_none()
        if template:
            return {
                "id": template.id,
                "name": template.name,
                "task_type": template.task_type,
                "version": template.version,
                "template_definition": template.template_definition
            }
        return None
    
    async def _create_agent_executions_from_template(self, task_id: str, template: Dict[str, Any]):
        """Create agent executions from workflow template."""
        
        agents = template["template_definition"].get("agents", [])
        
        for agent_config in agents:
            execution_data = AgentExecutionCreate(
                task_id=UUID(task_id),
                agent_type=agent_config["type"],
                execution_order=agent_config["order"],
                input_data={"agent_config": agent_config}
            )
            
            await self.create_agent_execution(execution_data)
    
    def _estimate_duration_from_template(self, template: Dict[str, Any]) -> Optional[int]:
        """Estimate task duration from template."""
        
        # Simple estimation based on number of agents
        agents = template["template_definition"].get("agents", [])
        base_duration = len(agents) * 60  # 1 minute per agent
        
        # Adjust for parallel execution
        if template["template_definition"].get("parallel_execution", False):
            base_duration = max(60, base_duration // 2)  # At least 1 minute
        
        return base_duration
    
    async def _add_task_to_queue(self, task_id: str):
        """Add task to processing queue."""
        
        queue_entry = TaskQueue(
            id=str(uuid4()),
            task_id=str(task_id),
            queue_status="queued",
            created_at=get_db_timestamp()
        )
        
        self.db.add(queue_entry)
        await self.db.commit()
    
    async def cleanup_temporary_artifacts(self, older_than_days: int = 7):
        """Clean up temporary artifacts older than specified days."""
        
        cutoff_date = get_db_timestamp() - timedelta(days=older_than_days)
        
        result = await self.db.execute(
            select(TaskArtifact)
            .where(
                and_(
                    TaskArtifact.is_temporary == True,
                    TaskArtifact.expires_at < cutoff_date
                )
            )
        )
        
        artifacts = result.scalars().all()
        
        for artifact in artifacts:
            await self.db.delete(artifact)
        
        await self.db.commit()
        
        return len(artifacts)