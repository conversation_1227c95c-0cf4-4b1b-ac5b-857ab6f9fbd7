"""
Service interfaces following Interface Segregation Principle.
Each interface has a specific responsibility.
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from ..models.schemas import (
    ChatMessageCreate,
    ChatMessageResponse,
    DesignConceptCreate,
    DesignConceptResponse,
    DesignModificationCreate,
    DesignModificationResponse,
    RenderRequest,
    RenderResultResponse,
    UserCreate,
    UserResponse,
    UserUpdate,
)


class IUserService(ABC):
    """User service interface."""
    
    @abstractmethod
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create a new user."""
        pass
    
    @abstractmethod
    async def get_user_by_id(self, user_id: UUID) -> Optional[UserResponse]:
        """Get user by ID."""
        pass
    
    @abstractmethod
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """Get user by email."""
        pass
    
    @abstractmethod
    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[UserResponse]:
        """Update user."""
        pass
    
    @abstractmethod
    async def delete_user(self, user_id: UUID) -> bool:
        """Delete user."""
        pass


class IAuthService(ABC):
    """Authentication service interface."""
    
    @abstractmethod
    async def authenticate_user(self, email: str, password: str) -> Optional[UserResponse]:
        """Authenticate user with email and password."""
        pass
    
    @abstractmethod
    async def create_access_token(self, user_id: UUID) -> str:
        """Create access token for user."""
        pass
    
    @abstractmethod
    async def verify_token(self, token: str) -> Optional[UUID]:
        """Verify access token and return user ID."""
        pass
    
    @abstractmethod
    def hash_password(self, password: str) -> str:
        """Hash password."""
        pass
    
    @abstractmethod
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        pass


class IDesignConceptService(ABC):
    """Design concept service interface."""
    
    @abstractmethod
    async def create_concept(self, user_id: UUID, concept_data: DesignConceptCreate) -> DesignConceptResponse:
        """Create a new design concept."""
        pass
    
    @abstractmethod
    async def get_concept_by_id(self, concept_id: UUID) -> Optional[DesignConceptResponse]:
        """Get concept by ID."""
        pass
    
    @abstractmethod
    async def get_conversation_concepts(self, conversation_id: UUID, user_id: UUID, skip: int = 0, limit: int = 100) -> List[DesignConceptResponse]:
        """Get conversation's concepts."""
        pass
    
    @abstractmethod
    async def set_active_concept(self, concept_id: UUID) -> bool:
        """Set concept as active."""
        pass
    
    @abstractmethod
    async def delete_concept(self, concept_id: UUID) -> bool:
        """Delete concept."""
        pass


class IDesignModificationService(ABC):
    """Design modification service interface."""
    
    @abstractmethod
    async def create_modification(self, modification_data: DesignModificationCreate) -> DesignModificationResponse:
        """Create a new design modification."""
        pass
    
    @abstractmethod
    async def get_concept_modifications(self, concept_id: UUID) -> List[DesignModificationResponse]:
        """Get concept's modifications."""
        pass


class IChatService(ABC):
    """Chat service interface."""
    
    @abstractmethod
    async def create_message(self, conversation_id: UUID, message_data: ChatMessageCreate) -> ChatMessageResponse:
        """Create a new chat message."""
        pass
    
    @abstractmethod
    async def get_conversation_messages(self, conversation_id: UUID, skip: int = 0, limit: int = 100) -> List[ChatMessageResponse]:
        """Get conversation's chat messages."""
        pass
    
    @abstractmethod
    async def delete_message(self, message_id: UUID) -> bool:
        """Delete chat message."""
        pass





class IRenderService(ABC):
    """3D render service interface."""
    
    @abstractmethod
    async def create_render_request(self, render_data: RenderRequest) -> RenderResultResponse:
        """Create a new render request."""
        pass
    
    @abstractmethod
    async def get_render_result(self, render_id: UUID) -> Optional[RenderResultResponse]:
        """Get render result by ID."""
        pass
    
    @abstractmethod
    async def get_concept_renders(self, concept_id: UUID) -> List[RenderResultResponse]:
        """Get concept's render results."""
        pass
    
    @abstractmethod
    async def process_render(self, render_id: UUID) -> bool:
        """Process render request."""
        pass


class IFileService(ABC):
    """File service interface."""
    
    @abstractmethod
    async def upload_file(self, file_content: bytes, filename: str, content_type: str) -> str:
        """Upload file and return URL."""
        pass
    
    @abstractmethod
    async def delete_file(self, file_url: str) -> bool:
        """Delete file."""
        pass
    
    @abstractmethod
    async def get_file_info(self, file_url: str) -> Optional[dict]:
        """Get file information."""
        pass
