"""
Workflow package for LangGraph-based design workflows.

This package contains all workflow implementations for different design types:
- BaseWorkflow: Abstract base class for all workflows
- DesignWorkflow: Universal design workflow supporting multiple design types
- FashionWorkflow: Specialized workflow for fashion design
- PosterWorkflow: Specialized workflow for poster design
- DramaWorkflow: Specialized workflow for drama production
"""

from .base_workflow import BaseWorkflow
from .design_workflow import DesignWorkflow
from .fashion_workflow import FashionWorkflow
from .poster_workflow import PosterWorkflow
from .drama_workflow import DramaWorkflow

# Import shared types and enums
from .base_workflow import (
    ConversationState,
    DesignRequirements,
    IntentType,
    WorkflowState
)

__all__ = [
    # Base classes
    "BaseWorkflow",
    
    # Workflow implementations
    "DesignWorkflow",
    "FashionWorkflow",
    "PosterWorkflow",
    "DramaWorkflow",
    
    # Shared types
    "ConversationState",
    "DesignRequirements", 
    "IntentType",
    "WorkflowState"
]
