"""
Drama-specific workflow implementation using LangGraph.

This workflow implements a comprehensive drama production process using LangGraph,
providing structured stages for script writing, character design, scene planning,
and video production with intelligent routing and quality control.
"""

import logging
import json
import re
import asyncio
from typing import Dict, Any, Optional, List, TypedDict
from enum import Enum
from uuid import uuid4
from langchain_core.messages import HumanMessage, AIMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .base_workflow import BaseWorkflow, ConversationState, DesignRequirements, IntentType, WorkflowState
from ..drama_models import DramaRequirements
from ...core.config import settings

logger = logging.getLogger(__name__)


class DramaStage(str, Enum):
    """剧情制作阶段枚举"""
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    SCRIPT_CONCEPT = "script_concept"
    CHARACTER_DESIGN = "character_design"
    SCENE_PLANNING = "scene_planning"
    VIDEO_PRODUCTION = "video_production"
    QUALITY_REVIEW = "quality_review"
    COMPREHENSIVE_PLAN = "comprehensive_plan"
    COMPLETED = "completed"


class DramaProductionState(TypedDict, total=False):
    """剧情制作状态定义"""
    # 基础信息
    user_input: str
    conversation_id: str
    user_id: str
    
    # 当前状态
    current_stage: DramaStage
    current_state: WorkflowState
    task_type: str
    
    # 剧情制作内容
    requirements: Optional[DramaRequirements]
    script_concept: Dict[str, Any]
    character_design: Dict[str, Any]
    scene_plan: Dict[str, Any]
    video_plan: Dict[str, Any]
    comprehensive_plan: Dict[str, Any]
    quality_feedback: Dict[str, Any]
    
    # 输出内容
    design_content: str
    production_phase: str
    
    # 对话历史
    messages: List[Dict[str, Any]]
    
    # 错误处理
    error_message: Optional[str]
    retry_count: int
    
    # 元数据
    metadata: Dict[str, Any]




class DramaWorkflow(BaseWorkflow):
    """
    Drama-specific workflow implementation using LangGraph.
    
    Implements a comprehensive drama production process with:
    - LangGraph-based structured workflow
    - Intelligent task routing
    - Quality control and review
    - Multi-stage production pipeline
    """
    
    def __init__(self, design_agent=None):
        """Initialize drama workflow with optional design agent."""
        super().__init__(design_agent=design_agent)
        
        logger.info("DramaWorkflow initialized with LangGraph")
    
    def _build_workflow(self) -> StateGraph:
        """构建LangGraph剧情制作工作流 - 实现BaseWorkflow抽象方法"""
        workflow = StateGraph(DramaProductionState)
        
        # 添加节点
        workflow.add_node("requirement_analysis", self._requirement_analysis_node)
        workflow.add_node("script_concept", self._script_concept_node)
        workflow.add_node("character_design", self._character_design_node)
        workflow.add_node("scene_planning", self._scene_planning_node)
        workflow.add_node("video_production", self._video_production_node)
        workflow.add_node("post_production", self._post_production_node)
        workflow.add_node("comprehensive_plan", self._comprehensive_plan_node)
        workflow.add_node("quality_review", self._quality_review_node)
        workflow.add_node("error_handler", self._error_handler_node)
        
        # 设置入口点
        workflow.set_entry_point("requirement_analysis")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "requirement_analysis",
            self._route_after_requirements,
            {
                "script_concept": "script_concept",
                "character_design": "character_design",
                "scene_planning": "scene_planning",
                "video_production": "video_production",
                "comprehensive_plan": "comprehensive_plan",
                "error": "error_handler"
            }
        )
        
        workflow.add_conditional_edges(
            "script_concept",
            self._route_after_script_concept,
            {
                "quality_review": "quality_review",
                "error": "error_handler"
            }
        )
        
        workflow.add_conditional_edges(
            "character_design",
            self._route_after_character_design,
            {
                "quality_review": "quality_review",
                "error": "error_handler"
            }
        )
        
        workflow.add_conditional_edges(
            "scene_planning",
            self._route_after_scene_planning,
            {
                "quality_review": "quality_review",
                "error": "error_handler"
            }
        )
        
        workflow.add_conditional_edges(
            "video_production",
            self._route_after_video_production,
            {
                "post_production": "post_production",
                "quality_review": "quality_review",
                "error": "error_handler"
            }
        )

        workflow.add_conditional_edges(
            "post_production",
            self._route_after_post_production,
            {
                "comprehensive_plan": "comprehensive_plan",
                "quality_review": "quality_review",
                "error": "error_handler"
            }
        )
        
        workflow.add_conditional_edges(
            "comprehensive_plan",
            self._route_after_comprehensive_plan,
            {
                "quality_review": "quality_review",
                "error": "error_handler"
            }
        )
        
        workflow.add_conditional_edges(
            "quality_review",
            self._route_after_quality_review,
            {
                "completed": END,
                "error": "error_handler"
            }
        )
        
        # 添加到END的边
        workflow.add_edge("error_handler", END)
        
        # 编译工作流
        memory = MemorySaver()
        return workflow.compile(checkpointer=memory)
    
    def _route_after_requirements(self, state: DramaProductionState) -> str:
        """需求分析后的路由 - 简化版，优先走剧本创作路径"""
        if state.get("error_message"):
            return "error"

        task_type = state.get("task_type", "")

        # 对于短剧创作，直接走剧本创作路径
        if task_type == "script_writing":
            return "script_concept"
        elif task_type == "character_design":
            return "character_design"
        elif task_type == "scene_planning":
            return "scene_planning"
        elif task_type == "video_production":
            return "video_production"
        else:
            # 默认走剧本创作路径，而不是综合计划
            return "script_concept"
    
    def _route_after_script_concept(self, state: DramaProductionState) -> str:
        """剧本构思后的路由"""
        return "quality_review" if not state.get("error_message") else "error"
    
    def _route_after_character_design(self, state: DramaProductionState) -> str:
        """角色设计后的路由"""
        return "quality_review" if not state.get("error_message") else "error"
    
    def _route_after_scene_planning(self, state: DramaProductionState) -> str:
        """场景规划后的路由"""
        return "quality_review" if not state.get("error_message") else "error"
    
    def _route_after_video_production(self, state: DramaProductionState) -> str:
        """视频制作后的路由"""
        if state.get("error_message"):
            return "error"
        # 视频制作完成后进入后期制作
        return "post_production"

    def _route_after_post_production(self, state: DramaProductionState) -> str:
        """后期制作后的路由"""
        if state.get("error_message"):
            return "error"
        # 后期制作完成后进入综合方案
        return "comprehensive_plan"
    
    def _route_after_comprehensive_plan(self, state: DramaProductionState) -> str:
        """综合方案后的路由"""
        return "quality_review" if not state.get("error_message") else "error"
    
    def _route_after_quality_review(self, state: DramaProductionState) -> str:
        """质量检查后的路由"""
        return "completed" if not state.get("error_message") else "error"
    
    async def _extract_requirements(self, user_input: str) -> DramaRequirements:
        """从用户输入中提取剧情制作需求"""
        
        extraction_prompt = f"""
        Extract drama production requirements from the user's input and return ONLY a valid JSON object.

        User input: "{user_input}"

        Extract the following drama-specific information:
        - genre: 剧情类型 (如：爱情, 动作, 喜剧, 剧情, 惊悚, 恐怖, 科幻, 奇幻, 纪录片, 动画)
        - target_audience: 目标观众 (如：儿童, 青少年, 成人, 家庭, 大众)
        - duration_minutes: 时长分钟数 (如：30, 60, 90, 120)
        - budget_level: 预算等级 (如：低, 中, 高, 超高)
        - production_scale: 制作规模 (如：小制作, 中等制作, 大制作, 大片)
        - setting: 故事背景 (如：现代, 古代, 未来, 虚构世界, 魔幻, 科幻等)
        - theme: 主题 (如：成长, 爱情, 友谊, 冒险, 救赎, 正义, 家庭等)
        - characters_count: 角色数量 (数字)
        - key_scenes_count: 关键场景数量 (数字)
        - visual_style: 视觉风格 (如：写实, 卡通, 美漫, 日漫, 水墨, 油画等)
        - dialogue_style: 对话风格 (如：自然, 文艺, 口语化, 正式, 幽默等)

        IMPORTANT: Return ONLY a valid JSON object, no explanations or markdown formatting.
        Use appropriate default values for unmentioned fields:
        - genre: "剧情"
        - target_audience: "成人"
        - duration_minutes: 5
        - budget_level: "中等"
        - production_scale: "中等制作"
        - characters_count: 2
        - key_scenes_count: 5

        Example format:
        {{
            "genre": "爱情",
            "target_audience": "成人",
            "duration_minutes": 90,
            "budget_level": "中等",
            "production_scale": "中等制作",
            "setting": "现代",
            "theme": "爱情",
            "characters_count": 4,
            "key_scenes_count": 8,
            "visual_style": "写实",
            "dialogue_style": "自然"
        }}
        """
        
        try:
            from langchain_core.messages import HumanMessage

            response = await self.llm.ainvoke([HumanMessage(content=extraction_prompt)])
            response_content = response.content.strip()

            logger.info(f"LLM response for drama requirements extraction: {response_content}")

            # 尝试提取JSON内容（处理可能的markdown格式）
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response_content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
            else:
                json_content = response_content

            # 解析JSON
            requirements_data = json.loads(json_content)
            
            # 处理枚举值映射
            genre_mapping = {
                "爱情": "爱情", "动作": "动作", "喜剧": "喜剧", "剧情": "剧情", 
                "惊悚": "惊悚", "恐怖": "恐怖", "科幻": "科幻", "奇幻": "奇幻", 
                "纪录片": "纪录片", "动画": "动画", "悲剧": "剧情", "文艺": "剧情",
                "悬疑": "惊悚", "未指定": "剧情"
            }
            
            audience_mapping = {
                "儿童": "儿童", "青少年": "青少年", "成人": "成人", 
                "家庭": "家庭", "大众": "大众", "全年龄": "大众", "未指定": "成人"
            }
            
            # 安全地获取和转换数值
            def safe_int(value, default):
                try:
                    return int(value) if str(value).isdigit() else default
                except (ValueError, TypeError):
                    return default
            
            # 创建DramaRequirements对象，处理验证错误
            try:
                requirements = DramaRequirements(
                    category="drama",
                    style=requirements_data.get("genre", "剧情"),
                    genre=genre_mapping.get(requirements_data.get("genre", "剧情"), "剧情"),
                    target_audience=audience_mapping.get(requirements_data.get("target_audience", "成人"), "成人"),
                    duration=safe_int(requirements_data.get("duration_minutes", 5), 5),
                    budget=requirements_data.get("budget_level", "中等"),
                    production_scale=self._map_production_scale(requirements_data.get("production_scale", "中等制作")),
                    setting=requirements_data.get("setting", "现代"),
                    theme=requirements_data.get("theme", "成长"),
                    main_character_count=safe_int(requirements_data.get("characters_count", 2), 2),
                    supporting_character_count=3,
                    scene_count=safe_int(requirements_data.get("key_scenes_count", 5), 5),
                    visual_style=requirements_data.get("visual_style", "写实"),
                    dialogue_style=requirements_data.get("dialogue_style", "自然"),
                    narrative_style="线性"
                )
                
                logger.info(f"Extracted drama requirements: {requirements}")
                return requirements
                
            except Exception as validation_error:
                logger.error(f"Validation error creating DramaRequirements: {validation_error}")
                # 返回默认需求
                return DramaRequirements()

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in drama requirements extraction: {e}")
            logger.error(f"Raw response: {response_content}")
            return DramaRequirements()
        
        except Exception as e:
            logger.error(f"Drama requirements extraction failed: {e}")
            return DramaRequirements()
    
    def _map_production_scale(self, scale_str: str) -> str:
        """Map production scale string to enum values."""
        from ..drama_models import ProductionScale
        
        scale_mapping = {
            "短视频": "小制作",
            "短片": "中等制作", 
            "网剧": "中等制作",
            "电影": "大制作",
            "大片": "大片",
            "低": "小制作",
            "中": "中等制作",
            "高": "大制作",
            "超高": "大片",
            "小制作": "小制作",
            "中等制作": "中等制作",
            "大制作": "大制作",
            "未指定": "中等制作"
        }
        
        mapped_value = scale_mapping.get(scale_str, "中等制作")
        
        # 确保返回的是有效的枚举值
        try:
            return ProductionScale(mapped_value).value
        except ValueError:
            return ProductionScale.MEDIUM.value
    
    # ============================================================================
    # LangGraph 节点实现
    # ============================================================================
    
    async def _requirement_analysis_node(self, state: DramaProductionState) -> DramaProductionState:
        """需求分析节点"""
        try:
            user_input = state["user_input"]

            # 使用现有的需求提取方法
            requirements = await self._extract_requirements(user_input)

            # 智能识别任务类型
            task_type = self._determine_task_type(user_input)

            state["requirements"] = requirements
            state["task_type"] = task_type  # 更新任务类型
            state["current_stage"] = DramaStage.REQUIREMENT_ANALYSIS
            state["current_state"] = WorkflowState.COMPLETED

            logger.info(f"Requirement analysis completed, task_type: {task_type}")
            return state
            
        except Exception as e:
            state["error_message"] = f"需求分析失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            return state

    def _determine_task_type(self, user_input: str) -> str:
        """智能识别任务类型"""
        user_input_lower = user_input.lower()

        # 剧本创作关键词
        script_keywords = ["剧本", "短剧", "剧情", "故事", "创作", "编剧", "脚本", "戏剧", "话剧", "小品"]

        # 角色设计关键词
        character_keywords = ["角色设计", "人物设计", "角色形象", "人物形象", "角色造型"]

        # 场景规划关键词
        scene_keywords = ["场景设计", "场景规划", "分镜", "镜头", "拍摄计划"]

        # 视频制作关键词
        video_keywords = ["视频制作", "拍摄", "制作视频", "视频生成", "影片制作"]

        # 检查关键词匹配
        if any(keyword in user_input_lower for keyword in character_keywords):
            return "character_design"
        elif any(keyword in user_input_lower for keyword in scene_keywords):
            return "scene_planning"
        elif any(keyword in user_input_lower for keyword in video_keywords):
            return "video_production"
        elif any(keyword in user_input_lower for keyword in script_keywords):
            return "script_writing"
        else:
            # 默认为剧本创作（因为大多数短剧请求都是要创作剧本）
            return "script_writing"

    async def _script_concept_node(self, state: DramaProductionState) -> DramaProductionState:
        """剧本构思节点 - 扩展版：生成剧本 + 分镜脚本"""
        try:
            requirements = state["requirements"]
            user_input = state["user_input"]
            duration = requirements.duration if requirements else 5

            # 第一步：生成剧本
            script_prompt = f"""
            你是一个专业的编剧，请根据以下要求创作剧本：

            用户需求：{user_input}
            剧情类型：{requirements.genre if requirements else '剧情'}
            目标观众：{requirements.target_audience if requirements else '成人'}
            时长：{duration}分钟
            故事背景：{requirements.setting if requirements else '现代'}
            主题：{requirements.theme if requirements else '成长'}
            对话风格：{requirements.dialogue_style if requirements else '自然'}

            请提供完整的剧本，包括：
            1. 剧本标题
            2. 故事梗概（200字以内）
            3. 主要角色介绍（3-5个角色，包含外貌特征）
            4. 完整的剧本内容（包含场景、对话、动作描述）
            5. 三幕结构划分

            请用中文回复，保持专业性和创意性。剧本要适合{duration}分钟的短剧制作。
            """

            script_response = await self.llm.ainvoke([HumanMessage(content=script_prompt)])

            # 第二步：基于剧本生成分镜脚本
            storyboard_prompt = f"""
            你是一个专业的分镜师，请基于以下剧本创建详细的分镜脚本：

            剧本内容：
            {script_response.content}

            请为这个{duration}分钟的短剧创建分镜脚本，要求：

            1. 将剧本分解为8-12个关键镜头（根据时长调整）
            2. 每个镜头包含以下信息：
               - 镜头编号（Shot_01, Shot_02...）
               - 场景描述（地点、时间、环境）
               - 角色信息（出现的角色、位置、动作）
               - 镜头类型（特写/中景/全景/远景）
               - 镜头运动（固定/推拉/摇移/跟随）
               - 预计时长（秒）
               - 画面构图描述（用于后续图片生成）
               - 对话内容（如有）
               - 情绪氛围（紧张/温馨/激动等）

            请以JSON格式输出，每个镜头为一个对象，确保总时长约为{duration * 60}秒。

            输出格式示例：
            [
              {{
                "shot_id": "Shot_01",
                "scene_location": "咖啡厅内部",
                "scene_time": "下午",
                "characters": ["主角A", "主角B"],
                "shot_type": "中景",
                "camera_movement": "固定",
                "duration": 15,
                "composition": "两人面对面坐在咖啡桌前，温暖的光线从窗户洒入",
                "dialogue": "你好，很高兴见到你。",
                "emotion": "温馨",
                "action_description": "主角A微笑着向主角B打招呼"
              }}
            ]
            """

            storyboard_response = await self.llm.ainvoke([HumanMessage(content=storyboard_prompt)])

            # 解析和验证分镜脚本
            storyboard_result = self._parse_and_validate_storyboard(
                storyboard_response.content,
                duration * 60  # 转换为秒
            )

            # 保存结果到状态
            state["script_concept"] = {
                "script": script_response.content,
                "storyboard": storyboard_result,
                "title": "剧本与分镜脚本",
                "type": "script_storyboard"
            }

            # 为兼容性保留原有字段
            state["design_content"] = script_response.content
            state["production_phase"] = "script"
            state["current_stage"] = DramaStage.SCRIPT_CONCEPT
            state["current_state"] = WorkflowState.COMPLETED
            state["image_url"] = None

            logger.info("Script concept and storyboard completed")
            return state

        except Exception as e:
            state["error_message"] = f"剧本构思失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            logger.error(f"Script concept node error: {e}")
            return state

    def _parse_and_validate_storyboard(self, storyboard_content: str, target_duration: int) -> Dict[str, Any]:
        """解析和验证分镜脚本"""
        try:
            import json
            import re

            # 尝试提取JSON部分（有时LLM会在JSON前后添加说明文字）
            json_match = re.search(r'\[.*\]', storyboard_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                storyboard_data = json.loads(json_str)
            else:
                storyboard_data = json.loads(storyboard_content)

            # 验证数据结构
            if not isinstance(storyboard_data, list):
                raise ValueError("Storyboard data should be a list")

            total_duration = 0
            validated_shots = []

            for i, shot in enumerate(storyboard_data):
                # 确保必要字段存在
                validated_shot = {
                    "shot_id": shot.get("shot_id", f"Shot_{i+1:02d}"),
                    "scene_location": shot.get("scene_location", "未指定场景"),
                    "scene_time": shot.get("scene_time", "白天"),
                    "characters": shot.get("characters", []),
                    "shot_type": shot.get("shot_type", "中景"),
                    "camera_movement": shot.get("camera_movement", "固定"),
                    "duration": int(shot.get("duration", 10)),
                    "composition": shot.get("composition", ""),
                    "dialogue": shot.get("dialogue", ""),
                    "emotion": shot.get("emotion", "中性"),
                    "action_description": shot.get("action_description", "")
                }

                total_duration += validated_shot["duration"]
                validated_shots.append(validated_shot)

            return {
                "shots": validated_shots,
                "total_duration": total_duration,
                "shot_count": len(validated_shots),
                "parsed": True,
                "validation_passed": True
            }

        except Exception as e:
            logger.warning(f"Failed to parse storyboard: {e}")
            return {
                "raw_content": storyboard_content,
                "parsed": False,
                "validation_passed": False,
                "error": str(e)
            }

    def _parse_character_designs(self, character_content: str) -> Dict[str, Any]:
        """解析角色设计JSON"""
        try:
            import json
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'\[.*\]', character_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                character_data = json.loads(json_str)
            else:
                character_data = json.loads(character_content)

            # 验证数据结构
            if not isinstance(character_data, list):
                raise ValueError("Character data should be a list")

            validated_characters = []
            for i, char in enumerate(character_data):
                validated_char = {
                    "name": char.get("name", f"角色{i+1}"),
                    "age": char.get("age", 25),
                    "gender": char.get("gender", "未指定"),
                    "personality": char.get("personality", ""),
                    "background": char.get("background", ""),
                    "appearance": char.get("appearance", ""),
                    "clothing": char.get("clothing", ""),
                    "role_in_story": char.get("role_in_story", ""),
                    "relationships": char.get("relationships", "")
                }
                validated_characters.append(validated_char)

            return {
                "characters": validated_characters,
                "character_count": len(validated_characters),
                "parsed": True,
                "validation_passed": True
            }

        except Exception as e:
            logger.warning(f"Failed to parse character designs: {e}")
            return {
                "raw_content": character_content,
                "parsed": False,
                "validation_passed": False,
                "error": str(e)
            }

    def _create_character_image_prompt(self, character: Dict[str, Any], requirements: Any) -> str:
        """创建角色图片生成提示词 - 基于剧本角色信息"""

        # 基础描述
        name = character.get("name", "角色")
        age = character.get("age", 25)
        gender = character.get("gender", "")
        appearance = character.get("appearance", "")
        clothing = character.get("clothing", "")
        key_traits = character.get("key_traits", "")
        personality = character.get("personality", "")

        # 构建详细的提示词
        prompt_parts = []

        # 年龄和性别
        if gender and age:
            if gender == "男":
                prompt_parts.append(f"{age} year old man")
            elif gender == "女":
                prompt_parts.append(f"{age} year old woman")
            else:
                prompt_parts.append(f"{age} year old person")

        # 外貌描述（转换为英文）
        if appearance:
            # 简化的中文到英文转换（实际项目中可以使用翻译API）
            appearance_en = self._translate_appearance_to_english(appearance)
            prompt_parts.append(appearance_en)

        # 服装描述
        if clothing:
            clothing_en = self._translate_clothing_to_english(clothing)
            prompt_parts.append(f"wearing {clothing_en}")

        # 关键特征
        if key_traits:
            traits_en = self._translate_traits_to_english(key_traits)
            prompt_parts.append(traits_en)

        # 基于性格的表情和姿态
        if personality:
            expression = self._get_expression_from_personality(personality)
            prompt_parts.append(expression)

        # 视觉风格
        visual_style = requirements.visual_style if requirements else "写实"
        if visual_style == "写实":
            style_desc = "photorealistic, high quality, detailed, professional photography"
        elif visual_style == "动漫":
            style_desc = "anime style, manga style, cel shading"
        elif visual_style == "卡通":
            style_desc = "cartoon style, cute, stylized"
        else:
            style_desc = "artistic style, concept art"

        # 组合提示词
        character_desc = ", ".join(prompt_parts)

        # 最终提示词 - 专门为角色设计优化
        final_prompt = f"Character portrait: {character_desc}, {style_desc}, studio lighting, neutral background, character design sheet, front view, clear facial features, consistent character design"

        return final_prompt

    def _translate_appearance_to_english(self, appearance: str) -> str:
        """简化的外貌描述中英转换"""
        # 这里是简化版本，实际项目中建议使用专业翻译API
        translations = {
            "高瘦": "tall and slim",
            "中等身材": "medium build",
            "短发": "short hair",
            "长发": "long hair",
            "黑发": "black hair",
            "棕发": "brown hair",
            "圆脸": "round face",
            "方脸": "square face",
            "大眼睛": "big eyes",
            "小眼睛": "small eyes",
            "高鼻梁": "high nose bridge",
            "戴眼镜": "wearing glasses",
            "胡须": "beard",
            "微胖": "slightly chubby",
            "瘦": "thin"
        }

        result = appearance
        for cn, en in translations.items():
            result = result.replace(cn, en)

        return result

    def _translate_clothing_to_english(self, clothing: str) -> str:
        """简化的服装描述中英转换"""
        translations = {
            "T恤": "t-shirt",
            "衬衫": "shirt",
            "牛仔裤": "jeans",
            "运动裤": "sweatpants",
            "休闲装": "casual wear",
            "商务装": "business attire",
            "运动鞋": "sneakers",
            "皮鞋": "leather shoes",
            "帽子": "hat",
            "眼镜": "glasses"
        }

        result = clothing
        for cn, en in translations.items():
            result = result.replace(cn, en)

        return result

    def _translate_traits_to_english(self, traits: str) -> str:
        """简化的特征描述中英转换"""
        translations = {
            "友善": "friendly",
            "严肃": "serious",
            "活泼": "lively",
            "内向": "introverted",
            "外向": "extroverted",
            "自信": "confident",
            "害羞": "shy"
        }

        result = traits
        for cn, en in translations.items():
            result = result.replace(cn, en)

        return result

    def _get_expression_from_personality(self, personality: str) -> str:
        """根据性格特点生成表情描述"""
        if "开朗" in personality or "乐观" in personality:
            return "smiling, cheerful expression"
        elif "严肃" in personality or "认真" in personality:
            return "serious expression, focused look"
        elif "害羞" in personality or "内向" in personality:
            return "gentle expression, soft smile"
        elif "自信" in personality:
            return "confident expression, determined look"
        else:
            return "natural expression, pleasant demeanor"

    async def _generate_character_image(self, prompt: str, character_name: str) -> Optional[Dict[str, Any]]:
        """生成角色图片"""
        try:
            # 导入图像生成服务
            from ..image_generation.factory import ImageGenerationManager
            from ..image_generation.base import (
                ImageGenerationRequest,
                ImageGenerationProvider,
                ProviderConfig
            )

            # 创建图像生成管理器
            manager = ImageGenerationManager(default_provider=ImageGenerationProvider.DASHSCOPE)

            # 配置Dashscope服务
            from ...core.config import settings
            dashscope_config = ProviderConfig(
                provider=ImageGenerationProvider.DASHSCOPE,
                api_key=settings.dashscope_api_key,
                base_url="https://dashscope.aliyuncs.com"  # 使用默认的Dashscope URL
            )
            manager.configure_provider(dashscope_config)

            # 创建生成请求
            request = ImageGenerationRequest(
                prompt=prompt,
                width=512,
                height=768,  # 角色图片使用竖版比例
                num_images=1,
                quality="high"
            )

            # 生成图片
            service = manager.get_service()
            result = await service.generate_image(request)

            if result.status == "success" and result.image_urls:
                return {
                    "image_url": result.image_urls[0],
                    "character_name": character_name,
                    "prompt": prompt,
                    "metadata": {
                        "generation_time": result.metadata.get("generation_time", 0),
                        "model": result.metadata.get("model", "unknown"),
                        "provider": "dashscope"
                    }
                }
            else:
                logger.warning(f"Failed to generate image for {character_name}: {result.error_message}")
                return None

        except Exception as e:
            logger.error(f"Error generating character image for {character_name}: {e}")
            return None

    def _parse_scene_plans(self, scene_content: str) -> Dict[str, Any]:
        """解析场景规划JSON"""
        try:
            import json
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'\[.*\]', scene_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                scene_data = json.loads(json_str)
            else:
                scene_data = json.loads(scene_content)

            # 验证数据结构
            if not isinstance(scene_data, list):
                raise ValueError("Scene data should be a list")

            validated_scenes = []
            for i, scene in enumerate(scene_data):
                validated_scene = {
                    "shot_id": scene.get("shot_id", f"Shot_{i+1:02d}"),
                    "scene_location": scene.get("scene_location", "未指定场景"),
                    "scene_time": scene.get("scene_time", "白天"),
                    "environment_description": scene.get("environment_description", ""),
                    "props_needed": scene.get("props_needed", []),
                    "lighting_setup": scene.get("lighting_setup", "自然光"),
                    "camera_position": scene.get("camera_position", "正面"),
                    "background_elements": scene.get("background_elements", ""),
                    "color_palette": scene.get("color_palette", "自然色调"),
                    "mood_atmosphere": scene.get("mood_atmosphere", "中性"),
                    "technical_notes": scene.get("technical_notes", "")
                }
                validated_scenes.append(validated_scene)

            return {
                "scenes": validated_scenes,
                "scene_count": len(validated_scenes),
                "parsed": True,
                "validation_passed": True
            }

        except Exception as e:
            logger.warning(f"Failed to parse scene plans: {e}")
            return {
                "raw_content": scene_content,
                "parsed": False,
                "validation_passed": False,
                "error": str(e)
            }

    def _create_scene_image_prompt(
        self,
        scene: Dict[str, Any],
        shot: Dict[str, Any],
        characters: List[Dict[str, Any]],
        requirements: Any
    ) -> str:
        """创建场景图片生成提示词 - 基于分镜脚本和角色信息"""

        # 基础场景信息
        shot_id = scene.get("shot_id", "")
        location = scene.get("scene_location", "")
        time = scene.get("scene_time", "白天")
        environment = scene.get("environment_description", "")
        lighting = scene.get("lighting_setup", "")
        mood = scene.get("mood_atmosphere", "")
        color_palette = scene.get("color_palette", "")

        # 分镜信息
        shot_type = shot.get("shot_type", "中景")
        composition = shot.get("composition", "")
        characters_in_shot = shot.get("characters", [])
        action_description = shot.get("action_description", "")
        emotion = shot.get("emotion", "")

        # 构建提示词
        prompt_parts = []

        # 场景描述
        if location:
            prompt_parts.append(f"Scene: {location}")

        # 时间和光线
        time_lighting = []
        if time:
            if time == "白天":
                time_lighting.append("daytime")
            elif time == "晚上":
                time_lighting.append("nighttime")
            elif time == "下午":
                time_lighting.append("afternoon")
            else:
                time_lighting.append(time)

        if lighting and lighting != "自然光":
            time_lighting.append(self._translate_lighting_to_english(lighting))

        if time_lighting:
            prompt_parts.append(", ".join(time_lighting))

        # 角色描述
        if characters_in_shot and characters:
            character_descriptions = []
            for char_name in characters_in_shot:
                # 找到对应的角色信息
                char_info = None
                for char in characters:
                    if char.get("name") == char_name:
                        char_info = char
                        break

                if char_info:
                    # 简化的角色描述
                    char_desc = f"{char_info.get('age', 25)} year old"
                    if char_info.get('gender') == '男':
                        char_desc += " man"
                    elif char_info.get('gender') == '女':
                        char_desc += " woman"

                    # 添加关键外貌特征
                    appearance = char_info.get('appearance', '')
                    if '高瘦' in appearance:
                        char_desc += ", tall and slim"
                    elif '微胖' in appearance:
                        char_desc += ", slightly chubby"

                    if '戴眼镜' in appearance:
                        char_desc += ", wearing glasses"

                    # 添加服装
                    clothing = char_info.get('clothing', '')
                    if '白衬衫' in clothing:
                        char_desc += ", wearing white shirt"
                    elif 'T恤' in clothing:
                        char_desc += ", wearing t-shirt"

                    character_descriptions.append(char_desc)

            if character_descriptions:
                prompt_parts.append(f"Characters: {', '.join(character_descriptions)}")

        # 动作和构图
        if action_description:
            action_en = self._translate_action_to_english(action_description)
            prompt_parts.append(action_en)

        if composition:
            comp_en = self._translate_composition_to_english(composition)
            prompt_parts.append(comp_en)

        # 镜头类型
        shot_type_en = self._translate_shot_type_to_english(shot_type)
        prompt_parts.append(shot_type_en)

        # 情绪氛围
        if emotion and emotion != "中性":
            emotion_en = self._translate_emotion_to_english(emotion)
            prompt_parts.append(f"{emotion_en} atmosphere")

        # 环境描述
        if environment:
            env_en = self._translate_environment_to_english(environment)
            prompt_parts.append(env_en)

        # 视觉风格
        visual_style = requirements.visual_style if requirements else "写实"
        if visual_style == "写实":
            style_desc = "photorealistic, cinematic, high quality"
        elif visual_style == "动漫":
            style_desc = "anime style, manga style"
        elif visual_style == "卡通":
            style_desc = "cartoon style, stylized"
        else:
            style_desc = "artistic style"

        # 组合最终提示词
        scene_desc = ", ".join(prompt_parts)
        final_prompt = f"{scene_desc}, {style_desc}, professional cinematography, detailed scene"

        return final_prompt

    def _translate_lighting_to_english(self, lighting: str) -> str:
        """翻译光线设置"""
        translations = {
            "柔和光": "soft lighting",
            "强光": "bright lighting",
            "暖光": "warm lighting",
            "冷光": "cool lighting",
            "侧光": "side lighting",
            "背光": "backlight"
        }

        result = lighting
        for cn, en in translations.items():
            result = result.replace(cn, en)
        return result

    def _translate_action_to_english(self, action: str) -> str:
        """翻译动作描述"""
        translations = {
            "坐着": "sitting",
            "站着": "standing",
            "走路": "walking",
            "跑步": "running",
            "说话": "talking",
            "微笑": "smiling",
            "皱眉": "frowning",
            "争吵": "arguing",
            "拥抱": "hugging"
        }

        result = action
        for cn, en in translations.items():
            result = result.replace(cn, en)
        return result

    def _translate_composition_to_english(self, composition: str) -> str:
        """翻译构图描述"""
        translations = {
            "面对面": "face to face",
            "并排": "side by side",
            "背景": "in background",
            "前景": "in foreground",
            "中心": "in center",
            "左侧": "on left side",
            "右侧": "on right side"
        }

        result = composition
        for cn, en in translations.items():
            result = result.replace(cn, en)
        return result

    def _translate_shot_type_to_english(self, shot_type: str) -> str:
        """翻译镜头类型"""
        translations = {
            "特写": "close-up shot",
            "中景": "medium shot",
            "全景": "wide shot",
            "远景": "long shot",
            "大特写": "extreme close-up"
        }

        return translations.get(shot_type, "medium shot")

    def _translate_emotion_to_english(self, emotion: str) -> str:
        """翻译情绪氛围"""
        translations = {
            "紧张": "tense",
            "温馨": "warm",
            "激动": "exciting",
            "悲伤": "sad",
            "快乐": "happy",
            "愤怒": "angry",
            "平静": "calm"
        }

        return translations.get(emotion, "neutral")

    def _translate_environment_to_english(self, environment: str) -> str:
        """翻译环境描述"""
        translations = {
            "客厅": "living room",
            "卧室": "bedroom",
            "厨房": "kitchen",
            "办公室": "office",
            "咖啡厅": "cafe",
            "公园": "park",
            "街道": "street",
            "整洁": "clean and tidy",
            "杂乱": "messy",
            "温馨": "cozy",
            "现代": "modern"
        }

        result = environment
        for cn, en in translations.items():
            result = result.replace(cn, en)
        return result

    async def _generate_scene_image(self, prompt: str, shot_id: str) -> Optional[Dict[str, Any]]:
        """生成场景图片"""
        try:
            # 导入图像生成服务
            from ..image_generation.factory import ImageGenerationManager
            from ..image_generation.base import (
                ImageGenerationRequest,
                ImageGenerationProvider,
                ProviderConfig
            )

            # 创建图像生成管理器
            manager = ImageGenerationManager(default_provider=ImageGenerationProvider.DASHSCOPE)

            # 配置Dashscope服务
            from ...core.config import settings
            dashscope_config = ProviderConfig(
                provider=ImageGenerationProvider.DASHSCOPE,
                api_key=settings.dashscope_api_key,
                base_url="https://dashscope.aliyuncs.com"
            )
            manager.configure_provider(dashscope_config)

            # 创建生成请求 - 场景图片使用横版比例
            request = ImageGenerationRequest(
                prompt=prompt,
                width=768,
                height=512,  # 16:9 横版比例，适合场景
                num_images=1,
                quality="high"
            )

            # 生成图片
            service = manager.get_service()
            result = await service.generate_image(request)

            if result.status == "success" and result.image_urls:
                return {
                    "image_url": result.image_urls[0],
                    "shot_id": shot_id,
                    "prompt": prompt,
                    "metadata": {
                        "generation_time": result.metadata.get("generation_time", 0),
                        "model": result.metadata.get("model", "unknown"),
                        "provider": "dashscope"
                    }
                }
            else:
                logger.warning(f"Failed to generate scene image for {shot_id}: {result.error_message}")
                return None

        except Exception as e:
            logger.error(f"Error generating scene image for {shot_id}: {e}")
            return None

    def _parse_video_plans(self, video_content: str) -> Dict[str, Any]:
        """解析视频制作计划JSON"""
        try:
            import json
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'\[.*\]', video_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                video_data = json.loads(json_str)
            else:
                video_data = json.loads(video_content)

            # 验证数据结构
            if not isinstance(video_data, list):
                raise ValueError("Video data should be a list")

            validated_plans = []
            for i, plan in enumerate(video_data):
                validated_plan = {
                    "shot_id": plan.get("shot_id", f"Shot_{i+1:02d}"),
                    "video_duration": float(plan.get("video_duration", 5.0)),
                    "motion_type": plan.get("motion_type", "静态"),
                    "motion_description": plan.get("motion_description", ""),
                    "transition_in": plan.get("transition_in", "淡入"),
                    "transition_out": plan.get("transition_out", "淡出"),
                    "audio_requirements": plan.get("audio_requirements", ""),
                    "technical_notes": plan.get("technical_notes", ""),
                    "quality_settings": plan.get("quality_settings", "高质量")
                }
                validated_plans.append(validated_plan)

            return {
                "plans": validated_plans,
                "plan_count": len(validated_plans),
                "total_duration": sum(p["video_duration"] for p in validated_plans),
                "parsed": True,
                "validation_passed": True
            }

        except Exception as e:
            logger.warning(f"Failed to parse video plans: {e}")
            return {
                "raw_content": video_content,
                "parsed": False,
                "validation_passed": False,
                "error": str(e)
            }

    async def _generate_video_from_image(
        self,
        image_url: str,
        duration: float,
        motion_description: str,
        shot_id: str
    ) -> Optional[Dict[str, Any]]:
        """基于图片生成视频片段"""
        try:
            # 这里应该调用图生视频API（如RunwayML、Pika、Stable Video Diffusion等）
            # 由于当前没有配置图生视频服务，我们先模拟实现

            logger.info(f"Generating video for {shot_id} from image: {image_url}")
            logger.info(f"Duration: {duration}s, Motion: {motion_description}")

            # 模拟视频生成过程
            await asyncio.sleep(1)  # 模拟API调用时间

            # 模拟返回结果
            mock_video_url = f"https://example.com/videos/{shot_id}_video.mp4"

            return {
                "video_url": mock_video_url,
                "shot_id": shot_id,
                "duration": duration,
                "source_image": image_url,
                "motion_description": motion_description,
                "metadata": {
                    "generation_time": 1.0,
                    "model": "mock_video_model",
                    "provider": "mock_provider",
                    "status": "success"
                }
            }

            # 实际实现应该类似这样：
            # from ..video_generation.factory import VideoGenerationManager
            # from ..video_generation.base import VideoGenerationRequest
            #
            # manager = VideoGenerationManager()
            # request = VideoGenerationRequest(
            #     image_url=image_url,
            #     duration=duration,
            #     motion_prompt=motion_description
            # )
            # result = await manager.generate_video(request)
            # return result

        except Exception as e:
            logger.error(f"Error generating video for {shot_id}: {e}")
            return None

    def _parse_post_production_plans(self, post_content: str) -> Dict[str, Any]:
        """解析后期制作计划JSON"""
        try:
            import json
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', post_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                post_data = json.loads(json_str)
            else:
                post_data = json.loads(post_content)

            # 验证和标准化数据结构
            validated_plans = {
                "video_composition": {
                    "sequence_order": post_data.get("video_composition", {}).get("sequence_order", []),
                    "transitions": post_data.get("video_composition", {}).get("transitions", []),
                    "color_grading": post_data.get("video_composition", {}).get("color_grading", "自然色调"),
                    "visual_effects": post_data.get("video_composition", {}).get("visual_effects", [])
                },
                "audio_production": {
                    "voiceover_plan": post_data.get("audio_production", {}).get("voiceover_plan", ""),
                    "background_music": post_data.get("audio_production", {}).get("background_music", ""),
                    "sound_effects": post_data.get("audio_production", {}).get("sound_effects", []),
                    "audio_mixing": post_data.get("audio_production", {}).get("audio_mixing", "")
                },
                "final_output": {
                    "resolution": post_data.get("final_output", {}).get("resolution", "1920x1080"),
                    "frame_rate": post_data.get("final_output", {}).get("frame_rate", "24fps"),
                    "format": post_data.get("final_output", {}).get("format", "MP4"),
                    "quality": post_data.get("final_output", {}).get("quality", "高质量")
                }
            }

            return {
                "plans": validated_plans,
                "parsed": True,
                "validation_passed": True
            }

        except Exception as e:
            logger.warning(f"Failed to parse post production plans: {e}")
            return {
                "raw_content": post_content,
                "parsed": False,
                "validation_passed": False,
                "error": str(e)
            }

    async def _compose_video_segments(
        self,
        video_segments: List[Dict[str, Any]],
        composition_plan: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """拼接视频片段"""
        try:
            logger.info(f"Composing {len(video_segments)} video segments")

            # 模拟视频拼接过程
            await asyncio.sleep(2)  # 模拟处理时间

            # 按照sequence_order排序视频片段
            sequence_order = composition_plan.get("sequence_order", [])
            if sequence_order:
                ordered_segments = []
                for shot_id in sequence_order:
                    for segment in video_segments:
                        if segment.get("shot_id") == shot_id:
                            ordered_segments.append(segment)
                            break
                video_segments = ordered_segments if ordered_segments else video_segments

            total_duration = sum(seg.get("duration", 5) for seg in video_segments)

            # 模拟返回结果
            composed_video_url = "https://example.com/videos/composed_drama.mp4"

            return {
                "video_url": composed_video_url,
                "duration": total_duration,
                "segment_count": len(video_segments),
                "composition_plan": composition_plan,
                "metadata": {
                    "processing_time": 2.0,
                    "method": "mock_composition",
                    "status": "success"
                }
            }

            # 实际实现应该调用视频处理服务（如FFmpeg）

        except Exception as e:
            logger.error(f"Error composing video segments: {e}")
            return None

    async def _generate_voiceover(
        self,
        script_content: str,
        audio_plan: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """生成配音"""
        try:
            logger.info("Generating voiceover from script")

            # 模拟配音生成过程
            await asyncio.sleep(1.5)  # 模拟处理时间

            # 提取对话内容
            dialogues = self._extract_dialogues_from_script(script_content)

            # 模拟返回结果
            voiceover_url = "https://example.com/audio/voiceover.mp3"

            return {
                "audio_url": voiceover_url,
                "duration": 60.0,  # 模拟时长
                "dialogue_count": len(dialogues),
                "voiceover_plan": audio_plan.get("voiceover_plan", ""),
                "metadata": {
                    "generation_time": 1.5,
                    "method": "mock_tts",
                    "status": "success"
                }
            }

            # 实际实现应该调用TTS服务

        except Exception as e:
            logger.error(f"Error generating voiceover: {e}")
            return None

    async def _generate_background_music(
        self,
        duration: float,
        audio_plan: Dict[str, Any],
        requirements: Any
    ) -> Optional[Dict[str, Any]]:
        """生成背景音乐"""
        try:
            logger.info(f"Generating background music for {duration}s")

            # 模拟音乐生成过程
            await asyncio.sleep(1)  # 模拟处理时间

            # 根据剧情类型选择音乐风格
            genre = requirements.genre.value if requirements else "剧情"
            music_style = "轻松愉快" if genre == "喜剧" else "温馨感人"

            # 模拟返回结果
            music_url = "https://example.com/audio/background_music.mp3"

            return {
                "audio_url": music_url,
                "duration": duration,
                "music_style": music_style,
                "background_music_plan": audio_plan.get("background_music", ""),
                "metadata": {
                    "generation_time": 1.0,
                    "method": "mock_music_gen",
                    "status": "success"
                }
            }

            # 实际实现应该调用AI音乐生成服务

        except Exception as e:
            logger.error(f"Error generating background music: {e}")
            return None

    async def _mix_audio_tracks(
        self,
        voiceover_result: Dict[str, Any],
        music_result: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """混合音频轨道"""
        try:
            logger.info("Mixing audio tracks")

            # 模拟音频混合过程
            await asyncio.sleep(1)  # 模拟处理时间

            voiceover_url = voiceover_result.get("audio_url", "")
            music_url = music_result.get("audio_url", "")
            duration = max(
                voiceover_result.get("duration", 0),
                music_result.get("duration", 0)
            )

            # 模拟返回结果
            mixed_audio_url = "https://example.com/audio/mixed_audio.mp3"

            return {
                "audio_url": mixed_audio_url,
                "duration": duration,
                "voiceover_source": voiceover_url,
                "music_source": music_url,
                "metadata": {
                    "mixing_time": 1.0,
                    "method": "mock_audio_mix",
                    "status": "success"
                }
            }

            # 实际实现应该调用音频处理服务

        except Exception as e:
            logger.error(f"Error mixing audio tracks: {e}")
            return None

    async def _merge_video_audio(
        self,
        video_result: Dict[str, Any],
        audio_result: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """合并视频和音频"""
        try:
            logger.info("Merging video and audio")

            # 模拟视频音频合并过程
            await asyncio.sleep(2)  # 模拟处理时间

            video_url = video_result.get("video_url", "")
            audio_url = audio_result.get("audio_url", "")
            duration = min(
                video_result.get("duration", 0),
                audio_result.get("duration", 0)
            )

            # 模拟返回结果
            final_video_url = "https://example.com/videos/final_drama_with_audio.mp4"

            return {
                "video_url": final_video_url,
                "duration": duration,
                "video_source": video_url,
                "audio_source": audio_url,
                "metadata": {
                    "merging_time": 2.0,
                    "method": "mock_video_audio_merge",
                    "status": "success",
                    "final_format": "MP4",
                    "resolution": "1920x1080"
                }
            }

            # 实际实现应该调用视频处理服务（如FFmpeg）

        except Exception as e:
            logger.error(f"Error merging video and audio: {e}")
            return None

    def _extract_dialogues_from_script(self, script_content: str) -> List[Dict[str, str]]:
        """从剧本中提取对话内容"""
        try:
            import re

            # 简单的对话提取正则表达式
            dialogue_pattern = r'\*\*([^*]+)\*\*[^：]*：(.+?)(?=\n|\*\*|$)'
            matches = re.findall(dialogue_pattern, script_content, re.DOTALL)

            dialogues = []
            for character, dialogue in matches:
                dialogues.append({
                    "character": character.strip(),
                    "dialogue": dialogue.strip()
                })

            return dialogues

        except Exception as e:
            logger.warning(f"Error extracting dialogues: {e}")
            return []
    
    async def _character_design_node(self, state: DramaProductionState) -> DramaProductionState:
        """角色设计节点 - 扩展版：基于剧本生成一致性角色设计 + 角色图片生成"""
        try:
            requirements = state["requirements"]
            user_input = state["user_input"]
            script_concept = state.get("script_concept", {})

            # 提取剧本和分镜脚本信息
            script_content = script_concept.get("script", "")
            storyboard_data = script_concept.get("storyboard", {})

            # 从分镜脚本中提取角色列表（确保一致性）
            character_names = set()
            if isinstance(storyboard_data, dict) and storyboard_data.get("parsed", False):
                shots = storyboard_data.get("shots", [])
                for shot in shots:
                    characters = shot.get("characters", [])
                    character_names.update(characters)

            character_names_list = list(character_names) if character_names else []

            # 第一步：基于剧本内容生成角色设计描述
            character_prompt = f"""
            你是一个专业的角色设计师，请根据以下剧本内容设计角色：

            项目背景：{user_input}
            视觉风格：{requirements.visual_style if requirements else '写实'}
            故事背景：{requirements.setting if requirements else '现代'}

            剧本内容：
            {script_content}

            分镜脚本中出现的角色：{', '.join(character_names_list) if character_names_list else '请从剧本中提取'}

            请基于剧本内容为每个角色提供详细设计，确保与剧本描述保持一致，输出JSON格式：
            [
              {{
                "name": "角色姓名（必须与剧本中的姓名完全一致）",
                "age": 年龄,
                "gender": "性别",
                "personality": "性格特点（基于剧本中的表现）",
                "background": "背景故事",
                "appearance": "外貌描述（详细，包含身高、体型、发型、面部特征，符合剧本描述）",
                "clothing": "服装风格（详细描述，符合角色身份和剧本场景）",
                "role_in_story": "在故事中的作用（基于剧本分析）",
                "relationships": "与其他角色的关系（基于剧本内容）",
                "key_traits": "关键特征（用于图片生成的重要视觉特征）"
              }}
            ]

            重要要求：
            1. 角色姓名必须与剧本中完全一致
            2. 角色设计要符合剧本中的人物描述和行为
            3. 外貌描述要足够详细以便后续图片生成
            4. 确保角色之间的差异性和识别度
            """

            character_response = await self.llm.ainvoke([HumanMessage(content=character_prompt)])

            # 解析角色设计JSON
            character_designs = self._parse_character_designs(character_response.content)

            # 第二步：为每个角色生成图片
            character_images = []
            if character_designs.get("parsed", False):
                characters = character_designs.get("characters", [])

                for i, character in enumerate(characters):
                    try:
                        # 生成角色图片提示词
                        image_prompt = self._create_character_image_prompt(character, requirements)

                        # 调用图像生成服务
                        image_result = await self._generate_character_image(image_prompt, character["name"])

                        if image_result:
                            character_images.append({
                                "character_name": character["name"],
                                "image_url": image_result["image_url"],
                                "prompt": image_prompt,
                                "metadata": image_result.get("metadata", {})
                            })

                        # 避免API调用过于频繁
                        if i < len(characters) - 1:
                            await asyncio.sleep(1)

                    except Exception as e:
                        logger.warning(f"Failed to generate image for character {character.get('name', 'Unknown')}: {e}")
                        continue

            # 保存结果到状态
            state["character_design"] = {
                "character_descriptions": character_designs,
                "character_images": character_images,
                "title": "角色设计与图片",
                "type": "character_with_images"
            }

            # 为兼容性保留原有字段
            state["design_content"] = character_response.content
            state["production_phase"] = "character"
            state["current_stage"] = DramaStage.CHARACTER_DESIGN
            state["current_state"] = WorkflowState.COMPLETED

            # 如果有生成的图片，设置第一张作为主图片
            if character_images:
                state["image_url"] = character_images[0]["image_url"]
            else:
                state["image_url"] = None

            logger.info(f"Character design completed with {len(character_images)} character images")
            return state

        except Exception as e:
            state["error_message"] = f"角色设计失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            logger.error(f"Character design node error: {e}")
            return state
    
    async def _scene_planning_node(self, state: DramaProductionState) -> DramaProductionState:
        """场景规划节点 - 扩展版：基于分镜脚本和角色设计生成场景规划 + 分镜图片生成"""
        try:
            requirements = state["requirements"]
            user_input = state["user_input"]
            script_concept = state.get("script_concept", {})
            character_design = state.get("character_design", {})

            # 获取前置节点的输出
            script_content = script_concept.get("script", "")
            storyboard_data = script_concept.get("storyboard", {})
            character_descriptions = character_design.get("character_descriptions", {})
            character_images = character_design.get("character_images", [])

            # 提取分镜脚本信息
            shots_info = []
            if isinstance(storyboard_data, dict) and storyboard_data.get("parsed", False):
                shots_info = storyboard_data.get("shots", [])

            # 提取角色信息
            characters_info = []
            if isinstance(character_descriptions, dict) and character_descriptions.get("parsed", False):
                characters_info = character_descriptions.get("characters", [])

            # 第一步：基于分镜脚本生成详细的场景规划
            scene_prompt = f"""
            你是一个专业的场景规划师，请基于以下分镜脚本和角色信息设计详细的场景规划：

            项目背景：{user_input}
            预算等级：{requirements.budget if requirements else '中等'}
            制作规模：{requirements.production_scale if requirements else '短片'}
            故事背景：{requirements.setting if requirements else '现代'}

            剧本内容：
            {script_content}

            分镜脚本信息：
            {json.dumps(shots_info, ensure_ascii=False, indent=2) if shots_info else '无分镜脚本'}

            角色信息：
            {json.dumps(characters_info, ensure_ascii=False, indent=2) if characters_info else '无角色信息'}

            请基于分镜脚本为每个镜头提供详细的场景设计，输出JSON格式：
            [
              {{
                "shot_id": "镜头编号（与分镜脚本对应）",
                "scene_location": "场景位置",
                "scene_time": "时间",
                "environment_description": "环境详细描述（光线、氛围、布景）",
                "props_needed": ["所需道具列表"],
                "lighting_setup": "灯光设置",
                "camera_position": "摄像机位置",
                "background_elements": "背景元素描述",
                "color_palette": "色彩搭配",
                "mood_atmosphere": "情绪氛围",
                "technical_notes": "技术要求"
              }}
            ]

            重要要求：
            1. 场景设计必须与分镜脚本完全对应
            2. 考虑角色的外貌和服装搭配
            3. 确保场景的连贯性和一致性
            4. 适合预算和制作规模
            """

            scene_response = await self.llm.ainvoke([HumanMessage(content=scene_prompt)])

            # 解析场景规划JSON
            scene_plans = self._parse_scene_plans(scene_response.content)

            # 第二步：为每个分镜生成场景图片
            scene_images = []
            if scene_plans.get("parsed", False) and shots_info:
                scenes = scene_plans.get("scenes", [])

                for i, scene in enumerate(scenes):
                    try:
                        # 找到对应的分镜信息
                        corresponding_shot = None
                        for shot in shots_info:
                            if shot.get("shot_id") == scene.get("shot_id"):
                                corresponding_shot = shot
                                break

                        if corresponding_shot:
                            # 生成分镜图片提示词
                            image_prompt = self._create_scene_image_prompt(
                                scene, corresponding_shot, characters_info, requirements
                            )

                            # 调用图像生成服务
                            image_result = await self._generate_scene_image(
                                image_prompt, scene.get("shot_id", f"scene_{i+1}")
                            )

                            if image_result:
                                scene_images.append({
                                    "shot_id": scene.get("shot_id"),
                                    "scene_location": scene.get("scene_location"),
                                    "image_url": image_result["image_url"],
                                    "prompt": image_prompt,
                                    "metadata": image_result.get("metadata", {})
                                })

                            # 避免API调用过于频繁
                            if i < len(scenes) - 1:
                                await asyncio.sleep(1)

                    except Exception as e:
                        logger.warning(f"Failed to generate scene image for {scene.get('shot_id', 'Unknown')}: {e}")
                        continue

            # 保存结果到状态
            state["scene_plan"] = {
                "scene_descriptions": scene_plans,
                "scene_images": scene_images,
                "title": "场景规划与分镜图片",
                "type": "scene_with_images"
            }

            # 为兼容性保留原有字段
            state["design_content"] = scene_response.content
            state["production_phase"] = "scene"
            state["current_stage"] = DramaStage.SCENE_PLANNING
            state["current_state"] = WorkflowState.COMPLETED

            # 如果有生成的图片，设置第一张作为主图片
            if scene_images:
                state["image_url"] = scene_images[0]["image_url"]
            else:
                state["image_url"] = None

            logger.info(f"Scene planning completed with {len(scene_images)} scene images")
            return state

        except Exception as e:
            state["error_message"] = f"场景规划失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            logger.error(f"Scene planning node error: {e}")
            return state
    
    async def _video_production_node(self, state: DramaProductionState) -> DramaProductionState:
        """视频制作节点 - 扩展版：基于分镜图片生成视频片段"""
        try:
            requirements = state["requirements"]
            user_input = state["user_input"]
            script_concept = state.get("script_concept", {})
            character_design = state.get("character_design", {})
            scene_plan = state.get("scene_plan", {})

            # 获取前置节点的输出
            storyboard_data = script_concept.get("storyboard", {})
            scene_images = scene_plan.get("scene_images", [])
            scene_descriptions = scene_plan.get("scene_descriptions", {})

            # 提取分镜脚本信息
            shots_info = []
            if isinstance(storyboard_data, dict) and storyboard_data.get("parsed", False):
                shots_info = storyboard_data.get("shots", [])

            # 第一步：生成视频制作计划
            video_plan_prompt = f"""
            你是一个专业的视频制作人，请基于以下分镜脚本和场景信息制定详细的视频制作方案：

            项目背景：{user_input}
            预算等级：{requirements.budget if requirements else '中等'}
            制作规模：{requirements.production_scale if requirements else '短片'}
            时长：{requirements.duration if requirements else 5}分钟
            视觉风格：{requirements.visual_style if requirements else '写实'}

            分镜脚本信息：
            {json.dumps(shots_info, ensure_ascii=False, indent=2) if shots_info else '无分镜脚本'}

            场景图片数量：{len(scene_images)}

            请为每个镜头提供详细的视频制作方案，输出JSON格式：
            [
              {{
                "shot_id": "镜头编号（与分镜脚本对应）",
                "video_duration": 镜头时长（秒）,
                "motion_type": "运动类型（静态/缓慢移动/快速移动）",
                "motion_description": "运动描述（摄像机运动、角色动作等）",
                "transition_in": "入场转场效果",
                "transition_out": "出场转场效果",
                "audio_requirements": "音频要求（对话、音效、背景音）",
                "technical_notes": "技术要求",
                "quality_settings": "质量设置"
              }}
            ]

            重要要求：
            1. 视频制作方案必须与分镜脚本完全对应
            2. 考虑图生视频的技术特点和限制
            3. 确保视频片段的连贯性
            4. 适合预算和制作规模
            """

            video_plan_response = await self.llm.ainvoke([HumanMessage(content=video_plan_prompt)])

            # 解析视频制作计划
            video_plans = self._parse_video_plans(video_plan_response.content)

            # 第二步：基于场景图片生成视频片段
            video_segments = []
            if video_plans.get("parsed", False) and scene_images:
                plans = video_plans.get("plans", [])

                for i, plan in enumerate(plans):
                    try:
                        # 找到对应的场景图片
                        corresponding_image = None
                        for image in scene_images:
                            if image.get("shot_id") == plan.get("shot_id"):
                                corresponding_image = image
                                break

                        if corresponding_image:
                            # 生成视频片段
                            video_result = await self._generate_video_from_image(
                                image_url=corresponding_image["image_url"],
                                duration=plan.get("video_duration", 5),
                                motion_description=plan.get("motion_description", ""),
                                shot_id=plan.get("shot_id")
                            )

                            if video_result:
                                video_segments.append({
                                    "shot_id": plan.get("shot_id"),
                                    "video_url": video_result["video_url"],
                                    "duration": plan.get("video_duration", 5),
                                    "motion_type": plan.get("motion_type", "静态"),
                                    "source_image": corresponding_image["image_url"],
                                    "metadata": video_result.get("metadata", {})
                                })

                            # 避免API调用过于频繁
                            if i < len(plans) - 1:
                                await asyncio.sleep(2)

                    except Exception as e:
                        logger.warning(f"Failed to generate video for {plan.get('shot_id', 'Unknown')}: {e}")
                        continue

            # 保存结果到状态
            state["video_plan"] = {
                "video_production_plans": video_plans,
                "video_segments": video_segments,
                "title": "视频制作与片段生成",
                "type": "video_with_segments"
            }

            # 为兼容性保留原有字段
            state["design_content"] = video_plan_response.content
            state["production_phase"] = "video"
            state["current_stage"] = DramaStage.VIDEO_PRODUCTION
            state["current_state"] = WorkflowState.COMPLETED

            # 如果有生成的视频，设置第一个作为主视频
            if video_segments:
                state["video_url"] = video_segments[0]["video_url"]
            else:
                state["video_url"] = None

            # 保持image_url为场景图片
            state["image_url"] = scene_images[0]["image_url"] if scene_images else None

            logger.info(f"Video production completed with {len(video_segments)} video segments")
            return state

        except Exception as e:
            state["error_message"] = f"视频制作失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            logger.error(f"Video production node error: {e}")
            return state

    async def _post_production_node(self, state: DramaProductionState) -> DramaProductionState:
        """后期制作节点 - 视频拼接、配音、音效、调色等"""
        try:
            requirements = state["requirements"]
            user_input = state["user_input"]
            script_concept = state.get("script_concept", {})
            character_design = state.get("character_design", {})
            scene_plan = state.get("scene_plan", {})
            video_plan = state.get("video_plan", {})

            # 获取前置节点的输出
            script_content = script_concept.get("script", "")
            video_segments = video_plan.get("video_segments", [])
            storyboard_data = script_concept.get("storyboard", {})

            # 计算总时长
            total_duration = sum(seg.get("duration", 5) for seg in video_segments)

            # 第一步：生成后期制作计划
            post_production_prompt = f"""
            你是一个专业的后期制作师，请基于以下视频片段和剧本内容制定详细的后期制作方案：

            项目背景：{user_input}
            剧本内容：{script_content[:500]}...
            视频片段数量：{len(video_segments)}
            总时长：{total_duration}秒
            视觉风格：{requirements.visual_style if requirements else '写实'}

            请提供详细的后期制作方案，输出JSON格式：
            {{
              "video_composition": {{
                "sequence_order": ["Shot_01", "Shot_02", "Shot_03"],
                "transitions": ["淡入淡出", "切换", "淡入淡出"],
                "color_grading": "整体色调调整方案",
                "visual_effects": ["稳定", "调色", "降噪"]
              }},
              "audio_production": {{
                "voiceover_plan": "配音制作计划",
                "background_music": "背景音乐风格和时长",
                "sound_effects": ["环境音", "动作音效"],
                "audio_mixing": "音频混合方案"
              }},
              "final_output": {{
                "resolution": "1920x1080",
                "frame_rate": "24fps",
                "format": "MP4",
                "quality": "高质量"
              }}
            }}

            重要要求：
            1. 确保视频片段的顺序与分镜脚本一致
            2. 配音要符合剧本对话内容
            3. 背景音乐要符合剧情氛围
            4. 整体风格要统一协调
            """

            post_plan_response = await self.llm.ainvoke([HumanMessage(content=post_production_prompt)])

            # 解析后期制作计划
            post_plans = self._parse_post_production_plans(post_plan_response.content)

            # 第二步：执行后期制作流程
            post_production_results = {}

            if post_plans.get("parsed", False):
                plans = post_plans.get("plans", {})

                # 视频拼接
                if video_segments:
                    composition_result = await self._compose_video_segments(
                        video_segments, plans.get("video_composition", {})
                    )
                    post_production_results["video_composition"] = composition_result

                # 配音生成
                if script_content:
                    voiceover_result = await self._generate_voiceover(
                        script_content, plans.get("audio_production", {})
                    )
                    post_production_results["voiceover"] = voiceover_result

                # 背景音乐生成
                music_result = await self._generate_background_music(
                    total_duration, plans.get("audio_production", {}), requirements
                )
                post_production_results["background_music"] = music_result

                # 音频混合
                if post_production_results.get("voiceover") and post_production_results.get("background_music"):
                    audio_mix_result = await self._mix_audio_tracks(
                        post_production_results["voiceover"],
                        post_production_results["background_music"]
                    )
                    post_production_results["audio_mix"] = audio_mix_result

                # 最终合成
                if post_production_results.get("video_composition") and post_production_results.get("audio_mix"):
                    final_result = await self._merge_video_audio(
                        post_production_results["video_composition"],
                        post_production_results["audio_mix"]
                    )
                    post_production_results["final_drama"] = final_result

            # 保存结果到状态
            state["post_production"] = {
                "post_production_plans": post_plans,
                "production_results": post_production_results,
                "title": "后期制作与最终合成",
                "type": "post_production_complete"
            }

            # 为兼容性保留原有字段
            state["design_content"] = post_plan_response.content
            state["production_phase"] = "post_production"
            state["current_stage"] = DramaStage.VIDEO_PRODUCTION  # 使用现有的枚举值
            state["current_state"] = WorkflowState.COMPLETED

            # 设置最终视频URL
            if post_production_results.get("final_drama"):
                state["final_video_url"] = post_production_results["final_drama"]["video_url"]
            elif post_production_results.get("video_composition"):
                state["final_video_url"] = post_production_results["video_composition"]["video_url"]
            else:
                state["final_video_url"] = None

            logger.info("Post production completed")
            return state

        except Exception as e:
            state["error_message"] = f"后期制作失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            logger.error(f"Post production node error: {e}")
            return state

    async def _comprehensive_plan_node(self, state: DramaProductionState) -> DramaProductionState:
        """综合方案节点"""
        try:
            requirements = state["requirements"]
            user_input = state["user_input"]
            
            comprehensive_prompt = f"""
            你是一个专业的剧情制作人，请为以下项目提供综合制作方案：
            
            项目需求：{user_input}
            剧情类型：{requirements.genre if requirements else '剧情'}
            制作规模：{requirements.production_scale if requirements else '短片'}
            预算等级：{requirements.budget if requirements else '中等'}
            时长：{requirements.duration if requirements else 5}分钟
            
            请提供完整的制作方案，包括：
            
            1. **剧本创作**
               - 故事梗概
               - 角色设定
               - 情节结构
            
            2. **角色设计**
               - 主要角色介绍
               - 视觉形象设计
               - 角色关系
            
            3. **场景规划**
               - 关键场景设计
               - 拍摄场地需求
               - 道具准备
            
            4. **制作方案**
               - 制作流程
               - 团队组建
               - 预算规划
               - 时间安排
            
            请用中文回复，方案要全面且具有可操作性。
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=comprehensive_prompt)])
            
            state["comprehensive_plan"] = {
                "content": response.content,
                "title": "综合制作方案",
                "type": "comprehensive"
            }
            state["design_content"] = response.content
            state["production_phase"] = "comprehensive"
            state["current_stage"] = DramaStage.COMPREHENSIVE_PLAN
            state["current_state"] = WorkflowState.COMPLETED
            state["image_url"] = None
            
            logger.info("Comprehensive planning completed")
            return state
            
        except Exception as e:
            state["error_message"] = f"综合方案失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            return state
    
    async def _quality_review_node(self, state: DramaProductionState) -> DramaProductionState:
        """质量检查节点"""
        try:
            design_content = state.get("design_content", "")
            production_phase = state.get("production_phase", "")
            
            quality_prompt = f"""
            你是一个专业的剧情制作质量评估师，请对以下{production_phase}内容进行质量评估：
            
            制作内容：{design_content}
            
            评估维度：
            1. 内容完整性
            2. 专业程度
            3. 创意水平
            4. 实用性
            5. 整体评分（1-10分）
            
            请提供具体的评估结果和改进建议。
            请返回纯JSON格式。
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=quality_prompt)])
            
            try:
                quality_feedback = json.loads(response.content)
                state["quality_feedback"] = quality_feedback
                state["current_stage"] = DramaStage.QUALITY_REVIEW
                state["current_state"] = WorkflowState.COMPLETED
                
                logger.info("Quality review completed")
            except json.JSONDecodeError:
                # 如果JSON解析失败，直接存储原始响应
                state["quality_feedback"] = {
                    "评估结果": response.content,
                    "整体评分": 8
                }
                state["current_stage"] = DramaStage.QUALITY_REVIEW
                state["current_state"] = WorkflowState.COMPLETED
                
            return state
            
        except Exception as e:
            state["error_message"] = f"质量检查失败：{str(e)}"
            state["current_state"] = WorkflowState.ERROR
            return state
    
    async def _error_handler_node(self, state: DramaProductionState) -> DramaProductionState:
        """错误处理节点"""
        error_message = state.get("error_message", "未知错误")
        logger.error(f"Drama production workflow error: {error_message}")
        
        state["current_state"] = WorkflowState.ERROR
        state["current_stage"] = DramaStage.COMPLETED
        
        return state
    
    # ============================================================================
    # 任务处理方法
    # ============================================================================
    
    async def process_conversation(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        重写process_conversation方法以适配DramaProductionState
        """
        try:
            # 创建初始状态
            initial_state = DramaProductionState(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                task_type=kwargs.get("task_type", "comprehensive"),
                current_stage=DramaStage.REQUIREMENT_ANALYSIS,
                current_state=WorkflowState.PENDING,
                messages=[],
                metadata=kwargs
            )
            
            # 创建线程配置
            thread_config = {
                "configurable": {
                    "thread_id": conversation_id
                }
            }
            
            # 通过工作流处理
            result = await self.workflow.ainvoke(
                initial_state,
                config=thread_config
            )
            
            # 格式化响应
            return self._format_drama_response(result)
            
        except Exception as e:
            logger.error(f"Drama workflow processing failed: {e}")
            return {
                "error": str(e),
                "conversation_id": conversation_id,
                "current_state": "error"
            }
    
    def _format_drama_response(self, state: DramaProductionState) -> Dict[str, Any]:
        """格式化剧情制作工作流的响应"""
        return {
            "conversation_id": state.get("conversation_id"),
            "current_state": state.get("current_state").value if hasattr(state.get("current_state"), 'value') else state.get("current_state"),
            "current_stage": state.get("current_stage").value if hasattr(state.get("current_stage"), 'value') else state.get("current_stage"),
            "design_content": state.get("design_content", ""),
            "production_phase": state.get("production_phase", ""),
            "image_url": state.get("image_url"),
            "error_message": state.get("error_message"),
            "quality_feedback": state.get("quality_feedback", {}),
            "requirements": state.get("requirements").__dict__ if hasattr(state.get("requirements"), '__dict__') else state.get("requirements"),
            "script_concept": state.get("script_concept", {}),
            "character_design": state.get("character_design", {}),
            "scene_plan": state.get("scene_plan", {}),
            "video_plan": state.get("video_plan", {}),
            "comprehensive_plan": state.get("comprehensive_plan", {}),
            "metadata": state.get("metadata", {})
        }
    
    def get_workflow_info(self) -> Dict[str, Any]:
        """获取剧情制作工作流信息"""
        return {
            "name": "DramaWorkflow",
            "type": "langgraph_drama_production",
            "description": "基于LangGraph的专业剧情制作工作流，提供结构化的剧本创作、角色设计、场景规划和视频制作服务",
            "capabilities": [
                "需求分析",
                "剧本构思",
                "角色设计", 
                "场景规划",
                "视频制作",
                "综合制作方案",
                "质量检查",
                "智能路由"
            ],
            "production_phases": [stage.value for stage in DramaStage],
            "langgraph_nodes": [
                "requirement_analysis",
                "script_concept", 
                "character_design",
                "scene_planning",
                "video_production",
                "comprehensive_plan",
                "quality_review",
                "error_handler"
            ],
            "supported_genres": [
                "喜剧", "悲剧", "动作", "爱情", "科幻", "悬疑", "恐怖", "文艺"
            ],
            "features": [
                "LangGraph工作流引擎",
                "智能任务路由",
                "结构化创作流程",
                "质量控制和反馈",
                "错误处理机制",
                "状态管理"
            ],
            "version": "2.0.0"
        }