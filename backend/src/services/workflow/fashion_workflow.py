"""
Fashion-specific workflow implementation.

This workflow is specialized for fashion design conversations,
extending the base DesignWorkflow with fashion-specific logic.
"""

import logging
from typing import Dict, Any, Optional

from .design_workflow import DesignWorkflow
from .base_workflow import ConversationState, DesignRequirements

logger = logging.getLogger(__name__)


class FashionWorkflow(DesignWorkflow):
    """
    Fashion-specific workflow implementation.
    
    Extends DesignWorkflow with fashion-specific:
    - Requirement extraction logic
    - Design generation prompts
    - Modification handling
    """
    
    def __init__(self, design_agent=None):
        """Initialize fashion workflow with optional design agent."""
        super().__init__(design_agent=design_agent)
        logger.info("FashionWorkflow initialized")
    
    async def _extract_requirements(self, user_input: str) -> DesignRequirements:
        """从用户输入中提取服装设计需求"""
        
        extraction_prompt = f"""
        Extract fashion design requirements from the user's input and return ONLY a valid JSON object.

        User input: "{user_input}"

        Extract the following fashion-specific information:
        - category: 服装类型 (如：连衣裙、衬衫、裤子、外套、裙子等)
        - style: 风格 (如：现代、复古、简约、优雅、休闲、正式等)
        - colors: 颜色列表 (如：["蓝色", "白色", "黑色"])
        - materials: 材质列表 (如：["棉质", "丝绸", "牛仔", "针织"])
        - occasion: 穿着场合 (如：日常、工作、约会、派对、运动等)
        - target_gender: 目标性别 (如：女性、男性、通用)
        - key_features: 服装特征 (如：["宽松", "短袖", "高腰", "A字型"])
        - mood: 设计氛围 (如：优雅、活泼、专业、浪漫、酷炫等)

        IMPORTANT: Return ONLY a valid JSON object, no explanations or markdown formatting.
        Use "未指定" for unmentioned fields.

        Example format:
        {{
            "category": "连衣裙",
            "style": "现代",
            "colors": ["蓝色"],
            "materials": ["棉质"],
            "occasion": "日常",
            "target_gender": "女性",
            "key_features": ["舒适"],
            "mood": "清新"
        }}
        """
        
        try:
            from langchain_core.messages import HumanMessage
            import json
            import re

            response = await self.llm.ainvoke([HumanMessage(content=extraction_prompt)])
            response_content = response.content.strip()

            logger.info(f"LLM response for requirements extraction: {response_content}")

            # 尝试提取JSON内容（处理可能的markdown格式）
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response_content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
            else:
                # 如果没有markdown格式，尝试直接解析
                json_content = response_content

            try:
                requirements_data = json.loads(json_content)
            except json.JSONDecodeError as json_error:
                logger.warning(f"JSON parsing failed: {json_error}, trying to fix common issues")
                # 尝试修复常见的JSON问题
                fixed_json = json_content.replace("'", '"').replace('，', ',').replace('：', ':')
                requirements_data = json.loads(fixed_json)

            return DesignRequirements(
                category=requirements_data.get("category", "服装"),
                style=requirements_data.get("style", "现代"),
                colors=requirements_data.get("colors", ["中性色"]),
                materials=requirements_data.get("materials", []),
                occasion=requirements_data.get("occasion", "日常"),
                target_gender=requirements_data.get("target_gender", "通用"),
                key_features=requirements_data.get("key_features", []),
                mood=requirements_data.get("mood", "时尚"),
                additional_notes=user_input
            )

        except Exception as e:
            logger.error(f"Fashion requirements extraction failed: {e}")
            logger.error(f"Raw LLM response: {response.content if 'response' in locals() else 'No response'}")
            # 返回基础服装需求
            return DesignRequirements(
                category="服装",
                style="现代",
                colors=["中性色"],
                materials=["棉质"],
                occasion="日常",
                target_gender="通用",
                key_features=["舒适"],
                mood="时尚",
                additional_notes=user_input
            )
    
    def _generate_confirmation_message(self, requirements: DesignRequirements) -> str:
        """生成服装设计需求确认消息"""
        
        colors_str = "、".join(requirements.colors) if requirements.colors else "未指定"
        materials_str = "、".join(requirements.materials) if requirements.materials else "未指定"
        features_str = "、".join(requirements.key_features) if requirements.key_features else "未指定"
        
        return f"""
👗 **服装设计需求确认**

• **服装类型**: {requirements.category}
• **设计风格**: {requirements.style}
• **主要颜色**: {colors_str}
• **面料材质**: {materials_str}
• **穿着场合**: {requirements.occasion}
• **目标人群**: {requirements.target_gender}
• **设计特征**: {features_str}
• **整体氛围**: {requirements.mood}

请确认以上服装设计需求是否正确？如需修改请直接说明，确认无误请回复"确认"开始生成设计。
        """.strip()
    
    async def _determine_modification_type(self, user_input: str) -> str:
        """智能判断服装设计修改类型"""
        
        modification_prompt = f"""
        Determine whether the fashion design modification should use "image_edit" or "design_regeneration".

        Fashion-specific guidelines:
        - image_edit: Color adjustments, brightness, contrast, minor visual tweaks
        - design_regeneration: Style changes, garment type changes, structural modifications

        Examples:
        - "让颜色更鲜艳" → image_edit
        - "改成短袖" → design_regeneration  
        - "换成连衣裙" → design_regeneration
        - "调整亮度" → image_edit
        - "改成正式风格" → design_regeneration

        User request: "{user_input}"

        Respond with ONLY: image_edit OR design_regeneration
        """

        try:
            from langchain_core.messages import HumanMessage
            
            response = await self.llm.ainvoke([HumanMessage(content=modification_prompt)])
            modification_type = response.content.strip().lower()

            if modification_type not in ["image_edit", "design_regeneration"]:
                logger.warning(f"Invalid modification type: {modification_type}, defaulting to design_regeneration")
                modification_type = "design_regeneration"

            logger.info(f"Fashion modification type determined: {modification_type}")
            return modification_type

        except Exception as e:
            logger.error(f"Error determining fashion modification type: {e}")
            return "design_regeneration"


# 保持向后兼容性的别名
class FashionDesignWorkflow(FashionWorkflow):
    """向后兼容的服装设计工作流"""
    
    def __init__(self):
        super().__init__(design_agent=None)
        logger.info("FashionDesignWorkflow initialized (compatibility mode)")
