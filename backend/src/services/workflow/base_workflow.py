"""
Base workflow classes and shared types for LangGraph workflows.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, TypedDict
from enum import Enum
from dataclasses import dataclass
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph
import logging

from ...core.config import settings

logger = logging.getLogger(__name__)


class IntentType(str, Enum):
    """用户意图类型枚举"""
    CASUAL_CHAT = "casual_chat"
    DESIGN_GENERATION = "design_generation"
    DESIGN_MODIFICATION = "design_modification"


class WorkflowState(str, Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    WAITING_CONFIRMATION = "waiting_confirmation"
    GENERATING = "generating"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class DesignRequirements:
    """设计需求数据类"""
    category: str = ""
    style: str = ""
    colors: List[str] = None
    materials: List[str] = None
    occasion: str = ""
    target_gender: str = ""
    key_features: List[str] = None
    mood: str = ""
    additional_notes: str = ""
    
    def __post_init__(self):
        if self.colors is None:
            self.colors = []
        if self.materials is None:
            self.materials = []
        if self.key_features is None:
            self.key_features = []


class ConversationState(TypedDict, total=False):
    """对话状态类型定义"""
    # 基础信息
    user_input: str
    conversation_id: str
    user_id: str
    
    # 意图和状态
    intent: IntentType
    current_state: WorkflowState
    
    # 设计相关
    requirements: Optional[DesignRequirements]
    image_url: Optional[str]
    design_prompt: Optional[str]
    
    # 对话历史
    messages: List[Dict[str, Any]]
    
    # 错误处理
    error_message: Optional[str]
    retry_count: int
    
    # 元数据
    metadata: Dict[str, Any]


class BaseWorkflow(ABC):
    """
    Abstract base class for all LangGraph workflows.
    
    Provides common functionality and interface for workflow implementations.
    """
    
    def __init__(self, design_agent=None):
        """Initialize the base workflow."""
        # Initialize LLM
        self.llm = ChatOpenAI(
            base_url=settings.qwen_base_url,
            api_key=settings.qwen_api_key,
            model=settings.qwen_model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens,
        )
        
        # Initialize memory saver for state persistence
        if not hasattr(BaseWorkflow, '_shared_memory'):
            BaseWorkflow._shared_memory = MemorySaver()
        self.memory = BaseWorkflow._shared_memory
        
        # Inject specific design agent
        self.design_agent = design_agent
        
        # Build the workflow graph
        self.workflow = self._build_workflow()
        
        logger.info(f"{self.__class__.__name__} initialized with agent: {design_agent.__class__.__name__ if design_agent else 'None'}")
    
    @abstractmethod
    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow. Must be implemented by subclasses."""
        pass
    
    async def process_conversation(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a conversation using the workflow.
        
        Args:
            user_input: User's input message
            conversation_id: Conversation identifier
            user_id: User identifier
            **kwargs: Additional parameters
            
        Returns:
            Dict containing the conversation result
        """
        try:
            # Create initial state
            initial_state = ConversationState(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                messages=[],
                retry_count=0,
                metadata=kwargs
            )
            
            # Create thread configuration
            thread_config = {
                "configurable": {
                    "thread_id": conversation_id
                }
            }
            
            # Process through workflow
            result = await self.workflow.ainvoke(
                initial_state,
                config=thread_config
            )
            
            return self._format_response(result)
            
        except Exception as e:
            logger.error(f"Workflow processing failed: {e}")
            return {
                "error": str(e),
                "conversation_id": conversation_id,
                "current_state": "error"
            }
    
    def _format_response(self, state: ConversationState) -> Dict[str, Any]:
        """Format the workflow result for API response."""
        # 序列化消息中的AIMessage对象
        messages = state.get("messages", [])
        serialized_messages = self._serialize_messages(messages)

        # 序列化枚举类型
        current_state = state.get("current_state")
        if hasattr(current_state, 'value'):
            current_state = current_state.value

        # 序列化需求对象
        requirements = state.get("requirements")
        if requirements and hasattr(requirements, '__dict__'):
            requirements = dict(requirements.__dict__)

        # 序列化意图类型
        intent = state.get("intent")
        if hasattr(intent, 'value'):
            intent = intent.value

        return {
            "messages": serialized_messages,
            "conversation_id": state.get("conversation_id"),
            "current_state": current_state,
            "intent": intent,
            "image_url": state.get("image_url"),
            "design_prompt": state.get("design_prompt"),
            "requirements": requirements,
            "error_message": state.get("error_message"),
            "metadata": state.get("metadata", {})
        }

    def _serialize_messages(self, messages: List) -> List[Dict[str, Any]]:
        """将消息列表中的AIMessage对象转换为可序列化的字典格式"""
        from langchain_core.messages import AIMessage, HumanMessage

        serialized_messages = []
        for msg in messages:
            if isinstance(msg, AIMessage):
                serialized_messages.append({
                    "role": "assistant",
                    "content": msg.content
                })
            elif isinstance(msg, HumanMessage):
                serialized_messages.append({
                    "role": "user",
                    "content": msg.content
                })
            elif isinstance(msg, dict):
                # 已经是字典格式，直接添加
                serialized_messages.append(msg)
            else:
                # 其他类型，尝试转换为字符串
                serialized_messages.append({
                    "role": "assistant",
                    "content": str(msg)
                })
        return serialized_messages
