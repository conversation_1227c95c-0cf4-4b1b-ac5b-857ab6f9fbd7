#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logo Design Workflow

专业Logo设计工作流，基于DesignWorkflow扩展
提供Logo设计专用的需求分析、确认和生成功能
"""

import json
import logging
from typing import Dict, Any, Optional, List
from langchain_core.messages import HumanMessage, AIMessage

from .design_workflow import DesignWorkflow
from .base_workflow import ConversationState, DesignRequirements

logger = logging.getLogger(__name__)


class LogoWorkflow(DesignWorkflow):
    """Logo设计专用工作流"""

    def __init__(self):
        super().__init__()
        logger.info("LogoWorkflow initialized")

    async def _extract_requirements(self, user_input: str) -> DesignRequirements:
        """专业Logo设计师的需求分析和细化"""
        
        extraction_prompt = f"""
你是一位国际知名的Logo设计大师，拥有25年顶级品牌设计经验，曾为可口可乐、苹果、耐克等世界500强企业设计Logo。你深谙品牌视觉识别的精髓，擅长将品牌理念转化为永恒的视觉符号。

## 用户原始需求
"{user_input}"

## 专业分析任务
请基于你的专业经验，对用户需求进行深度分析和细化：

### 第一步：品牌理念挖掘
- 用户真正想要表达的品牌核心价值是什么？
- 可能的行业特征和目标市场是什么？
- 从用户的表达中能推断出什么品牌个性？

### 第二步：专业需求细化
基于分析，请细化以下Logo设计要素：

**Logo类型分析**：
- category: 具体Logo类型（企业Logo、产品Logo、个人品牌Logo、活动Logo、组织Logo等）

**设计风格建议**：
- style: 推荐的设计风格（现代简约、经典传统、创意艺术、科技未来、手工匠心、奢华精致等）

**色彩策略建议**：
- colors: 建议的主色调搭配（考虑行业特点、品牌定位、心理效应）

**设计元素规划**：
- materials: 适合的设计元素（文字标识、图形符号、抽象图案、具象图形、几何元素等）

**应用场景定位**：
- occasion: 主要应用场景（商业标识、数字媒体、印刷物料、产品包装、建筑标识等）

**受众群体分析**：
- target_gender: 目标受众特征（企业客户、消费者群体、专业人士、年轻群体等）

**设计特征定义**：
- key_features: 核心设计特征（简洁易记、独特识别、可扩展性、文化适应、时代感等）

**品牌氛围营造**：
- mood: 期望营造的品牌氛围（专业权威、创新活力、温暖亲和、高端奢华、可靠稳重等）

## 输出要求
请以JSON格式输出分析结果，对于用户未明确提及的要素，请基于你的专业判断给出合理建议，避免使用"未指定"。

## 专业建议
在JSON后，请用2-3句话总结你的专业建议，说明为什么这样设计Logo。
"""

        try:
            response = await self.llm.ainvoke([HumanMessage(content=extraction_prompt)])
            response_content = response.content.strip()
            
            # 尝试提取JSON部分（可能包含专业建议）
            json_start = response_content.find('{')
            json_end = response_content.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_content = response_content[json_start:json_end]
                requirements_data = json.loads(json_content)
                
                # 提取专业建议（JSON后的内容）
                professional_advice = ""
                if json_end < len(response_content):
                    professional_advice = response_content[json_end:].strip()
                
                # 将专业建议添加到additional_notes
                full_notes = f"用户需求: {user_input}"
                if professional_advice:
                    full_notes += f"\n\n专业建议: {professional_advice}"
                
                logger.info(f"Extracted logo requirements with professional advice: {professional_advice}")
                
                # 确保数组字段的数据类型正确
                def ensure_array(value, default):
                    if isinstance(value, list):
                        return value
                    elif isinstance(value, str):
                        # 如果是字符串，尝试按逗号或顿号分割
                        return [item.strip() for item in value.replace('、', ',').split(',') if item.strip()]
                    else:
                        return default

                return DesignRequirements(
                    category=requirements_data.get("category", "企业Logo"),
                    style=requirements_data.get("style", "现代简约"),
                    colors=ensure_array(requirements_data.get("colors"), ["蓝色", "白色"]),
                    materials=ensure_array(requirements_data.get("materials"), ["文字标识", "几何图形"]),
                    occasion=requirements_data.get("occasion", "商业标识"),
                    target_gender=requirements_data.get("target_gender", "企业客户"),
                    key_features=ensure_array(requirements_data.get("key_features"), ["简洁易记", "独特识别"]),
                    mood=requirements_data.get("mood", "专业权威"),
                    additional_notes=full_notes
                )
            else:
                raise ValueError("无法从响应中提取有效的JSON数据")
            
        except Exception as e:
            logger.error(f"Logo requirements extraction failed: {e}")
            logger.error(f"LLM response: {response.content if 'response' in locals() else 'No response'}")
            
            # 如果专业分析失败，重新抛出异常让上层处理
            raise Exception(f"专业Logo需求分析失败: {str(e)}")

    def _generate_confirmation_message(self, requirements: DesignRequirements) -> str:
        """生成专业的Logo设计需求确认消息"""
        
        # 安全的数组转字符串处理
        def safe_join(value, default="未指定"):
            if isinstance(value, list) and value:
                return "、".join(str(item) for item in value)
            elif isinstance(value, str) and value:
                return value
            else:
                return default

        colors_str = safe_join(requirements.colors)
        materials_str = safe_join(requirements.materials)
        features_str = safe_join(requirements.key_features)
        
        # 检查是否有专业建议
        has_professional_advice = "专业建议:" in requirements.additional_notes
        
        confirmation_msg = f"""
🎨 **专业Logo设计方案**

基于您的品牌需求，我为您制定了以下Logo设计方案：

📋 **设计规划**
• **Logo类型**: {requirements.category}
• **设计风格**: {requirements.style}
• **色彩策略**: {colors_str}
• **设计元素**: {materials_str}
• **应用场景**: {requirements.occasion}
• **目标受众**: {requirements.target_gender}
• **核心特征**: {features_str}
• **品牌氛围**: {requirements.mood}
"""

        # 如果有专业建议，添加到确认消息中
        if has_professional_advice:
            advice_start = requirements.additional_notes.find("专业建议:")
            if advice_start != -1:
                professional_advice = requirements.additional_notes[advice_start:].strip()
                confirmation_msg += f"""

💡 **{professional_advice}**
"""

        confirmation_msg += """

✅ **下一步操作**
如果您对以上Logo设计方案满意，请回复"确认"开始生成Logo。
如需调整任何设计要素，请直接告诉我您的想法。
        """
        
        return confirmation_msg.strip()

    async def _intent_recognition_node(self, state):
        """
        Logo设计专用的意图识别节点
        重写父类方法，使用Logo设计专用的提示词
        """
        try:
            user_input = state["user_input"]
            current_requirements = state.get("requirements")
            current_state = state.get("current_state")
            design_history = state.get("design_history", [])
            messages = state.get("messages", [])

            # 检查是否有编辑图片URL
            edit_image_url = state.get("edit_image_url")
            has_selected_image = bool(edit_image_url)
            logger.info(f"Has selected image for editing: {has_selected_image}, URL: {edit_image_url}")

            # 构建上下文信息
            context_info = []
            if current_requirements:
                context_info.append(f"Current logo requirements: {current_requirements}")
            if current_state:
                context_info.append(f"Current workflow state: {current_state}")
            if design_history:
                context_info.append(f"Design history: {len(design_history)} previous designs")
            if has_selected_image:
                context_info.append(f"Selected image for editing: {edit_image_url}")
            if messages:
                recent_messages = messages[-3:] if len(messages) > 3 else messages
                context_info.append(f"Recent conversation: {[msg.get('content', '') for msg in recent_messages]}")

            intent_prompt = f"""
            You are an expert intent classifier for a logo design AI system.
            Analyze the user's message with full conversation context to determine their intent.

            Intent types (直接对应工作流节点):
            - casual_chat: General conversation, greetings, questions not related to logo design
            - design_generation: User wants to create a NEW logo design (直接进入生成，无需确认)
            - design_modification: User wants to modify an EXISTING logo design (包括设计修改和图片编辑)

            Context information:
            {chr(10).join(context_info) if context_info else "No previous context"}

            User message: "{user_input}"

            Important classification rules:
            1. If user provides logo design requirements or wants to create something → design_generation
            2. If user wants to modify existing logo design or selected image → design_modification
            3. If user is just chatting or asking general questions → casual_chat

            Respond with ONLY the intent type: casual_chat, design_generation, or design_modification
            """

            response = await self.llm.ainvoke([HumanMessage(content=intent_prompt)])
            intent = response.content.strip().lower()

            # Validate intent
            from .base_workflow import IntentType
            valid_intents = [e.value for e in IntentType]
            if intent not in valid_intents:
                logger.warning(f"Invalid intent detected: {intent}, defaulting to casual_chat")
                intent = IntentType.CASUAL_CHAT

            state["intent"] = IntentType(intent)
            from .base_workflow import WorkflowState
            state["current_state"] = WorkflowState.PROCESSING

            logger.info(f"Logo intent recognized: {intent} for input: '{user_input[:50]}...'")
            return state

        except Exception as e:
            logger.error(f"Logo intent recognition failed: {e}")
            from .base_workflow import IntentType
            state["intent"] = IntentType.CASUAL_CHAT
            state["error_message"] = f"Intent recognition error: {str(e)}"
            return state

    async def _design_generation_node(self, state):
        """
        Logo设计专用的设计生成节点
        重写父类方法，使用Logo设计专用的生成逻辑
        """
        try:
            conversation_id = state.get("conversation_id")
            user_input = state.get("user_input", "")
            requirements = state.get("requirements")

            logger.info(f"Starting logo design generation for conversation: {conversation_id}")

            # 如果没有需求，先提取需求
            if not requirements:
                logger.info("No requirements found, extracting from user input")
                requirements = await self._extract_requirements(user_input)
                state["requirements"] = requirements
                logger.info(f"Extracted requirements: {requirements.__dict__ if hasattr(requirements, '__dict__') else requirements}")

            # 使用Logo设计专用的生成逻辑
            logger.info("Using logo design generation")

            # 构建Logo设计提示词
            logo_prompt = await self._build_logo_design_prompt(requirements)

            # 调用图片生成服务
            from ..image_generation.config_manager import get_image_generation_manager
            from ..image_generation import ImageGenerationRequest

            manager = get_image_generation_manager()
            service = manager.get_service()

            # 创建生成请求
            request = ImageGenerationRequest(
                prompt=logo_prompt,
                width=1024,
                height=1024,
                num_images=1,
                quality="high"
            )

            result = await service.generate_image(request)

            if result.success:
                image_url = result.image_urls[0] if result.image_urls else None
                state["image_url"] = image_url
                state["design_prompt"] = logo_prompt
                state["current_state"] = "design_completed"

                # 添加成功消息
                success_message = AIMessage(
                    content=f"🎉 您的Logo设计已经完成！我为您创建了一个{requirements.style}风格的品牌标识。这个Logo体现了{requirements.mood}的品牌氛围，您觉得这个设计怎么样？如果需要调整，请告诉我！"
                )

                if "messages" not in state:
                    state["messages"] = []
                state["messages"].append(success_message)

                logger.info(f"Logo design generated successfully: {image_url}")
            else:
                error_msg = result.error_message or "图片生成失败"
                logger.error(f"Logo design generation failed: {error_msg}")
                state["error_message"] = f"Logo设计生成失败: {error_msg}"

            return state

        except Exception as e:
            logger.error(f"Logo design generation failed: {e}")
            state["error_message"] = f"Logo设计生成异常: {str(e)}"
            return state

    async def _build_logo_design_prompt(self, requirements: DesignRequirements) -> str:
        """使用LLM生成专业的中文Logo设计提示词"""

        # 构建专业的Logo设计专家提示词
        expert_prompt = f"""
你是一位获得国际设计大奖的顶级Logo设计大师，拥有30年世界顶级品牌设计经验，曾为苹果、可口可乐、奔驰、路易威登等全球知名品牌设计Logo。你深谙品牌视觉识别的精髓，擅长将品牌灵魂转化为永恒的视觉符号。

## 设计需求分析
- **Logo类型**: {requirements.category}
- **设计风格**: {requirements.style}
- **色彩策略**: {', '.join(requirements.colors) if isinstance(requirements.colors, list) else requirements.colors}
- **设计元素**: {', '.join(requirements.materials) if isinstance(requirements.materials, list) else requirements.materials}
- **应用场景**: {requirements.occasion}
- **目标受众**: {requirements.target_gender}
- **核心特征**: {', '.join(requirements.key_features) if isinstance(requirements.key_features, list) else requirements.key_features}
- **品牌氛围**: {requirements.mood}
- **用户原始需求**: {requirements.additional_notes}

## Logo设计大师思维框架

### 第一层：品牌基因解码
- **品牌核心**：从需求中提炼品牌的核心价值和独特基因
- **符号语言**：将抽象的品牌理念转化为具象的视觉符号
- **文化内涵**：融入深层的文化意义和情感共鸣

### 第二层：视觉识别系统
- **图形构成**：运用几何美学、黄金比例构建完美图形
- **字体设计**：选择或定制与品牌气质完美匹配的字体
- **色彩心理**：运用色彩心理学营造精准的品牌情感

### 第三层：技术工艺精度
- **矢量精度**：确保Logo在任何尺寸下都保持完美清晰
- **负空间运用**：巧妙运用负空间增强视觉冲击和记忆点
- **细节雕琢**：每一个像素都经过精心设计和优化

### 第四层：应用适配性
- **多场景适配**：考虑Logo在不同媒介和尺寸下的表现
- **单色版本**：确保Logo在单色情况下依然具有强烈识别性
- **国际化视野**：考虑Logo在不同文化背景下的接受度

### 第五层：时代永恒性
- **经典设计原则**：运用经得起时间考验的设计原则
- **创新元素**：融入时代特色但不失永恒价值
- **可持续发展**：为品牌未来发展预留视觉延展空间

## 创作要求
请运用你的顶级设计思维，创作一个极其精细、专业的中文Logo设计提示词：

1. **精确描述**：每个设计元素都要有精确的形状、比例、位置描述
2. **品牌深度**：深度体现品牌的核心价值和独特个性
3. **技术细节**：包含矢量精度、色彩数值、字体规格等专业要求
4. **视觉冲击**：确保Logo具有强烈的视觉识别性和记忆点
5. **文化内涵**：融入深层的文化意义和情感价值

## 输出标准
- 长度：250-350字的精细描述
- 语言：专业而富有艺术感的中文表达
- 结构：逻辑清晰，层次分明
- 创意：独特而具有永恒价值
- 格式：直接输出纯文本内容，不要任何标题、标记或格式符号

## 重要提醒
请直接输出Logo设计的描述内容，不要添加"专业Logo设计提示词"、"###"、"**"等任何标题或格式标记。

请开始创作你的专业Logo设计提示词：
"""

        try:
            # 调用LLM生成专业中文提示词
            response = await self.llm.ainvoke([HumanMessage(content=expert_prompt)])
            generated_prompt = response.content.strip()

            # 清理提示词，移除可能的标题和格式标记
            cleaned_prompt = self._clean_generated_prompt(generated_prompt)

            logger.info(f"Generated Chinese logo design prompt: {cleaned_prompt}")
            return cleaned_prompt

        except Exception as e:
            logger.error(f"Failed to generate logo prompt with LLM: {e}")
            # 如果LLM生成失败，抛出异常
            raise Exception(f"Logo提示词生成失败: {str(e)}")

    def _clean_generated_prompt(self, prompt: str) -> str:
        """清理生成的提示词，移除不必要的标题和格式标记"""

        # 移除常见的标题格式
        title_patterns = [
            r"###\s*专业Logo设计提示词\s*",
            r"##\s*专业Logo设计提示词\s*",
            r"#\s*专业Logo设计提示词\s*",
            r"专业Logo设计提示词[:：]\s*",
            r"【专业Logo设计提示词】\s*",
            r"\*\*专业Logo设计提示词\*\*\s*",
            r"Logo设计提示词[:：]\s*",
            r"提示词[:：]\s*",
        ]

        import re
        cleaned = prompt

        # 逐个移除标题模式
        for pattern in title_patterns:
            cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE)

        # 移除开头的换行符和空格
        cleaned = cleaned.lstrip('\n\r\t ')

        # 移除markdown格式标记
        cleaned = re.sub(r'^\*\*(.+?)\*\*', r'\1', cleaned)  # 移除开头的粗体标记
        cleaned = re.sub(r'^`(.+?)`', r'\1', cleaned)        # 移除开头的代码标记

        # 确保提示词不以标点符号开头
        while cleaned and cleaned[0] in '：:：。，、':
            cleaned = cleaned[1:].lstrip()

        logger.info(f"Cleaned prompt from '{prompt[:50]}...' to '{cleaned[:50]}...'")
        return cleaned
