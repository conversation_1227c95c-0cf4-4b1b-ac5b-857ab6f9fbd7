"""
Poster-specific workflow implementation.

This workflow is specialized for poster design conversations,
extending the base DesignWorkflow with poster-specific logic.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from langchain_core.messages import HumanMessage, AIMessage

from .design_workflow import DesignWorkflow
from .base_workflow import ConversationState, DesignRequirements

logger = logging.getLogger(__name__)


class PosterWorkflow(DesignWorkflow):
    """
    Poster-specific workflow implementation.
    
    Extends DesignWorkflow with poster-specific:
    - Requirement extraction logic
    - Design generation prompts
    - Modification handling
    """
    
    def __init__(self, design_agent=None):
        """Initialize poster workflow with optional design agent."""
        super().__init__(design_agent=design_agent)
        logger.info("PosterWorkflow initialized")
    
    async def _extract_requirements(self, user_input: str) -> DesignRequirements:
        """专业海报设计师的需求分析和细化"""

        extraction_prompt = f"""
你是一位资深的海报设计专家，拥有15年的商业设计经验。用户向你咨询海报设计，但他们的需求往往比较宽泛。作为专业人员，你需要：

1. **深度理解用户意图**：从简单描述中挖掘真实需求
2. **专业需求细化**：基于设计经验补充关键设计要素
3. **给出设计建议**：提供专业的设计方向建议

## 用户原始需求
"{user_input}"

## 专业分析任务
请基于你的专业经验，对用户需求进行深度分析和细化：

### 第一步：需求理解分析
- 用户真正想要什么类型的海报？
- 可能的应用场景和目标受众是什么？
- 从用户的表达中能推断出什么设计偏好？

### 第二步：专业需求细化
基于分析，请细化以下设计要素：

**海报类型分析**：
- category: 具体海报类型（宣传海报、产品海报、活动海报、品牌海报、招聘海报、公益海报等）

**设计风格建议**：
- style: 推荐的设计风格（现代简约、商务专业、创意艺术、复古怀旧、科技未来、手绘插画、极简主义等）

**色彩方案建议**：
- colors: 建议的主色调搭配（考虑行业特点、目标受众、情感表达）

**设计元素规划**：
- materials: 适合的设计元素（图标、插画、摄影图片、几何图形、文字排版、装饰元素等）

**应用场景定位**：
- occasion: 具体使用场合（线上推广、线下展示、社交媒体、印刷物料、活动现场等）

**受众群体分析**：
- target_gender: 目标受众特征（通用、商务人士、年轻群体、专业人士、消费者等）

**设计特征定义**：
- key_features: 核心设计特征（简洁明了、视觉冲击、专业可信、创意独特、信息清晰等）

**情感氛围营造**：
- mood: 期望营造的氛围（专业权威、活力动感、温馨亲和、科技前沿、艺术创意等）

## 输出要求
请以JSON格式输出分析结果，对于用户未明确提及的要素，请基于你的专业判断给出合理建议，避免使用"未指定"。

## 专业建议
在JSON后，请用1-2句话总结你的专业建议，说明为什么这样设计。
"""
        
        try:
            response = await self.llm.ainvoke([HumanMessage(content=extraction_prompt)])
            response_content = response.content.strip()

            # 尝试提取JSON部分（可能包含专业建议）
            json_start = response_content.find('{')
            json_end = response_content.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_content = response_content[json_start:json_end]
                try:
                    requirements_data = json.loads(json_content)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error in poster requirements: {e}")
                    logger.error(f"JSON content: {json_content[:200]}...")
                    raise ValueError(f"Invalid JSON format: {e}")

                # 提取专业建议（JSON后的内容）
                professional_advice = ""
                if json_end < len(response_content):
                    professional_advice = response_content[json_end:].strip()

                # 将专业建议添加到additional_notes
                full_notes = f"用户需求: {user_input}"
                if professional_advice:
                    full_notes += f"\n\n专业建议: {professional_advice}"

                logger.info(f"Extracted poster requirements with professional advice: {professional_advice}")

                # 确保数组字段的数据类型正确
                def ensure_array(value, default):
                    if isinstance(value, list):
                        return value
                    elif isinstance(value, str):
                        # 如果是字符串，尝试按逗号或顿号分割
                        return [item.strip() for item in value.replace('、', ',').split(',') if item.strip()]
                    else:
                        return default

                return DesignRequirements(
                    category=requirements_data.get("category", "宣传海报"),
                    style=requirements_data.get("style", "现代简约"),
                    colors=ensure_array(requirements_data.get("colors"), ["蓝色", "白色"]),
                    materials=ensure_array(requirements_data.get("materials"), ["图标", "文字排版"]),
                    occasion=requirements_data.get("occasion", "商业推广"),
                    target_gender=requirements_data.get("target_gender", "通用受众"),
                    key_features=ensure_array(requirements_data.get("key_features"), ["简洁明了", "视觉冲击"]),
                    mood=requirements_data.get("mood", "专业可信"),
                    additional_notes=full_notes
                )
            else:
                raise ValueError("无法从响应中提取有效的JSON数据")

        except Exception as e:
            logger.error(f"Poster requirements extraction failed: {e}")
            logger.error(f"LLM response: {response.content if 'response' in locals() else 'No response'}")

            # 如果专业分析失败，重新抛出异常让上层处理
            raise Exception(f"专业海报需求分析失败: {str(e)}")

    def _create_intelligent_default_requirements(self, user_input: str) -> DesignRequirements:
        """基于用户输入创建智能默认需求"""

        # 简单的关键词分析来推断需求
        input_lower = user_input.lower()

        # 推断海报类型
        if any(word in input_lower for word in ["产品", "商品", "销售"]):
            category = "产品海报"
            style = "商务专业"
            mood = "可信权威"
        elif any(word in input_lower for word in ["活动", "事件", "聚会", "庆典"]):
            category = "活动海报"
            style = "活力创意"
            mood = "热情活跃"
        elif any(word in input_lower for word in ["招聘", "求职", "人才"]):
            category = "招聘海报"
            style = "现代简约"
            mood = "专业可信"
        elif any(word in input_lower for word in ["品牌", "企业", "公司"]):
            category = "品牌海报"
            style = "现代商务"
            mood = "专业权威"
        else:
            category = "宣传海报"
            style = "现代简约"
            mood = "专业可信"

        # 推断颜色偏好
        if any(word in input_lower for word in ["科技", "数字", "智能"]):
            colors = ["蓝色", "白色", "灰色"]
        elif any(word in input_lower for word in ["活力", "年轻", "时尚"]):
            colors = ["橙色", "白色", "黑色"]
        elif any(word in input_lower for word in ["环保", "自然", "健康"]):
            colors = ["绿色", "白色", "棕色"]
        else:
            colors = ["蓝色", "白色"]

        return DesignRequirements(
            category=category,
            style=style,
            colors=colors,
            materials=["图标", "文字排版", "几何图形"],
            occasion="商业推广",
            target_gender="通用受众",
            key_features=["简洁明了", "视觉冲击", "信息清晰"],
            mood=mood,
            additional_notes=f"用户需求: {user_input}\n\n系统智能分析: 基于关键词分析推断的设计方向"
        )
    
    def _generate_confirmation_message(self, requirements: DesignRequirements) -> str:
        """生成专业的海报设计需求确认消息"""

        # 安全的数组转字符串处理
        def safe_join(value, default="未指定"):
            if isinstance(value, list) and value:
                return "、".join(str(item) for item in value)
            elif isinstance(value, str) and value:
                return value
            else:
                return default

        colors_str = safe_join(requirements.colors)
        materials_str = safe_join(requirements.materials)
        features_str = safe_join(requirements.key_features)

        # 检查是否有专业建议
        has_professional_advice = "专业建议:" in requirements.additional_notes

        confirmation_msg = f"""
🎨 **专业海报设计方案**

基于您的需求，我为您制定了以下设计方案：

📋 **设计规划**
• **海报类型**: {requirements.category}
• **设计风格**: {requirements.style}
• **色彩方案**: {colors_str}
• **设计元素**: {materials_str}
• **应用场景**: {requirements.occasion}
• **目标受众**: {requirements.target_gender}
• **核心特征**: {features_str}
• **情感氛围**: {requirements.mood}
"""

        # 如果有专业建议，添加到确认消息中
        if has_professional_advice:
            advice_start = requirements.additional_notes.find("专业建议:")
            if advice_start != -1:
                professional_advice = requirements.additional_notes[advice_start:].strip()
                confirmation_msg += f"""

💡 **{professional_advice}**
"""

        confirmation_msg += """

✅ **下一步操作**
如果您对以上设计方案满意，请回复"确认"开始生成海报。
如需调整任何设计要素，请直接告诉我您的想法。
        """

        return confirmation_msg.strip()
    
    async def _determine_modification_type(self, user_input: str) -> str:
        """智能判断海报设计修改类型"""
        
        modification_prompt = f"""
        Determine whether the poster design modification should use "image_edit" or "design_regeneration".

        Poster-specific guidelines:
        - image_edit: Color adjustments, brightness, contrast, text color changes, minor visual tweaks
        - design_regeneration: Layout changes, style changes, content changes, structural modifications

        Examples:
        - "让颜色更鲜艳" → image_edit
        - "改成简约风格" → design_regeneration  
        - "换个布局" → design_regeneration
        - "调整亮度" → image_edit
        - "改成商务风格" → design_regeneration
        - "文字颜色改成红色" → image_edit
        - "添加更多图标" → design_regeneration

        User request: "{user_input}"

        Respond with ONLY: image_edit OR design_regeneration
        """

        try:
            response = await self.llm.ainvoke([HumanMessage(content=modification_prompt)])
            modification_type = response.content.strip().lower()

            if modification_type not in ["image_edit", "design_regeneration"]:
                logger.warning(f"Invalid modification type: {modification_type}, defaulting to design_regeneration")
                modification_type = "design_regeneration"

            logger.info(f"Poster modification type determined: {modification_type}")
            return modification_type

        except Exception as e:
            logger.error(f"Error determining poster modification type: {e}")
            return "design_regeneration"

    async def _intent_recognition_node(self, state):
        """
        海报设计专用的意图识别节点
        重写父类方法，使用海报设计专用的提示词
        """
        try:
            user_input = state["user_input"]
            current_requirements = state.get("requirements")
            current_state = state.get("current_state")
            design_history = state.get("design_history", [])
            messages = state.get("messages", [])

            # 检查是否有编辑图片URL
            edit_image_url = state.get("edit_image_url")
            has_selected_image = bool(edit_image_url)
            logger.info(f"Has selected image for editing: {has_selected_image}, URL: {edit_image_url}")

            # 构建上下文信息
            context_info = []
            if current_requirements:
                context_info.append(f"Current poster requirements: {current_requirements}")
            if current_state:
                context_info.append(f"Current workflow state: {current_state}")
            if design_history:
                context_info.append(f"Design history: {len(design_history)} previous designs")
            if has_selected_image:
                context_info.append(f"Selected image for editing: {edit_image_url}")
            if messages:
                recent_messages = messages[-3:] if len(messages) > 3 else messages
                context_info.append(f"Recent conversation: {[msg.get('content', '') for msg in recent_messages]}")

            intent_prompt = f"""
            You are an expert intent classifier for a poster design AI system.
            Analyze the user's message with full conversation context to determine their intent.

            Intent types (直接对应工作流节点):
            - casual_chat: General conversation, greetings, questions not related to poster design
            - design_generation: User wants to create a NEW poster design (直接进入生成，无需确认)
            - design_modification: User wants to modify an EXISTING poster design (包括设计修改和图片编辑)

            Context information:
            {chr(10).join(context_info) if context_info else "No previous context"}

            User message: "{user_input}"

            Important classification rules:
            1. If user provides poster design requirements or wants to create something → design_generation
            2. If user wants to modify existing poster design or selected image → design_modification
            3. If user is just chatting or asking general questions → casual_chat

            Respond with ONLY the intent type: casual_chat, design_generation, or design_modification
            """

            response = await self.llm.ainvoke([HumanMessage(content=intent_prompt)])
            intent = response.content.strip().lower()

            # Validate intent
            from .base_workflow import IntentType
            valid_intents = [e.value for e in IntentType]
            if intent not in valid_intents:
                logger.warning(f"Invalid intent detected: {intent}, defaulting to casual_chat")
                intent = IntentType.CASUAL_CHAT

            state["intent"] = IntentType(intent)
            from .base_workflow import WorkflowState
            state["current_state"] = WorkflowState.PROCESSING

            logger.info(f"Poster intent recognized: {intent} for input: '{user_input[:50]}...'")
            return state

        except Exception as e:
            logger.error(f"Poster intent recognition failed: {e}")
            from .base_workflow import IntentType
            state["intent"] = IntentType.CASUAL_CHAT
            state["error_message"] = f"Intent recognition error: {str(e)}"
            return state

    async def _modify_requirements(self, user_input: str, current_requirements: DesignRequirements) -> DesignRequirements:
        """修改现有海报需求"""

        modification_prompt = f"""
        You are a professional poster design assistant. Modify the existing poster design requirements based on user's new input.

        Current requirements: {current_requirements.__dict__ if hasattr(current_requirements, '__dict__') else current_requirements}
        User modification request: "{user_input}"

        CRITICAL INSTRUCTIONS:
        1. Return ONLY a valid JSON object with ALL the required fields
        2. Do not include any explanations, markdown formatting, or additional text
        3. Only change the fields that the user specifically mentions
        4. Keep all other fields unchanged from the current requirements
        5. Ensure all array fields (colors, materials, key_features) are arrays

        Required JSON structure:
        {{
            "category": "string",
            "style": "string",
            "colors": ["array", "of", "strings"],
            "materials": ["array", "of", "strings"],
            "occasion": "string",
            "target_gender": "string",
            "key_features": ["array", "of", "strings"],
            "mood": "string",
            "additional_notes": "string"
        }}
        """

        try:
            response = await self.llm.ainvoke([HumanMessage(content=modification_prompt)])
            response_content = response.content.strip()

            # 尝试提取JSON内容
            if response_content.startswith('```json'):
                # 移除markdown代码块标记
                response_content = response_content.replace('```json', '').replace('```', '').strip()
            elif response_content.startswith('```'):
                # 移除普通代码块标记
                response_content = response_content.replace('```', '').strip()

            # 查找JSON对象的开始和结束
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = response_content[start_idx:end_idx + 1]
                updated_data = json.loads(json_content)

                # 验证必要字段
                if not isinstance(updated_data, dict):
                    raise ValueError("Response is not a valid dictionary")

                return DesignRequirements(**updated_data)
            else:
                raise ValueError("No valid JSON object found in response")

        except json.JSONDecodeError as e:
            logger.error(f"Poster requirements modification JSON decode failed: {e}")
            logger.error(f"Response content: {response.content[:200]}...")
            return current_requirements
        except Exception as e:
            logger.error(f"Poster requirements modification failed: {e}")
            logger.error(f"Response content: {response.content[:200] if hasattr(response, 'content') else 'No response content'}...")
            return current_requirements

    def get_poster_specific_prompts(self) -> Dict[str, str]:
        """获取海报设计专用的提示词模板"""
        
        return {
            "business_poster": """
            Create a professional business poster with:
            - Clean, corporate design aesthetic
            - Professional color scheme
            - Clear information hierarchy
            - Modern typography
            - Suitable for office environments
            """,
            
            "event_poster": """
            Create an engaging event poster with:
            - Eye-catching visual design
            - Clear event information display
            - Vibrant and appealing colors
            - Dynamic layout and composition
            - Suitable for promotional purposes
            """,
            
            "product_poster": """
            Create a compelling product poster with:
            - Product-focused design
            - Commercial appeal
            - Brand-consistent styling
            - Clear product benefits
            - Suitable for marketing campaigns
            """,
            
            "creative_poster": """
            Create an artistic creative poster with:
            - Innovative design concepts
            - Unique visual elements
            - Creative typography
            - Artistic color combinations
            - Suitable for creative industries
            """
        }

    def get_design_guidelines(self) -> Dict[str, List[str]]:
        """获取海报设计指导原则"""
        
        return {
            "layout_principles": [
                "视觉层次清晰",
                "信息组织有序",
                "留白空间合理",
                "对齐方式统一"
            ],
            
            "color_guidelines": [
                "主色调突出",
                "配色和谐统一",
                "对比度适中",
                "符合品牌调性"
            ],
            
            "typography_rules": [
                "字体层次分明",
                "可读性良好",
                "风格统一协调",
                "大小比例合适"
            ],
            
            "visual_elements": [
                "图像质量高清",
                "元素风格统一",
                "装饰恰到好处",
                "整体平衡美观"
            ]
        }

    async def _design_generation_node(self, state):
        """
        海报设计专用的设计生成节点
        重写父类方法，使用海报设计专用的生成逻辑
        """
        try:
            conversation_id = state.get("conversation_id")
            user_input = state.get("user_input", "")
            requirements = state.get("requirements")

            logger.info(f"Starting poster design generation for conversation: {conversation_id}")

            # 如果没有需求，先提取需求
            if not requirements:
                logger.info("No requirements found, extracting from user input")
                requirements = await self._extract_requirements(user_input)
                state["requirements"] = requirements
                logger.info(f"Extracted requirements: {requirements.__dict__ if hasattr(requirements, '__dict__') else requirements}")

            # 使用海报设计专用的生成逻辑
            logger.info("Using poster design generation")

            # 构建海报设计提示词
            poster_prompt = await self._build_poster_design_prompt(requirements)

            # 调用图片生成服务
            from ..image_generation.config_manager import get_image_generation_manager
            from ..image_generation import ImageGenerationRequest

            manager = get_image_generation_manager()
            service = manager.get_service()

            # 创建生成请求
            request = ImageGenerationRequest(
                prompt=poster_prompt,
                width=1024,
                height=1024,
                num_images=1,
                quality="high"
            )

            result = await service.generate_image(request)

            if result.success:
                image_url = result.image_urls[0] if result.image_urls else None
                state["image_url"] = image_url
                state["design_prompt"] = poster_prompt
                state["current_state"] = "design_completed"

                # 添加成功消息
                success_message = AIMessage(
                    content=f"🎉 您的海报设计已经完成！我为您创建了一个{requirements.style}风格的设计。您觉得这个设计怎么样？如果需要调整，请告诉我！"
                )

                if "messages" not in state:
                    state["messages"] = []
                state["messages"].append(success_message)

                logger.info(f"Poster design generated successfully: {image_url}")
            else:
                error_msg = result.error_message or "图片生成失败"
                logger.error(f"Poster design generation failed: {error_msg}")
                state["error_message"] = f"海报设计生成失败: {error_msg}"

            return state

        except Exception as e:
            logger.error(f"Poster design generation failed: {e}")
            state["error_message"] = f"海报设计生成异常: {str(e)}"
            return state

    async def _build_poster_design_prompt(self, requirements: DesignRequirements) -> str:
        """使用LLM生成专业的中文海报设计提示词"""

        # 构建专业的海报设计专家提示词
        expert_prompt = f"""
你是一位获得国际设计大奖的资深海报设计专家，拥有20年顶级商业设计经验，曾为苹果、谷歌、奔驰等知名品牌设计海报。你深谙视觉传达的精髓，擅长将抽象概念转化为精准的视觉语言。

## 设计需求分析
- **海报类型**: {requirements.category}
- **设计风格**: {requirements.style}
- **主要颜色**: {', '.join(requirements.colors) if isinstance(requirements.colors, list) else requirements.colors}
- **设计元素**: {', '.join(requirements.materials) if isinstance(requirements.materials, list) else requirements.materials}
- **使用场合**: {requirements.occasion}
- **目标受众**: {requirements.target_gender}
- **设计特征**: {', '.join(requirements.key_features) if isinstance(requirements.key_features, list) else requirements.key_features}
- **整体氛围**: {requirements.mood}
- **用户原始需求**: {requirements.additional_notes}

## 专业设计思维框架

### 第一层：视觉层次构建
- **主视觉焦点**：确定画面的绝对主角和视觉重心
- **次级信息层**：规划支撑信息的视觉权重和位置关系
- **背景氛围层**：营造整体情感基调和空间深度

### 第二层：色彩心理学应用
- **主色调情感**：分析色彩的心理暗示和文化内涵
- **色彩比例**：精确控制各色彩的面积占比和饱和度
- **色彩渐变**：运用渐变营造空间感和现代感

### 第三层：字体设计美学
- **字体性格**：选择与品牌调性完美匹配的字体风格
- **字重层次**：通过字重变化建立清晰的信息层级
- **字间距与行距**：精确控制文字的呼吸感和阅读节奏

### 第四层：构图美学原理
- **黄金比例**：运用黄金分割点安排关键元素位置
- **视觉平衡**：通过元素的大小、颜色、位置创造动态平衡
- **留白艺术**：巧妙运用负空间增强视觉冲击力

### 第五层：材质与光影
- **表面质感**：细腻描述材质的触感和光泽度
- **光影关系**：营造立体感和空间层次
- **细节雕琢**：关注边缘处理、阴影效果等微观细节

## 创作要求
请运用你的顶级设计思维，创作一个极其精细、专业的中文海报设计提示词：

1. **精确描述**：每个设计元素都要有精确的位置、大小、质感描述
2. **情感传达**：深度考虑色彩、构图对目标受众的心理影响
3. **技术细节**：包含光影、材质、边缘处理等专业技术要求
4. **品牌思维**：体现对品牌价值和市场定位的深度理解
5. **创新元素**：融入独特的创意点，避免千篇一律的设计

## 输出标准
- 长度：200-300字的精细描述
- 语言：专业而富有诗意的中文表达
- 结构：逻辑清晰，层次分明
- 创意：独特而不失商业价值
- 格式：直接输出纯文本内容，不要任何标题、标记或格式符号

## 重要提醒
请直接输出海报设计的描述内容，不要添加"专业海报设计提示词"、"###"、"**"等任何标题或格式标记。

请开始创作你的专业海报设计提示词：
"""

        try:
            # 调用LLM生成专业中文提示词
            response = await self.llm.ainvoke([HumanMessage(content=expert_prompt)])
            generated_prompt = response.content.strip()

            # 清理提示词，移除可能的标题和格式标记
            cleaned_prompt = self._clean_generated_prompt(generated_prompt)

            logger.info(f"Generated Chinese poster design prompt: {cleaned_prompt}")
            return cleaned_prompt

        except Exception as e:
            logger.error(f"Failed to generate poster prompt with LLM: {e}")
            # 如果LLM生成失败，抛出异常
            raise Exception(f"海报提示词生成失败: {str(e)}")

    def _clean_generated_prompt(self, prompt: str) -> str:
        """清理生成的提示词，移除不必要的标题和格式标记"""

        # 移除常见的标题格式
        title_patterns = [
            r"###\s*专业海报设计提示词\s*",
            r"##\s*专业海报设计提示词\s*",
            r"#\s*专业海报设计提示词\s*",
            r"专业海报设计提示词[:：]\s*",
            r"【专业海报设计提示词】\s*",
            r"\*\*专业海报设计提示词\*\*\s*",
            r"海报设计提示词[:：]\s*",
            r"提示词[:：]\s*",
        ]

        import re
        cleaned = prompt

        # 逐个移除标题模式
        for pattern in title_patterns:
            cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE)

        # 移除开头的换行符和空格
        cleaned = cleaned.lstrip('\n\r\t ')

        # 移除markdown格式标记
        cleaned = re.sub(r'^\*\*(.+?)\*\*', r'\1', cleaned)  # 移除开头的粗体标记
        cleaned = re.sub(r'^`(.+?)`', r'\1', cleaned)        # 移除开头的代码标记

        # 确保提示词不以标点符号开头
        while cleaned and cleaned[0] in '：:：。，、':
            cleaned = cleaned[1:].lstrip()

        logger.info(f"Cleaned prompt from '{prompt[:50]}...' to '{cleaned[:50]}...'")
        return cleaned


