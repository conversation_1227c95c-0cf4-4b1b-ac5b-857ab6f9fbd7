"""
Universal design workflow supporting multiple design types.

This workflow provides a common conversation flow for all design types:
- Intent recognition
- Requirement confirmation
- Design generation (delegated to specific agents)
- Design modification
- Error handling
"""

import json
import uuid
from typing import Dict, Any, Optional, List
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
import logging

from .base_workflow import (
    BaseWorkflow, ConversationState, DesignRequirements, 
    IntentType, WorkflowState
)

logger = logging.getLogger(__name__)


class DesignWorkflow(BaseWorkflow):
    """
    Universal design workflow supporting multiple design types.

    Implements a multi-agent system with:
    - Intent recognition
    - Requirement extraction and planning
    - Design generation (delegated to specific agents)
    - Error handling and recovery
    """

    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow with all nodes and edges."""
        workflow = StateGraph(ConversationState)
        
        # Add nodes (简化后的4个核心节点)
        workflow.add_node("intent_recognition", self._intent_recognition_node)
        workflow.add_node("casual_chat", self._casual_chat_node)
        workflow.add_node("design_generation", self._design_generation_node)               # 设计生成（包含需求提取）
        workflow.add_node("design_modification", self._design_modification_node)          # 设计修改
        workflow.add_node("error_handler", self._error_handler_node)
        
        # Set entry point
        workflow.set_entry_point("intent_recognition")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "intent_recognition",
            self._route_after_intent,
            {
                "casual_chat": "casual_chat",
                "design_generation": "design_generation",               # 设计请求直接进入生成
                "design_modification": "design_modification",          # 设计修改
                "error": "error_handler"
            }
        )

        # Add edges to END
        workflow.add_edge("casual_chat", END)
        workflow.add_edge("design_generation", END)
        workflow.add_edge("design_modification", END)
        workflow.add_edge("error_handler", END)
        
        # Compile with memory
        return workflow.compile(checkpointer=self.memory)
    
    def _route_after_intent(self, state: ConversationState) -> str:
        """Route conversation based on detected intent - 直接映射到对应节点."""
        intent = state.get("intent")

        # 简化的路由逻辑
        if intent == IntentType.CASUAL_CHAT:
            return "casual_chat"
        elif intent == IntentType.DESIGN_GENERATION:
            return "design_generation"  # 设计请求直接进入生成
        elif intent == IntentType.DESIGN_MODIFICATION:
            return "design_modification"
        else:
            return "error"

    async def _intent_recognition_node(self, state: ConversationState) -> ConversationState:
        """
        Enhanced Intent Recognition Agent Node.
        Analyzes user input with full conversation context to determine intent.
        Supports multiple designs in one conversation.
        """
        try:
            user_input = state["user_input"]
            current_requirements = state.get("requirements")
            current_state = state.get("current_state")
            design_history = state.get("design_history", [])
            messages = state.get("messages", [])

            # 检查是否有编辑图片URL（但不直接判断意图，让LLM智能分析）
            edit_image_url = state.get("edit_image_url")
            has_selected_image = bool(edit_image_url)
            logger.info(f"Has selected image for editing: {has_selected_image}, URL: {edit_image_url}")

            # 构建上下文信息
            context_info = []
            if current_requirements:
                context_info.append(f"Current requirements: {current_requirements.__dict__ if hasattr(current_requirements, '__dict__') else current_requirements}")
            if design_history:
                context_info.append(f"Design history: {len(design_history)} designs created")
            if has_selected_image:
                context_info.append(f"User has selected an image for editing: {edit_image_url}")
            if messages:
                recent_messages = messages[-3:] if len(messages) > 3 else messages
                context_info.append(f"Recent conversation: {[msg.get('content', '') for msg in recent_messages]}")

            intent_prompt = f"""
            You are an expert intent classifier for a fashion design AI system.
            Analyze the user's message with full conversation context to determine their intent.

            Intent types (直接对应工作流节点):
            - casual_chat: General conversation, greetings, questions not related to design
            - design_generation: User wants to create a NEW design (直接进入生成，无需确认)
            - design_modification: User wants to modify an EXISTING design (包括设计修改和图片编辑)

            Context information:
            {chr(10).join(context_info) if context_info else "No previous context"}

            User message: "{user_input}"

            Important classification rules:
            1. If user provides design requirements or wants to create something → design_generation
            2. If user wants to modify existing design or selected image → design_modification
            3. If user is just chatting or asking general questions → casual_chat

            Respond with ONLY the intent type: casual_chat, design_generation, or design_modification
            """

            response = await self.llm.ainvoke([HumanMessage(content=intent_prompt)])
            intent = response.content.strip().lower()

            # Validate intent
            valid_intents = [e.value for e in IntentType]
            if intent not in valid_intents:
                logger.warning(f"Invalid intent detected: {intent}, defaulting to casual_chat")
                intent = IntentType.CASUAL_CHAT

            state["intent"] = IntentType(intent)
            state["current_state"] = WorkflowState.PROCESSING

            logger.info(f"Intent recognized: {intent} for input: '{user_input[:50]}...'")
            return state

        except Exception as e:
            logger.error(f"Intent recognition failed: {e}")
            state["intent"] = IntentType.CASUAL_CHAT
            state["error_message"] = f"Intent recognition error: {str(e)}"
            return state

    async def _casual_chat_node(self, state: ConversationState) -> ConversationState:
        """Handle casual conversation."""
        try:
            user_input = state["user_input"]
            
            chat_prompt = f"""
            You are a friendly fashion design AI assistant. The user is having a casual conversation.
            Respond naturally and helpfully. If they seem interested in fashion design, gently guide them.
            
            User message: "{user_input}"
            
            Respond in a friendly, conversational tone in Chinese.
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=chat_prompt)])
            
            # Add response to messages
            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(AIMessage(content=response.content))
            
            state["current_state"] = WorkflowState.COMPLETED
            
            return state
            
        except Exception as e:
            logger.error(f"Casual chat failed: {e}")
            state["error_message"] = f"Chat processing error: {str(e)}"
            state["current_state"] = WorkflowState.ERROR
            return state

    async def _error_handler_node(self, state: ConversationState) -> ConversationState:
        """Handle errors and provide recovery options."""
        error_message = state.get("error_message", "Unknown error occurred")
        
        # Add error response to messages
        if "messages" not in state:
            state["messages"] = []
        
        error_response = f"抱歉，处理您的请求时遇到了问题：{error_message}。请重新尝试或换个方式描述您的需求。"
        state["messages"].append(AIMessage(content=error_response))
        
        state["current_state"] = WorkflowState.ERROR
        
        logger.error(f"Error handled: {error_message}")
        return state



    async def _design_generation_node(self, state: ConversationState) -> ConversationState:
        """
        Design Generation Agent Node.
        包含需求提取和设计生成 - 支持多种设计类型
        """
        try:
            user_input = state.get("user_input", "")
            conversation_id = state.get("conversation_id")
            requirements = state.get("requirements")

            # 如果没有需求，先提取需求
            if not requirements:
                logger.info("Extracting requirements from user input")
                requirements = await self._extract_requirements(user_input)
                state["requirements"] = requirements

            logger.info(f"Starting design generation for conversation: {conversation_id}")

            # 如果有注入的设计Agent，委托给它处理
            if self.design_agent and hasattr(self.design_agent, 'generate_design'):
                logger.info(f"Delegating to design agent: {self.design_agent.__class__.__name__}")

                # 追踪传递给Agent的数据
                logger.info(f"=== Workflow to Agent Debug ===")
                logger.info(f"requirements type: {type(requirements)}")
                logger.info(f"requirements value: {requirements}")
                logger.info(f"==============================")

                result = await self.design_agent.generate_design(
                    user_input=user_input,
                    requirements=requirements,
                    conversation_id=conversation_id,
                    user_id=state.get("user_id")
                )
            else:
                # 默认使用服装设计逻辑（向后兼容）
                logger.info("Using default fashion design generation")
                result = await self._default_fashion_design_generation(
                    user_input, requirements, conversation_id
                )

            # 处理生成结果
            if result.get("error"):
                raise Exception(result["error"])

            # 更新状态
            state["image_url"] = result.get("image_url")
            state["design_prompt"] = result.get("optimized_prompt", result.get("design_prompt"))

            # 更新设计历史
            design_history = state.get("design_history", [])
            design_number = len(design_history) + 1

            design_entry = {
                "design_id": str(uuid.uuid4()),
                "design_number": design_number,
                "image_url": result.get("image_url"),
                "prompt": result.get("optimized_prompt", result.get("design_prompt")),
                "requirements": requirements.__dict__ if hasattr(requirements, '__dict__') else requirements,
                "created_at": str(uuid.uuid4()),  # Placeholder
                "tools_used": result.get("tools_used", 0),
                "generation_method": result.get("generation_method", "unknown")
            }

            design_history.append(design_entry)
            state["design_history"] = design_history

            # 生成成功消息
            tools_used = result.get("tools_used", 0)
            category = getattr(requirements, 'category', '设计')
            style = getattr(requirements, 'style', '现代')

            success_message = f"🎉 您的{category}设计已经完成！我为您创建了一个{style}风格的设计，使用了{tools_used}个专业工具来确保质量。您觉得这个设计怎么样？如果需要调整，请告诉我！"

            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(AIMessage(content=success_message))

            # 设置工作流状态为完成
            state["current_state"] = WorkflowState.COMPLETED
            state["intent"] = IntentType.DESIGN_GENERATION  # 设置意图类型

            logger.info(f"New design #{design_number} generation completed: {result.get('image_url')}")
            logger.info(f"Total designs in conversation: {len(design_history)}")

            return state

        except Exception as e:
            logger.error(f"Error in design generation: {e}")
            state["error_message"] = f"Design generation failed: {str(e)}"
            state["current_state"] = WorkflowState.ERROR
            return state

    async def _default_fashion_design_generation(
        self,
        user_input: str,
        requirements: DesignRequirements,
        conversation_id: str
    ) -> Dict[str, Any]:
        """默认的服装设计生成逻辑（向后兼容）"""

        # 使用自主工具调用代理
        from ..autonomous_agent import AutonomousToolCallingAgent
        autonomous_agent = AutonomousToolCallingAgent()

        logger.info(f"Using default fashion design generation for conversation: {conversation_id}")

        # LLM自主决策工具调用生成新设计
        result = await autonomous_agent.autonomous_design_generation(
            user_input=user_input,
            structured_requirements=requirements.__dict__ if hasattr(requirements, '__dict__') else requirements,
            conversation_id=conversation_id
        )

        return result

    async def _design_modification_node(self, state: ConversationState) -> ConversationState:
        """
        Design Modification Agent Node.
        统一处理设计修改和图片编辑，智能选择最佳方法
        """
        try:
            user_input = state.get("user_input", "")
            current_image = state.get("image_url")  # 历史对话中的最后一张图
            selected_image = state.get("edit_image_url")  # 前端传递的用户选择图片
            conversation_id = state.get("conversation_id")

            logger.info(f"Processing design modification")
            logger.info(f"Current image (last in conversation): {current_image}")
            logger.info(f"Selected image (user choice from frontend): {selected_image}")

            # 确定目标图片
            target_image = selected_image or current_image
            if not target_image:
                raise ValueError("No image available for modification")

            # 使用自主工具调用代理
            from ..autonomous_agent import AutonomousToolCallingAgent
            autonomous_agent = AutonomousToolCallingAgent()

            # 智能判断修改类型
            modification_type = await self._determine_modification_type(user_input)

            if modification_type == "image_edit":
                # 使用图片编辑（适合局部修改）
                logger.info("Using image edit approach for modification")
                result = await self._execute_image_edit(autonomous_agent, user_input, target_image, conversation_id, state)

                # 检查图片编辑是否成功，如果失败则降级到重新生成
                if not result.get("image_url") or result.get("error"):
                    logger.warning("Image edit failed, falling back to design regeneration")
                    logger.info("Using design regeneration as fallback")
                    result = await self._execute_design_regeneration(autonomous_agent, user_input, target_image, conversation_id, state)
            else:
                # 使用设计重生成（适合大幅修改）
                logger.info("Using design regeneration approach for modification")
                result = await self._execute_design_regeneration(autonomous_agent, user_input, target_image, conversation_id, state)

            # 处理修改结果
            if result.get("error"):
                raise Exception(result["error"])

            # 验证最终结果
            if not result.get("image_url"):
                raise Exception("Design modification failed: No image generated")

            # 更新状态
            state["image_url"] = result.get("image_url")
            state["design_prompt"] = result.get("optimized_prompt", result.get("design_prompt"))

            # 生成成功消息
            success_message = f"✨ 设计修改完成！我已经根据您的要求调整了设计。您觉得这个修改效果如何？"

            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(AIMessage(content=success_message))

            state["current_state"] = WorkflowState.COMPLETED
            state["intent"] = IntentType.DESIGN_MODIFICATION  # 设置意图类型

            logger.info(f"Design modification completed: {result.get('image_url')}")
            return state

        except Exception as e:
            logger.error(f"Error in design modification: {e}")
            state["error_message"] = f"Design modification failed: {str(e)}"
            state["current_state"] = WorkflowState.ERROR
            return state

    # 辅助方法
    async def _extract_requirements(self, user_input: str) -> DesignRequirements:
        """从用户输入中提取设计需求"""

        extraction_prompt = f"""
        Extract fashion design requirements from the user's input.

        User input: "{user_input}"

        Extract the following information (use "未指定" if not mentioned):
        - category: 服装类型 (如：连衣裙、衬衫、裤子等)
        - style: 风格 (如：现代、复古、简约等)
        - colors: 颜色列表 (如：["蓝色", "白色"])
        - materials: 材质列表 (如：["棉质", "丝绸"])
        - occasion: 场合 (如：日常、正式、运动等)
        - target_gender: 目标性别 (如：女性、男性、通用)
        - key_features: 关键特征列表 (如：["宽松", "短袖"])
        - mood: 情绪/氛围 (如：优雅、活泼、专业等)

        Return as JSON format.
        """

        try:
            response = await self.llm.ainvoke([HumanMessage(content=extraction_prompt)])
            response_content = response.content.strip()

            # 尝试解析JSON，处理可能的```json标记
            try:
                # 清理响应内容，移除可能的markdown标记
                clean_content = response_content
                if '```json' in clean_content:
                    # 提取```json和```之间的内容
                    start_marker = '```json'
                    end_marker = '```'
                    start_idx = clean_content.find(start_marker)
                    if start_idx != -1:
                        start_idx += len(start_marker)
                        end_idx = clean_content.find(end_marker, start_idx)
                        if end_idx != -1:
                            clean_content = clean_content[start_idx:end_idx].strip()
                elif '```' in clean_content:
                    # 处理普通代码块
                    clean_content = clean_content.replace('```', '').strip()

                # 查找JSON对象的开始和结束
                start_idx = clean_content.find('{')
                end_idx = clean_content.rfind('}')

                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    json_content = clean_content[start_idx:end_idx + 1]
                    requirements_data = json.loads(json_content)
                    logger.info(f"Successfully parsed requirements: {requirements_data}")
                else:
                    raise ValueError("No valid JSON object found in response")

            except json.JSONDecodeError as json_error:
                logger.warning(f"JSON parsing failed: {json_error}, response: {response_content[:200]}...")
                # 如果JSON解析失败，使用基础需求
                requirements_data = {}
            except ValueError as ve:
                logger.warning(f"JSON extraction failed: {ve}, response: {response_content[:200]}...")
                # 如果JSON提取失败，使用基础需求
                requirements_data = {}

            # 确保数组字段的数据类型正确
            def ensure_array(value, default):
                if isinstance(value, list):
                    return value
                elif isinstance(value, str):
                    # 如果是字符串，尝试按逗号或顿号分割
                    return [item.strip() for item in value.replace('、', ',').split(',') if item.strip()]
                else:
                    return default

            return DesignRequirements(
                category=requirements_data.get("category", "服装"),
                style=requirements_data.get("style", "现代"),
                colors=ensure_array(requirements_data.get("colors"), ["中性色"]),
                materials=ensure_array(requirements_data.get("materials"), []),
                occasion=requirements_data.get("occasion", "日常"),
                target_gender=requirements_data.get("target_gender", "通用"),
                key_features=ensure_array(requirements_data.get("key_features"), []),
                mood=requirements_data.get("mood", "时尚"),
                additional_notes=user_input
            )

        except Exception as e:
            logger.error(f"Requirements extraction failed: {e}")
            # 返回基础需求
            return DesignRequirements(
                category="服装",
                style="现代",
                colors=["中性色"],
                materials=[],
                occasion="日常",
                target_gender="通用",
                key_features=[],
                mood="时尚",
                additional_notes=user_input
            )

    async def _modify_requirements(self, user_input: str, current_requirements: DesignRequirements) -> DesignRequirements:
        """修改现有需求"""

        modification_prompt = f"""
        You are a professional design assistant. Modify the existing design requirements based on user's new input.

        Current requirements: {current_requirements.__dict__ if hasattr(current_requirements, '__dict__') else current_requirements}
        User modification request: "{user_input}"

        CRITICAL INSTRUCTIONS:
        1. Return ONLY a valid JSON object with ALL the required fields
        2. Do not include any explanations, markdown formatting, or additional text
        3. Only change the fields that the user specifically mentions
        4. Keep all other fields unchanged from the current requirements
        5. Ensure all array fields (colors, materials, key_features) are arrays

        Required JSON structure:
        {{
            "category": "string",
            "style": "string",
            "colors": ["array", "of", "strings"],
            "materials": ["array", "of", "strings"],
            "occasion": "string",
            "target_gender": "string",
            "key_features": ["array", "of", "strings"],
            "mood": "string",
            "additional_notes": "string"
        }}
        """

        try:
            response = await self.llm.ainvoke([HumanMessage(content=modification_prompt)])
            response_content = response.content.strip()

            # 清理响应内容，移除可能的markdown标记
            clean_content = response_content
            if '```json' in clean_content:
                start_marker = '```json'
                end_marker = '```'
                start_idx = clean_content.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = clean_content.find(end_marker, start_idx)
                    if end_idx != -1:
                        clean_content = clean_content[start_idx:end_idx].strip()
            elif '```' in clean_content:
                # 处理普通代码块
                clean_content = clean_content.replace('```', '').strip()

            # 查找JSON对象的开始和结束
            start_idx = clean_content.find('{')
            end_idx = clean_content.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = clean_content[start_idx:end_idx + 1]
                updated_data = json.loads(json_content)

                # 验证必要字段
                if not isinstance(updated_data, dict):
                    raise ValueError("Response is not a valid dictionary")

                return DesignRequirements(**updated_data)
            else:
                raise ValueError("No valid JSON object found in response")

        except json.JSONDecodeError as e:
            logger.error(f"Requirements modification JSON decode failed: {e}")
            logger.error(f"Response content: {response.content[:200] if hasattr(response, 'content') else 'No response content'}...")
            return current_requirements
        except Exception as e:
            logger.error(f"Requirements modification failed: {e}")
            logger.error(f"Response content: {response.content[:200] if hasattr(response, 'content') else 'No response content'}...")
            return current_requirements

    def _generate_confirmation_message(self, requirements: DesignRequirements) -> str:
        """生成需求确认消息"""

        # 安全的数组转字符串处理
        def safe_join(value, default="未指定"):
            if isinstance(value, list) and value:
                return "、".join(str(item) for item in value)
            elif isinstance(value, str) and value:
                return value
            else:
                return default

        colors_str = safe_join(requirements.colors)
        features_str = safe_join(requirements.key_features)

        return f"""
📋 **设计需求确认**

• **类型**: {requirements.category}
• **风格**: {requirements.style}
• **颜色**: {colors_str}
• **场合**: {requirements.occasion}
• **目标**: {requirements.target_gender}
• **特征**: {features_str}
• **氛围**: {requirements.mood}

请确认以上需求是否正确？如需修改请直接说明，确认无误请回复"确认"开始生成设计。
        """.strip()

    async def _determine_modification_type(self, user_input: str) -> str:
        """智能判断修改类型：图片编辑 vs 设计重生成"""

        modification_prompt = f"""
        Determine whether the user's modification request should use "image_edit" or "design_regeneration".

        Guidelines:
        - image_edit: Visual adjustments (brightness, contrast, saturation, minor color changes)
        - design_regeneration: Structural changes (style, garment type, major design changes)

        User request: "{user_input}"

        Respond with ONLY: image_edit OR design_regeneration
        """

        try:
            response = await self.llm.ainvoke([HumanMessage(content=modification_prompt)])
            modification_type = response.content.strip().lower()

            if modification_type not in ["image_edit", "design_regeneration"]:
                logger.warning(f"Invalid modification type: {modification_type}, defaulting to design_regeneration")
                modification_type = "design_regeneration"

            return modification_type

        except Exception as e:
            logger.error(f"Error determining modification type: {e}")
            return "design_regeneration"

    async def _execute_image_edit(self, autonomous_agent, user_input: str, target_image: str, conversation_id: str, state: ConversationState) -> Dict:
        """执行图片编辑"""
        edit_context = {
            "current_image": target_image,
            "modification_request": user_input,
            "requirements": state.get("requirements", {}).__dict__ if hasattr(state.get("requirements", {}), '__dict__') else state.get("requirements", {})
        }

        return await autonomous_agent.autonomous_image_edit(
            user_input=user_input,
            edit_context=edit_context,
            conversation_id=conversation_id
        )

    async def _execute_design_regeneration(self, autonomous_agent, user_input: str, current_image: str, conversation_id: str, state: ConversationState) -> Dict:
        """执行设计重生成"""
        current_requirements = state.get("requirements", {})

        # 创建修改需求的结构化数据
        modification_requirements = current_requirements.__dict__ if hasattr(current_requirements, '__dict__') else current_requirements
        modification_requirements["modification_request"] = user_input
        modification_requirements["current_image"] = current_image

        return await autonomous_agent.autonomous_design_generation(
            user_input=f"修改设计：{user_input}",
            structured_requirements=modification_requirements,
            conversation_id=conversation_id
        )
