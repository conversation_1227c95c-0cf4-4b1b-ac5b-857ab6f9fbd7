"""
Design service implementations.
Follows Single Responsibility Principle - each service handles one domain.
"""

import logging
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from ..models.database import (
    ChatMessage as ChatMessageModel,
    DesignConcept as DesignConceptModel,
    DesignModification as DesignModificationModel,
    Conversation as ConversationModel,
)
from ..models.schemas import (
    ChatMessageCreate,
    ChatMessageResponse,
    DesignConceptCreate,
    DesignConceptResponse,
    DesignModificationCreate,
    DesignModificationResponse,
)
from .interfaces import (
    IChatService,
    IDesignConceptService,
    IDesignModificationService,
)

logger = logging.getLogger(__name__)





class DesignConceptService(IDesignConceptService):
    """Design concept service implementation."""
    
    def __init__(self, db_session: AsyncSession):
        """Initialize service with database session."""
        self.db = db_session
    
    async def create_concept(self, user_id: UUID, concept_data: DesignConceptCreate) -> DesignConceptResponse:
        """Create a new design concept."""
        try:
            db_concept = DesignConceptModel(
                conversation_id=str(concept_data.conversation_id) if concept_data.conversation_id else str(user_id),  # Use conversation_id or fallback to user_id
                request_id=str(concept_data.request_id) if concept_data.request_id else None,  # Convert UUID to string for SQLite
                image_url=concept_data.image_url,
                prompt=concept_data.prompt,
                ai_model=concept_data.ai_model,
                version=concept_data.version,
                ai_metadata=concept_data.ai_metadata,
            )
            
            self.db.add(db_concept)
            await self.db.commit()
            await self.db.refresh(db_concept)
            
            logger.info(f"Created design concept: {db_concept.id}")
            return DesignConceptResponse.model_validate(db_concept)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating design concept: {e}")
            raise
    
    async def get_concept_by_id(self, concept_id: UUID) -> Optional[DesignConceptResponse]:
        """Get concept by ID."""
        try:
            stmt = select(DesignConceptModel).where(DesignConceptModel.id == str(concept_id))  # Convert UUID to string for SQLite
            result = await self.db.execute(stmt)
            concept = result.scalar_one_or_none()
            
            if concept:
                return DesignConceptResponse.model_validate(concept)
            return None
            
        except Exception as e:
            logger.error(f"Error getting design concept {concept_id}: {e}")
            raise
    
    async def get_conversation_concepts(self, conversation_id: UUID, user_id: UUID, skip: int = 0, limit: int = 100) -> List[DesignConceptResponse]:
        """Get conversation's concepts."""
        try:
            stmt = (
                select(DesignConceptModel)
                .where(DesignConceptModel.conversation_id == str(conversation_id))  # Convert UUID to string for SQLite
                .offset(skip)
                .limit(limit)
                .order_by(DesignConceptModel.created_at.desc())
            )
            result = await self.db.execute(stmt)
            concepts = result.scalars().all()
            
            return [DesignConceptResponse.model_validate(concept) for concept in concepts]
            
        except Exception as e:
            logger.error(f"Error getting conversation concepts for {conversation_id}: {e}")
            raise
    
    async def set_active_concept(self, concept_id: UUID) -> bool:
        """Set concept as active."""
        try:
            # First, get the concept to find its conversation
            concept_stmt = select(DesignConceptModel).where(DesignConceptModel.id == str(concept_id))  # Convert UUID to string for SQLite
            concept_result = await self.db.execute(concept_stmt)
            concept = concept_result.scalar_one_or_none()
            
            if not concept:
                return False
            
            # Deactivate all concepts in the conversation
            deactivate_stmt = (
                select(DesignConceptModel)
                .where(DesignConceptModel.conversation_id == concept.conversation_id)
            )
            deactivate_result = await self.db.execute(deactivate_stmt)
            all_concepts = deactivate_result.scalars().all()
            
            for c in all_concepts:
                c.is_active = False
            
            # Activate the selected concept
            concept.is_active = True
            
            await self.db.commit()
            
            logger.info(f"Set active concept: {concept_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error setting active concept {concept_id}: {e}")
            raise
    
    async def delete_concept(self, concept_id: UUID) -> bool:
        """Delete concept."""
        try:
            stmt = select(DesignConceptModel).where(DesignConceptModel.id == str(concept_id))  # Convert UUID to string for SQLite
            result = await self.db.execute(stmt)
            concept = result.scalar_one_or_none()
            
            if not concept:
                return False
            
            await self.db.delete(concept)
            await self.db.commit()
            
            logger.info(f"Deleted concept: {concept_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting concept {concept_id}: {e}")
            raise


class ChatService(IChatService):
    """Chat service implementation."""
    
    def __init__(self, db_session: AsyncSession):
        """Initialize service with database session."""
        self.db = db_session
    
    async def create_message(self, conversation_id: UUID, message_data: ChatMessageCreate) -> ChatMessageResponse:
        """Create a new chat message."""
        try:
            conversation_id = str(conversation_id) if conversation_id else str(message_data.conversation_id)
            
            db_message = ChatMessageModel(
                conversation_id=conversation_id,
                role=message_data.role,
                content=message_data.content,
                image_url=message_data.image_url,
                message_metadata=message_data.message_metadata,
            )
            
            self.db.add(db_message)
            await self.db.commit()
            await self.db.refresh(db_message)
            
            logger.info(f"Created chat message: {db_message.id}")
            return ChatMessageResponse.model_validate(db_message)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating chat message: {e}")
            raise
    
    async def get_conversation_messages(self, conversation_id: UUID, skip: int = 0, limit: int = 100) -> List[ChatMessageResponse]:
        """Get conversation's chat messages."""
        try:
            stmt = (
                select(ChatMessageModel)
                .where(ChatMessageModel.conversation_id == str(conversation_id))  # Convert UUID to string for SQLite
                .offset(skip)
                .limit(limit)
                .order_by(ChatMessageModel.created_at.asc())
            )
            result = await self.db.execute(stmt)
            messages = result.scalars().all()
            
            return [ChatMessageResponse.model_validate(message) for message in messages]
            
        except Exception as e:
            logger.error(f"Error getting conversation messages for {conversation_id}: {e}")
            raise
    
    async def delete_message(self, message_id: UUID) -> bool:
        """Delete chat message."""
        try:
            stmt = select(ChatMessageModel).where(ChatMessageModel.id == str(message_id))  # Convert UUID to string for SQLite
            result = await self.db.execute(stmt)
            message = result.scalar_one_or_none()
            
            if not message:
                return False
            
            await self.db.delete(message)
            await self.db.commit()
            
            logger.info(f"Deleted message: {message_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting message {message_id}: {e}")
            raise
