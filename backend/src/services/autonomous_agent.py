"""
自主工具调用代理 - LLM自主决策的工具调用系统
实现基于Function Calling的智能工具调用，包括智能提示词生成和实时状态反馈
"""

import json
import logging
import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime
from http import HTTPStatus

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain_openai import ChatOpenAI

from ..core.config import settings
from .image_generation.config_manager import get_image_generation_manager
from .image_generation import (
    ImageGenerationRequest,
    ImageEditRequest,
    ImageGenerationProvider,
    ImageGenerationException
)

logger = logging.getLogger(__name__)


class AutonomousGenerationStatus:
    """自主生成状态管理"""
    
    def __init__(self):
        self.phase = "thinking"
        self.action = ""
        self.progress = 0
        self.current_tool = None
        self.iteration = 0
        self.total_tools_called = 0
        self.tools_used = 0
        self.tool_result_summary = None
        self.error = None
        self.start_time = datetime.now()


class AutonomousToolCallingAgent:
    """LLM自主工具调用代理"""
    
    def __init__(self):
        """初始化自主工具调用代理"""
        # 注册所有可用工具
        self.tools = self._register_all_tools()
        
        # 配置支持工具调用的LLM
        self.llm = ChatOpenAI(
            base_url=settings.qwen_base_url,
            api_key=settings.qwen_api_key,
            model=settings.qwen_model,
            temperature=0.7,
        ).bind_tools(self.tools)
        
        # WebSocket管理器
        from .websocket_manager import get_status_broadcaster
        self.status_broadcaster = get_status_broadcaster()
        
        logger.info(f"Registered {len(self.tools)} tools for autonomous calling")
    
    def _register_all_tools(self) -> List[Dict]:
        """注册所有可用工具"""
        return [
            # 智能提示词生成工具（合并了多个功能）
            {
                "type": "function",
                "function": {
                    "name": "generate_optimized_fashion_prompt",
                    "description": "基于结构化设计需求，生成高质量的时装设计图像提示词。整合风格研究、色彩搭配、面料建议和提示词优化功能",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "structured_requirements": {
                                "type": "object",
                                "description": "从需求确认阶段获得的结构化设计数据",
                                "properties": {
                                    "category": {"type": "string", "description": "服装类别"},
                                    "style": {"type": "string", "description": "设计风格"},
                                    "colors": {"type": "array", "items": {"type": "string"}, "description": "颜色列表"},
                                    "materials": {"type": "array", "items": {"type": "string"}, "description": "材质列表"},
                                    "occasion": {"type": "string", "description": "穿着场合"},
                                    "target_gender": {"type": "string", "description": "目标性别"},
                                    "key_features": {"type": "array", "items": {"type": "string"}, "description": "关键特征"},
                                    "mood": {"type": "string", "description": "整体感觉"}
                                },
                                "required": ["category", "style", "colors", "mood"]
                            },
                            "optimization_level": {
                                "type": "string", 
                                "enum": ["standard", "enhanced", "premium"], 
                                "default": "enhanced",
                                "description": "提示词优化级别"
                            },
                            "technical_style": {
                                "type": "string",
                                "enum": ["fashion_sketch", "technical_drawing", "artistic_illustration", "concept_art"],
                                "default": "fashion_sketch",
                                "description": "技术绘图风格"
                            },
                            "emphasis_aspects": {
                                "type": "array",
                                "items": {
                                    "type": "string",
                                    "enum": ["silhouette", "fabric_texture", "color_harmony", "design_details", "styling", "proportions"]
                                },
                                "description": "需要特别强调的设计方面"
                            }
                        },
                        "required": ["structured_requirements"]
                    }
                }
            },
            
            # 图像生成工具
            {
                "type": "function",
                "function": {
                    "name": "generate_fashion_image",
                    "description": "使用Flux模型生成高质量时装设计图像",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "optimized_prompt": {"type": "string", "description": "优化后的设计提示词"},
                            "image_size": {"type": "string", "enum": ["1024*1024", "1024*1536", "1536*1024"], "default": "1024*1024"},
                            "quality_preset": {"type": "string", "enum": ["draft", "standard", "high", "premium"], "default": "high"}
                        },
                        "required": ["optimized_prompt"]
                    }
                }
            },

            # 图片编辑工具
            {
                "type": "function",
                "function": {
                    "name": "edit_fashion_image",
                    "description": "使用AI编辑现有的时装设计图像，根据用户要求进行修改",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "base_image_url": {
                                "type": "string",
                                "description": "要编辑的原始图片URL"
                            },
                            "edit_prompt": {
                                "type": "string",
                                "description": "编辑指令，描述要如何修改图片"
                            },
                            "mask_image_url": {
                                "type": "string",
                                "description": "可选的遮罩图片URL，指定编辑区域",
                                "default": None
                            },
                            "negative_prompt": {
                                "type": "string",
                                "description": "负面提示词，描述不希望出现的内容",
                                "default": None
                            }
                        },
                        "required": ["base_image_url", "edit_prompt"]
                    }
                }
            }
        ]
    
    async def autonomous_design_generation(self, user_input: str, structured_requirements: Dict, conversation_id: str) -> Dict:
        """LLM自主决策的设计生成流程"""

        # 为这次生成创建唯一ID
        import uuid
        generation_id = str(uuid.uuid4())

        # 给LLM完整的上下文和工具权限
        system_prompt = """
        你是一位专业的时装设计AI助手，拥有多种专业工具。请根据用户需求和结构化设计数据，自主决定：
        1. 需要调用哪些工具
        2. 工具调用的顺序
        3. 每个工具的具体参数
        4. 是否需要多轮工具调用来完善结果

        可用工具包括：
        - generate_optimized_fashion_prompt: 生成优化的设计提示词（整合风格研究、色彩搭配、面料建议）
        - generate_fashion_image: 生成时装设计图像

        请根据需求智能选择和组合这些工具，创造出最佳的设计方案。
        """
        
        user_prompt = f"""
        用户需求：{user_input}
        
        结构化设计数据：{json.dumps(structured_requirements, ensure_ascii=False, indent=2)}
        
        请分析这个需求，然后自主决定调用合适的工具来完成设计任务。
        你可以：
        1. 对于新设计：先调用 generate_optimized_fashion_prompt 生成专业提示词，然后调用 generate_fashion_image 生成设计图像
        2. 对于图片编辑：直接调用 edit_fashion_image 编辑现有图像
        
        请开始执行，我会实时更新状态给用户。
        """
        
        # 移除思考过程状态推送，直接开始生成
        
        # LLM自主决策和执行
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        tool_call_history = []
        max_iterations = 10  # 防止无限循环
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            
            # LLM决策下一步行动
            response = await self.llm.ainvoke(messages)
            
            # 检查是否有工具调用
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行工具调用
                tool_results = []
                for tool_call in response.tool_calls:
                    result = await self._execute_autonomous_tool_call(
                        tool_call, conversation_id, iteration, tool_call_history, generation_id
                    )
                    tool_results.append(result)
                    tool_call_history.append({
                        "tool_call": tool_call,
                        "result": result
                    })

                # 将工具调用结果添加到对话历史
                messages.append(response)

                # 添加工具结果消息
                for i, tool_call in enumerate(response.tool_calls):
                    from langchain_core.messages import ToolMessage

                    # 获取tool_call_id
                    if hasattr(tool_call, 'id'):
                        tool_call_id = tool_call.id
                    elif isinstance(tool_call, dict):
                        tool_call_id = tool_call.get('id', f"call_{i}")
                    else:
                        tool_call_id = f"call_{i}"

                    messages.append(ToolMessage(
                        content=json.dumps(tool_results[i], ensure_ascii=False),
                        tool_call_id=tool_call_id
                    ))

            # 检查响应内容中是否包含工具调用（备用方案）
            elif hasattr(response, 'content') and 'tool_calls' in response.content.lower():
                logger.info("Detected potential tool calls in response content, but no structured tool_calls found")
                # 尝试解析内容中的工具调用
                try:
                    # 这里可以添加解析逻辑，但现在先跳过
                    pass
                except Exception as e:
                    logger.error(f"Failed to parse tool calls from content: {e}")
                
                # 如果是图像生成工具，可能就是最终结果
                if any(tc.function.name == "generate_fashion_image" for tc in response.tool_calls):
                    # 让LLM决定是否还需要其他工具
                    continue_prompt = "图像已生成。请评估是否需要调用其他工具来完善设计，或者可以结束了。如果满意当前结果，请直接回复完成状态。"
                    messages.append(HumanMessage(content=continue_prompt))
                
            else:
                # LLM认为任务完成，没有更多工具调用
                break
        
        # 整理最终结果
        final_result = self._compile_autonomous_results(tool_call_history)
        
        await self._send_autonomous_status(conversation_id, {
            "phase": "completed",
            "action": "设计生成完成！",
            "progress": 100,
            "tools_used": len(tool_call_history),
            "generation_id": generation_id
        })
        
        return final_result

    async def autonomous_image_edit(self, user_input: str, edit_context: Dict, conversation_id: str) -> Dict:
        """LLM自主决策的图片编辑流程"""

        # 为这次编辑创建唯一ID
        import uuid
        generation_id = str(uuid.uuid4())

        logger.info(f"=== 开始图片编辑流程 ===")
        logger.info(f"用户输入: {user_input}")
        logger.info(f"编辑上下文: {edit_context}")
        logger.info(f"对话ID: {conversation_id}")
        logger.info(f"生成ID: {generation_id}")

        # 给LLM完整的上下文和工具权限
        system_prompt = """
        你是一位专业的时装设计AI助手，现在需要编辑一张现有的设计图片。

        重要提示：这是图片编辑任务，不是生成新图片！

        你只能使用以下工具：
        - edit_fashion_image: 编辑现有的时装设计图像

        禁止使用：
        - generate_fashion_image: 这是生成新图片的工具，不适用于编辑任务
        - generate_optimized_fashion_prompt: 这是生成提示词的工具，编辑任务不需要

        请严格按照用户的编辑要求，只调用 edit_fashion_image 工具来完成图片编辑任务。
        """

        base_image_url = edit_context.get("base_image_url") or edit_context.get("current_image")
        original_design = edit_context.get("original_design", {})

        # 检查base_image_url是否是本地路径，如果是，尝试从metadata获取原始URL
        if base_image_url and base_image_url.startswith("static/"):
            logger.info(f"检测到本地路径，尝试获取原始URL: {base_image_url}")
            # 这里需要从设计历史或metadata中获取原始URL
            # 暂时使用本地路径，但这可能会导致编辑失败
            logger.warning(f"使用本地路径进行编辑可能会失败: {base_image_url}")

        logger.info(f"最终使用的图片URL: {base_image_url}")

        user_prompt = f"""
        用户要求编辑图片：{user_input}

        原始图片URL：{base_image_url}
        原始设计信息：{json.dumps(original_design, ensure_ascii=False, indent=2)}

        请立即调用 edit_fashion_image 工具，参数如下：
        - base_image_url: "{base_image_url}"
        - edit_prompt: "{user_input}"

        不要调用其他任何工具，只调用 edit_fashion_image 工具来完成这个编辑任务。
        """

        # 移除思考过程状态推送，直接开始编辑

        # LLM自主决策和执行 - 只提供编辑工具
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        # 为图片编辑创建专门的工具列表，只包含编辑工具
        edit_tools = [
            {
                "type": "function",
                "function": {
                    "name": "edit_fashion_image",
                    "description": "使用AI编辑现有的时装设计图像，根据用户要求进行修改",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "base_image_url": {
                                "type": "string",
                                "description": "要编辑的原始图片URL"
                            },
                            "edit_prompt": {
                                "type": "string",
                                "description": "编辑指令，描述要如何修改图片"
                            },
                            "mask_image_url": {
                                "type": "string",
                                "description": "可选的遮罩图片URL，指定编辑区域",
                                "default": None
                            },
                            "negative_prompt": {
                                "type": "string",
                                "description": "负面提示词，描述不希望出现的内容",
                                "default": None
                            }
                        },
                        "required": ["base_image_url", "edit_prompt"]
                    }
                }
            }
        ]

        tool_call_history = []
        max_iterations = 5  # 图片编辑通常只需要一次调用
        iteration = 0

        while iteration < max_iterations:
            iteration += 1

            # 移除思考状态推送

            # 为图片编辑创建专门的LLM实例，只绑定编辑工具
            edit_llm = ChatOpenAI(
                base_url=settings.qwen_base_url,
                api_key=settings.qwen_api_key,
                model=settings.qwen_model,
                temperature=0.7,
            ).bind_tools(edit_tools)

            # LLM决策下一步行动
            response = await edit_llm.ainvoke(messages)

            # 检查是否有工具调用
            if hasattr(response, 'tool_calls') and response.tool_calls:
                for tool_call in response.tool_calls:
                    # 执行工具调用
                    result = await self._execute_autonomous_tool_call(
                        tool_call, conversation_id, iteration, tool_call_history, generation_id
                    )

                    # 记录工具调用历史
                    tool_call_history.append({
                        "iteration": iteration,
                        "tool_call": tool_call,
                        "result": result
                    })

                    # 将工具结果添加到消息历史
                    messages.append(response)

                    # 安全地获取tool_call_id
                    if hasattr(tool_call, 'id'):
                        tool_call_id = tool_call.id
                    elif isinstance(tool_call, dict):
                        tool_call_id = tool_call.get('id', f"call_{len(tool_call_history)}")
                    else:
                        tool_call_id = f"call_{len(tool_call_history)}"

                    messages.append(ToolMessage(
                        content=json.dumps(result, ensure_ascii=False),
                        tool_call_id=tool_call_id
                    ))

                    # 如果是图片编辑成功，可以结束
                    if result.get("edit_success"):
                        break

                # 如果有成功的编辑结果，结束循环
                if any(entry["result"].get("edit_success") for entry in tool_call_history):
                    break
            else:
                # 没有工具调用，LLM可能认为任务完成或需要更多信息
                break

        # 发送完成状态
        await self._send_autonomous_status(conversation_id, {
            "phase": "completed",
            "action": "图片编辑完成",
            "progress": 100,
            "generation_id": generation_id
        })

        # 整理最终结果
        final_result = self._compile_autonomous_results(tool_call_history)
        final_result["generation_id"] = generation_id
        final_result["edit_type"] = "image_edit"

        return final_result

    async def _execute_autonomous_tool_call(
        self,
        tool_call,
        conversation_id: str,
        iteration: int,
        history: List,
        generation_id: str
    ) -> Any:
        """执行LLM自主决定的工具调用"""

        # 处理不同格式的tool_call
        if hasattr(tool_call, 'function'):
            # 标准格式
            tool_name = tool_call.function.name
            tool_args = json.loads(tool_call.function.arguments)
        elif isinstance(tool_call, dict):
            # 字典格式 - LangChain返回的格式
            tool_name = tool_call.get('name')
            tool_args = tool_call.get('args', {})

            # 如果args是字符串，尝试解析JSON
            if isinstance(tool_args, str):
                try:
                    tool_args = json.loads(tool_args)
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse tool arguments: {tool_args}")
                    tool_args = {}
        else:
            raise ValueError(f"Unsupported tool_call format: {type(tool_call)}")

        if not tool_name:
            raise ValueError(f"No tool name found in tool_call: {tool_call}")

        # 发送工具调用状态
        await self._send_autonomous_status(conversation_id, {
            "phase": "executing",
            "action": f"正在执行: {self._get_tool_display_name(tool_name)}",
            "progress": 20 + (iteration * 15),
            "current_tool": tool_name,
            "iteration": iteration,
            "total_tools_called": len(history),
            "generation_id": generation_id
        })

        try:
            # 根据工具名称执行对应功能
            if tool_name == "generate_optimized_fashion_prompt":
                result = await self._generate_optimized_fashion_prompt(**tool_args)
            elif tool_name == "generate_fashion_image":
                result = await self._generate_fashion_image(**tool_args, conversation_id=conversation_id)
            elif tool_name == "edit_fashion_image":
                result = await self._edit_fashion_image(**tool_args, conversation_id=conversation_id)
            else:
                raise ValueError(f"Unknown tool: {tool_name}")

            # 检查工具执行结果，如果是图像生成失败，发送错误状态
            if tool_name == "generate_fashion_image" and not result.get("generation_success"):
                await self._send_autonomous_status(conversation_id, {
                    "phase": "tool_error",
                    "action": f"工具执行失败: {self._get_tool_display_name(tool_name)}",
                    "current_tool": tool_name,
                    "error": result.get("error", "图像生成失败"),
                    "progress": 25 + (iteration * 15),
                    "generation_id": generation_id
                })
            else:
                # 发送工具完成状态
                await self._send_autonomous_status(conversation_id, {
                    "phase": "tool_completed",
                    "action": f"完成: {self._get_tool_display_name(tool_name)}",
                    "progress": 25 + (iteration * 15),
                    "current_tool": tool_name,
                    "tool_result_summary": self._summarize_tool_result(tool_name, result),
                    "generation_id": generation_id
                })

            return result

        except Exception as e:
            logger.error(f"Tool execution failed: {tool_name}, error: {e}")
            await self._send_autonomous_status(conversation_id, {
                "phase": "tool_error",
                "action": f"工具执行失败: {tool_name}",
                "current_tool": tool_name,
                "error": str(e),
                "generation_id": generation_id
            })
            raise

    def _get_tool_display_name(self, tool_name: str) -> str:
        """获取工具的友好显示名称"""
        display_names = {
            "generate_optimized_fashion_prompt": "规划设计步骤",
            "generate_fashion_image": "图像生成",
            "edit_fashion_image": "图像编辑"
        }
        return display_names.get(tool_name, tool_name)

    def _summarize_tool_result(self, tool_name: str, result: Dict) -> str:
        """生成工具结果摘要"""
        if tool_name == "generate_optimized_fashion_prompt":
            return f"生成了{result.get('prompt_quality_score', 0.8)*100:.0f}%质量的设计步骤"
        elif tool_name == "generate_fashion_image":
            if result.get("generation_success"):
                return "成功生成高质量时装设计图像"
            else:
                return f"图像生成失败: {result.get('error', '未知错误')}"
        elif tool_name == "edit_fashion_image":
            if result.get("edit_success"):
                return "成功编辑时装设计图像"
            else:
                return f"图像编辑失败: {result.get('error', '未知错误')}"
        return "工具执行完成"

    async def _send_autonomous_status(self, conversation_id: str, status: Dict):
        """发送自主生成状态更新"""
        # 添加时间戳
        from datetime import datetime
        status["timestamp"] = datetime.now().isoformat()

        logger.info(f"Status update for {conversation_id}: {status}")

        # 通过WebSocket发送状态更新
        try:
            if self.status_broadcaster:
                await self.status_broadcaster.send_status_update(conversation_id, status)
            else:
                logger.warning("Status broadcaster not available, skipping status update")
        except Exception as e:
            logger.error(f"Failed to send status update: {e}")

    def _compile_autonomous_results(self, tool_call_history: List) -> Dict:
        """整理自主工具调用的最终结果"""

        final_result = {
            "tool_call_history": tool_call_history,
            "tools_used": len(tool_call_history),
            "generation_method": "autonomous_llm_calling"
        }

        # 提取关键结果
        for entry in tool_call_history:
            tool_call = entry["tool_call"]
            result = entry["result"]

            # 获取工具名称
            if hasattr(tool_call, 'function'):
                tool_name = tool_call.function.name
            elif isinstance(tool_call, dict):
                tool_name = tool_call.get('name')
            else:
                tool_name = "unknown"

            if tool_name == "generate_optimized_fashion_prompt":
                final_result["optimized_prompt"] = result.get("optimized_prompt")
                final_result["style_research"] = result.get("style_research")
                final_result["color_analysis"] = result.get("color_analysis")
                final_result["fabric_recommendations"] = result.get("fabric_recommendations")
            elif tool_name == "generate_fashion_image":
                final_result["image_url"] = result.get("image_url")
                final_result["generation_success"] = result.get("generation_success")
                final_result["metadata"] = result.get("metadata", {})
            elif tool_name == "edit_fashion_image":
                final_result["image_url"] = result.get("image_url")
                final_result["edit_success"] = result.get("edit_success")
                final_result["original_image_url"] = result.get("original_image_url")

        return final_result

    # ==================== 工具实现方法 ====================

    async def _generate_optimized_fashion_prompt(
        self,
        structured_requirements: Dict,
        optimization_level: str = "enhanced",
        technical_style: str = "fashion_sketch",
        emphasis_aspects: List[str] = None
    ) -> Dict:
        """
        智能生成优化的时装设计提示词
        整合风格研究、色彩理论、面料知识和专业提示词工程
        """

        try:
            # 1. 提取基础信息
            category = structured_requirements.get("category", "clothing")
            style = structured_requirements.get("style", "modern")
            colors = structured_requirements.get("colors", ["neutral"])
            materials = structured_requirements.get("materials", ["fabric"])
            occasion = structured_requirements.get("occasion", "casual")
            mood = structured_requirements.get("mood", "stylish")
            key_features = structured_requirements.get("key_features", [])
            target_gender = structured_requirements.get("target_gender", "unisex")

            # 2. 智能风格研究和增强
            style_enhancement = await self._research_and_enhance_style(
                style, category, mood, optimization_level
            )

            # 3. 智能色彩搭配优化
            color_palette = await self._generate_intelligent_color_scheme(
                colors, mood, style, occasion
            )

            # 4. 智能面料建议
            fabric_suggestions = await self._suggest_intelligent_fabrics(
                category, style, occasion, materials
            )

            # 5. 使用混合方案生成专业提示词
            optimized_prompt = await self._build_professional_prompt_with_llm(
                structured_requirements=structured_requirements,
                style_enhancement=style_enhancement,
                color_palette=color_palette,
                fabric_suggestions=fabric_suggestions,
                technical_style=technical_style,
                optimization_level=optimization_level,
                emphasis_aspects=emphasis_aspects or []
            )

            return {
                "optimized_prompt": optimized_prompt,
                "generation_method": "llm_with_code_enhancement",
                "style_research": style_enhancement,
                "color_analysis": color_palette,
                "fabric_recommendations": fabric_suggestions,
                "technical_specifications": {
                    "style": technical_style,
                    "emphasis": emphasis_aspects,
                    "optimization_level": optimization_level
                },
                "prompt_quality_score": self._calculate_prompt_quality(optimized_prompt)
            }

        except Exception as e:
            logger.error(f"Error in generate_optimized_fashion_prompt: {e}")
            return {
                "error": str(e),
                "fallback_prompt": self._generate_fallback_prompt(structured_requirements)
            }

    async def _research_and_enhance_style(
        self,
        style: str,
        category: str,
        mood: str,
        optimization_level: str
    ) -> Dict:
        """智能风格研究和增强"""

        # 风格知识库
        style_database = {
            "minimalist": {
                "characteristics": ["clean lines", "simple silhouettes", "neutral colors", "functional design"],
                "key_elements": ["geometric shapes", "negative space", "quality fabrics"],
                "avoid": ["excessive details", "busy patterns", "multiple colors"]
            },
            "vintage": {
                "characteristics": ["retro silhouettes", "classic patterns", "nostalgic elements"],
                "key_elements": ["period-appropriate details", "traditional craftsmanship", "timeless appeal"],
                "avoid": ["modern synthetic materials", "contemporary cuts"]
            },
            "streetwear": {
                "characteristics": ["urban influence", "casual comfort", "bold graphics", "layering"],
                "key_elements": ["oversized fits", "logo elements", "mixed textures"],
                "avoid": ["formal elements", "delicate fabrics"]
            },
            "bohemian": {
                "characteristics": ["flowing fabrics", "earthy colors", "artistic patterns", "free-spirited"],
                "key_elements": ["natural textures", "layered accessories", "ethnic influences"],
                "avoid": ["structured tailoring", "corporate aesthetics"]
            },
            "modern": {
                "characteristics": ["contemporary design", "clean aesthetics", "innovative materials"],
                "key_elements": ["current trends", "versatile styling", "functional beauty"],
                "avoid": ["outdated elements", "overly traditional details"]
            }
        }

        base_style_info = style_database.get(style.lower(), style_database["modern"])

        # 根据服装类别调整风格特征
        category_adjustments = {
            "dress": {"emphasis": ["silhouette", "draping", "length"]},
            "shirt": {"emphasis": ["collar design", "fit", "sleeve details"]},
            "pants": {"emphasis": ["cut", "rise", "leg shape"]},
            "jacket": {"emphasis": ["structure", "lapels", "proportions"]},
            "skirt": {"emphasis": ["length", "silhouette", "waistline"]},
            "top": {"emphasis": ["neckline", "fit", "sleeve style"]}
        }

        category_specific = category_adjustments.get(category, {"emphasis": ["overall design"]})

        return {
            "style_name": style,
            "characteristics": base_style_info["characteristics"],
            "key_elements": base_style_info["key_elements"],
            "category_emphasis": category_specific["emphasis"],
            "mood_alignment": mood,
            "enhancement_level": optimization_level
        }

    async def _generate_intelligent_color_scheme(
        self,
        base_colors: List[str],
        mood: str,
        style: str,
        occasion: str
    ) -> Dict:
        """智能色彩搭配生成"""

        # 色彩心理学映射
        mood_color_mapping = {
            "elegant": ["navy", "charcoal", "cream", "burgundy"],
            "fresh": ["mint", "coral", "sky blue", "white"],
            "bold": ["red", "black", "gold", "royal blue"],
            "calm": ["sage", "beige", "soft gray", "lavender"],
            "energetic": ["orange", "yellow", "bright green", "magenta"],
            "stylish": ["black", "white", "gray", "camel"],
            "romantic": ["blush pink", "soft lavender", "cream", "rose gold"],
            "professional": ["navy", "charcoal", "white", "light blue"]
        }

        # 场合色彩适配
        occasion_colors = {
            "business": ["navy", "charcoal", "white", "light blue"],
            "casual": ["denim", "khaki", "white", "earth tones"],
            "evening": ["black", "deep jewel tones", "metallic accents"],
            "summer": ["light colors", "pastels", "bright accents"],
            "daily": ["versatile neutrals", "soft colors", "classic tones"]
        }

        # 智能色彩组合
        suggested_colors = mood_color_mapping.get(mood, base_colors)
        occasion_appropriate = occasion_colors.get(occasion, base_colors)

        # 合并和优化色彩方案
        final_palette = list(set(base_colors + suggested_colors[:2] + occasion_appropriate[:2]))

        return {
            "primary_colors": base_colors,
            "suggested_additions": suggested_colors,
            "occasion_appropriate": occasion_appropriate,
            "final_palette": final_palette[:5],  # 限制为5种颜色
            "color_harmony": "complementary" if len(final_palette) <= 3 else "analogous"
        }

    async def _suggest_intelligent_fabrics(
        self,
        category: str,
        style: str,
        occasion: str,
        existing_materials: List[str]
    ) -> Dict:
        """智能面料建议"""

        # 服装类别面料映射
        category_fabrics = {
            "dress": ["silk", "chiffon", "crepe", "jersey", "cotton"],
            "shirt": ["cotton", "linen", "silk", "poplin", "chambray"],
            "pants": ["wool", "cotton", "denim", "twill", "stretch fabric"],
            "jacket": ["wool", "tweed", "cotton canvas", "leather", "denim"],
            "skirt": ["wool", "cotton", "silk", "denim", "knit"],
            "top": ["cotton", "silk", "jersey", "linen", "modal"]
        }

        # 风格面料偏好
        style_fabrics = {
            "minimalist": ["high-quality cotton", "wool crepe", "silk", "linen"],
            "vintage": ["tweed", "velvet", "brocade", "cotton sateen"],
            "streetwear": ["cotton jersey", "denim", "fleece", "nylon"],
            "bohemian": ["cotton gauze", "silk chiffon", "linen", "embroidered fabrics"],
            "modern": ["technical fabrics", "cotton blends", "sustainable materials"]
        }

        # 场合面料适配
        occasion_fabrics = {
            "business": ["wool", "cotton", "silk", "high-quality blends"],
            "casual": ["cotton", "denim", "jersey", "linen"],
            "evening": ["silk", "satin", "velvet", "lace"],
            "athletic": ["moisture-wicking", "stretch fabrics", "breathable materials"],
            "daily": ["comfortable cotton", "easy-care fabrics", "durable materials"]
        }

        suggested_fabrics = (
            category_fabrics.get(category, []) +
            style_fabrics.get(style, []) +
            occasion_fabrics.get(occasion, [])
        )

        # 去重并优先保留现有材料
        final_fabrics = existing_materials + [f for f in suggested_fabrics if f not in existing_materials]

        return {
            "existing_materials": existing_materials,
            "category_suggestions": category_fabrics.get(category, []),
            "style_appropriate": style_fabrics.get(style, []),
            "occasion_suitable": occasion_fabrics.get(occasion, []),
            "final_recommendations": final_fabrics[:4]  # 限制为4种面料
        }

    async def _build_professional_prompt_with_llm(
        self,
        structured_requirements: Dict,
        style_enhancement: Dict,
        color_palette: Dict,
        fabric_suggestions: Dict,
        technical_style: str,
        optimization_level: str,
        emphasis_aspects: List[str]
    ) -> str:
        """使用LLM生成创意提示词，然后代码后处理"""

        try:
            # 1. LLM生成创意部分
            creative_prompt = await self._generate_creative_prompt_with_llm(
                structured_requirements, style_enhancement, color_palette, fabric_suggestions
            )

            # 2. 代码后处理，确保技术规格
            final_prompt = self._post_process_prompt(
                creative_prompt, technical_style, optimization_level
            )

            return final_prompt

        except Exception as e:
            logger.error(f"LLM prompt generation failed: {e}")
            # 降级到代码拼接方案
            return self._generate_fallback_prompt(structured_requirements)

    async def _generate_creative_prompt_with_llm(
        self,
        structured_requirements: Dict,
        style_enhancement: Dict,
        color_palette: Dict,
        fabric_suggestions: Dict
    ) -> str:
        """LLM生成创意部分"""

        design_summary = self._create_design_summary(structured_requirements)

        creative_instruction = f"""
        基于以下专业分析，创作一个富有创意且专业的时装设计提示词：

        ## 设计概要
        {design_summary}

        ## 风格分析
        - 风格特征: {', '.join(style_enhancement.get('characteristics', []))}
        - 关键元素: {', '.join(style_enhancement.get('key_elements', []))}
        - 类别重点: {', '.join(style_enhancement.get('category_emphasis', []))}

        ## 色彩方案
        - 主要色彩: {', '.join(color_palette.get('primary_colors', []))}
        - 最终调色板: {', '.join(color_palette.get('final_palette', []))}
        - 色彩和谐: {color_palette.get('color_harmony')}

        ## 面料建议
        - 推荐面料: {', '.join(fabric_suggestions.get('final_recommendations', []))}

        请生成一个创意、专业、适合Flux模型的提示词。重点：
        1. 语言自然流畅，避免生硬拼接
        2. 突出设计独特性和专业水准
        3. 体现时尚设计的艺术性
        4. 适合图像生成模型理解

        只返回提示词内容，不要其他解释。长度控制在50-100词。
        """

        response = await self.llm.ainvoke([HumanMessage(content=creative_instruction)])
        return response.content.strip()

    def _create_design_summary(self, requirements: Dict) -> str:
        """创建设计概要"""
        category = requirements.get("category", "服装")
        style = requirements.get("style", "现代")
        colors = requirements.get("colors", [])
        occasion = requirements.get("occasion", "日常")
        mood = requirements.get("mood", "时尚")
        target_gender = requirements.get("target_gender", "通用")

        summary = f"设计一件{style}风格的{category}，适合{target_gender}，用于{occasion}场合"
        if colors:
            summary += f"，主要色彩为{', '.join(colors[:3])}"
        summary += f"，整体感觉{mood}"

        return summary

    def _post_process_prompt(
        self,
        creative_prompt: str,
        technical_style: str,
        optimization_level: str
    ) -> str:
        """代码后处理，确保技术规格"""

        # 技术规格映射
        technical_specs = {
            "fashion_sketch": "fashion illustration style, clean line art, professional sketch",
            "technical_drawing": "technical fashion drawing, flat lay design, precise details",
            "artistic_illustration": "artistic fashion illustration, watercolor style, creative presentation",
            "concept_art": "fashion concept art, digital illustration, modern presentation"
        }

        quality_specs = {
            "standard": "good quality, clear presentation",
            "enhanced": "high quality, professional presentation, detailed design",
            "premium": "ultra-high quality, professional designer presentation, award-winning design"
        }

        # 确保包含必要的技术规格
        tech_spec = technical_specs.get(technical_style, "professional fashion illustration")
        quality_spec = quality_specs.get(optimization_level, "high quality")

        # 智能插入技术规格（如果不存在）
        if "white background" not in creative_prompt.lower():
            creative_prompt += ", white background"

        if not any(tech_word in creative_prompt.lower() for tech_word in ["illustration", "sketch", "drawing"]):
            creative_prompt += f", {tech_spec}"

        if not any(quality_word in creative_prompt.lower() for quality_word in ["quality", "professional"]):
            creative_prompt += f", {quality_spec}"

        return creative_prompt

    def _generate_fallback_prompt(self, requirements: Dict) -> str:
        """生成备用提示词（代码拼接方式）"""
        category = requirements.get("category", "clothing")
        style = requirements.get("style", "modern")
        colors = ", ".join(requirements.get("colors", ["neutral"])[:3])

        return f"{category} design concept, {style} style, color palette: {colors}, fashion illustration style, white background, high quality, professional presentation"

    def _calculate_prompt_quality(self, prompt: str) -> float:
        """计算提示词质量分数"""
        quality_score = 0.5  # 基础分数

        # 检查长度
        if 30 <= len(prompt.split()) <= 100:
            quality_score += 0.2

        # 检查关键词
        quality_keywords = ["fashion", "design", "style", "professional", "quality", "illustration"]
        for keyword in quality_keywords:
            if keyword in prompt.lower():
                quality_score += 0.05

        # 检查技术规格
        if "white background" in prompt.lower():
            quality_score += 0.1

        return min(quality_score, 1.0)

    async def _generate_fashion_image(
        self,
        optimized_prompt: str,
        image_size: str = "1024*1024",
        quality_preset: str = "high",
        conversation_id: str = None,
        provider: Optional[str] = None
    ) -> Dict:
        """
        使用配置的文生图服务生成时装设计图像

        Args:
            optimized_prompt: 优化后的设计提示词
            image_size: 图像尺寸 (1024*1024, 1024*1536, 1536*1024)
            quality_preset: 质量预设 (draft, standard, high, premium)
            conversation_id: 对话ID
            provider: 指定的服务提供商 (可选)

        Returns:
            Dict: 生成结果
        """

        try:
            logger.info(f"Generating fashion image with prompt: {optimized_prompt}")

            # 获取文生图服务管理器
            manager = get_image_generation_manager()

            # 解析图像尺寸
            if "*" in image_size:
                width, height = map(int, image_size.split("*"))
            else:
                # 默认尺寸
                width, height = 1024, 1024

            # 创建生成请求
            request = ImageGenerationRequest(
                prompt=optimized_prompt,
                width=width,
                height=height,
                num_images=1,
                quality=quality_preset
            )

            # 获取服务实例
            if provider:
                # 如果指定了提供商，尝试使用指定的提供商
                try:
                    provider_enum = ImageGenerationProvider(provider.lower())
                    service = manager.get_service(provider_enum)
                except (ValueError, KeyError):
                    logger.warning(f"Invalid provider '{provider}', using default")
                    service = manager.get_service()
            else:
                service = manager.get_service()

            logger.info(f"Using provider: {service.provider.value}")

            # 生成图像
            result = await service.generate_image(request)

            if result.success and result.image_urls:
                image_url = result.image_urls[0]
                logger.info(f"Successfully generated image: {image_url}")

                # 安全地获取provider值
                provider_value = "unknown"
                if hasattr(result.provider, 'value'):
                    provider_value = result.provider.value
                elif isinstance(result.provider, str):
                    provider_value = result.provider

                return {
                    "image_url": image_url,
                    "prompt_used": optimized_prompt,
                    "generation_success": True,
                    "provider": provider_value,
                    "generation_id": result.generation_id,
                    "parameters": {
                        "size": image_size,
                        "quality_preset": quality_preset,
                        "generation_time": result.generation_time
                    },
                    "metadata": result.metadata
                }
            else:
                error_msg = result.error_message or "Unknown generation error"
                logger.error(f'Image generation failed: {error_msg}')
                # 安全地获取provider值
                provider_value = "unknown"
                if result.provider:
                    if hasattr(result.provider, 'value'):
                        provider_value = result.provider.value
                    elif isinstance(result.provider, str):
                        provider_value = result.provider

                return {
                    "generation_success": False,
                    "error": error_msg,
                    "provider": provider_value,
                    "fallback_url": "/api/placeholder/400/600"
                }

        except ImageGenerationException as e:
            logger.error(f"Image generation service error: {e}")
            # 安全地获取provider值
            provider_value = "unknown"
            if hasattr(e, 'provider') and e.provider:
                if hasattr(e.provider, 'value'):
                    provider_value = e.provider.value
                elif isinstance(e.provider, str):
                    provider_value = e.provider

            return {
                "generation_success": False,
                "error": f"Service error: {str(e)}",
                "provider": provider_value,
                "fallback_url": "/api/placeholder/400/600"
            }
        except Exception as e:
            logger.error(f"Unexpected error generating fashion image: {e}")
            return {
                "generation_success": False,
                "error": str(e),
                "fallback_url": "/api/placeholder/400/600"
            }

    async def _edit_fashion_image(
        self,
        base_image_url: str,
        edit_prompt: str,
        mask_image_url: str = None,
        negative_prompt: str = None,
        conversation_id: str = None,
        provider: Optional[str] = None
    ) -> Dict:
        """
        编辑时装设计图像 - 优化版本，支持多个图像提供商

        Args:
            base_image_url: 要编辑的原始图片URL
            edit_prompt: 编辑指令
            mask_image_url: 可选的遮罩图片URL
            negative_prompt: 负面提示词
            conversation_id: 对话ID，用于状态更新
            provider: 指定的服务提供商 (可选)

        Returns:
            Dict: 编辑结果
        """
        try:
            logger.info(f"Starting image edit: {edit_prompt[:50]}...")

            # 发送开始编辑状态
            if conversation_id:
                await self._send_autonomous_status(conversation_id, {
                    "phase": "image_editing",
                    "action": f"正在编辑图像: {edit_prompt[:30]}...",
                    "progress": 10
                })

            # 获取文生图服务管理器
            manager = get_image_generation_manager()

            # 创建编辑请求
            edit_request = ImageEditRequest(
                base_image_url=base_image_url,
                prompt=edit_prompt,
                mask_image_url=mask_image_url,
                negative_prompt=negative_prompt,
                guidance_scale=3.5
            )

            # 获取服务实例
            if provider:
                # 如果指定了提供商，尝试使用指定的提供商
                try:
                    provider_enum = ImageGenerationProvider(provider.lower())
                    service = manager.get_service(provider_enum)
                except (ValueError, KeyError):
                    logger.warning(f"Invalid provider '{provider}', using default")
                    service = manager.get_service()
            else:
                service = manager.get_service()

            logger.info(f"Using provider: {service.provider.value}")

            # 发送编辑进度状态
            if conversation_id:
                await self._send_autonomous_status(conversation_id, {
                    "phase": "image_editing",
                    "action": f"正在使用{service.provider.value}引擎编辑图像...",
                    "progress": 30
                })

            # 执行图片编辑，带重试机制
            max_retries = 3
            edit_result = None
            last_error = None

            for attempt in range(max_retries):
                try:
                    logger.info(f"Image edit attempt {attempt + 1}/{max_retries}")
                    if conversation_id and attempt > 0:
                        await self._send_autonomous_status(conversation_id, {
                            "phase": "image_editing",
                            "action": f"图像编辑重试中 (第{attempt + 1}次尝试)...",
                            "progress": 30 + (attempt * 20)
                        })

                    edit_result = await service.edit_image(edit_request)

                    # 检查编辑是否成功
                    if edit_result.success and edit_result.image_url:
                        logger.info(f"Image edit succeeded on attempt {attempt + 1}")
                        break
                    elif edit_result.success and not edit_result.image_url:
                        # 编辑成功但没有图片URL - 这是一个特殊情况
                        last_error = "Edit succeeded but no image URL returned"
                        logger.warning(f"Image edit attempt {attempt + 1}: {last_error}")
                        logger.warning(f"Edit result details: success={edit_result.success}, image_url={edit_result.image_url}, error={edit_result.error_message}")

                        # 如果不是最后一次尝试，继续重试
                        if attempt < max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                    else:
                        last_error = edit_result.error_message or "Unknown edit error"
                        logger.warning(f"Image edit attempt {attempt + 1} failed: {last_error}")

                        # 如果不是最后一次尝试，继续重试
                        if attempt < max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue

                except Exception as e:
                    last_error = str(e)
                    logger.error(f"Image edit attempt {attempt + 1} error: {last_error}")

                    # 如果不是最后一次尝试，继续重试
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 ** attempt)  # 指数退避
                        continue
                    else:
                        # 最后一次尝试也失败了
                        edit_result = None
                        break

            if edit_result and edit_result.success and edit_result.image_url:
                # 编辑成功
                if conversation_id:
                    await self._send_autonomous_status(conversation_id, {
                        "phase": "image_editing",
                        "action": "图像编辑完成，正在保存结果...",
                        "progress": 90
                    })

                logger.info(f"Image edit completed successfully: {edit_result.image_url}")

                # 安全地获取provider值
                provider_value = "unknown"
                if hasattr(edit_result.provider, 'value'):
                    provider_value = edit_result.provider.value
                elif isinstance(edit_result.provider, str):
                    provider_value = edit_result.provider

                return {
                    "edit_success": True,
                    "image_url": edit_result.image_url,
                    "original_image_url": base_image_url,
                    "edit_prompt": edit_prompt,
                    "generation_time": edit_result.generation_time,
                    "metadata": edit_result.metadata,
                    "provider": provider_value,
                    "message": f"成功根据「{edit_prompt}」编辑了图像"
                }
            else:
                # 编辑失败（重试3次后仍然失败）
                error_msg = last_error or (edit_result.error_message if edit_result else "未知错误")
                logger.error(f"Image edit failed after {max_retries} attempts: {error_msg}")

                # 发送失败状态
                if conversation_id:
                    await self._send_autonomous_status(conversation_id, {
                        "phase": "image_editing",
                        "action": f"图像编辑失败 (已重试{max_retries}次)",
                        "progress": 0,
                        "error": True
                    })

                # 安全地获取provider值
                provider_value = "unknown"
                if edit_result and edit_result.provider:
                    if hasattr(edit_result.provider, 'value'):
                        provider_value = edit_result.provider.value
                    elif isinstance(edit_result.provider, str):
                        provider_value = edit_result.provider

                return {
                    "edit_success": False,
                    "error": error_msg,
                    "original_image_url": base_image_url,
                    "edit_prompt": edit_prompt,
                    "provider": provider_value,
                    "message": f"图像编辑失败 (重试{max_retries}次): {error_msg}"
                }

        except ImageGenerationException as e:
            logger.error(f"Image editing service error: {e}")
            # 安全地获取provider值
            provider_value = "unknown"
            if hasattr(e, 'provider') and e.provider:
                if hasattr(e.provider, 'value'):
                    provider_value = e.provider.value
                elif isinstance(e.provider, str):
                    provider_value = e.provider

            return {
                "edit_success": False,
                "error": f"Service error: {str(e)}",
                "original_image_url": base_image_url,
                "edit_prompt": edit_prompt,
                "provider": provider_value,
                "message": f"图像编辑服务错误: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Unexpected error editing fashion image: {e}")
            return {
                "edit_success": False,
                "error": str(e),
                "original_image_url": base_image_url,
                "edit_prompt": edit_prompt,
                "message": f"图像编辑过程中出现错误: {str(e)}"
            }

    async def _process_edit_result(
        self,
        edit_result,
        original_request: dict,
        conversation_id: str = None
    ) -> dict:
        """
        处理图像编辑结果

        Args:
            edit_result: 编辑结果对象
            original_request: 原始请求参数
            conversation_id: 对话ID

        Returns:
            dict: 处理后的结果
        """
        try:
            if edit_result.success and edit_result.image_url:
                # 编辑成功
                if conversation_id:
                    await self._send_autonomous_status(conversation_id, {
                        "phase": "image_editing",
                        "action": "图像编辑完成，正在保存结果...",
                        "progress": 90
                    })

                logger.info(f"Image edit completed successfully: {edit_result.image_url}")

                return {
                    "edit_success": True,
                    "image_url": edit_result.image_url,
                    "original_image_url": original_request["base_image_url"],
                    "edit_prompt": original_request["edit_prompt"],
                    "generation_time": edit_result.generation_time,
                    "metadata": edit_result.metadata,
                    "provider": edit_result.provider.value,
                    "message": f"成功根据「{original_request['edit_prompt']}」编辑了图像"
                }
            else:
                # 编辑失败
                error_msg = edit_result.error_message or "未知错误"
                logger.error(f"Image edit failed: {error_msg}")

                return {
                    "edit_success": False,
                    "error": error_msg,
                    "original_image_url": original_request["base_image_url"],
                    "edit_prompt": original_request["edit_prompt"],
                    "provider": edit_result.provider.value if edit_result.provider else "unknown",
                    "message": f"图像编辑失败: {error_msg}"
                }

        except Exception as e:
            logger.error(f"Error processing edit result: {e}")
            return await self._handle_edit_exception(
                error=e,
                base_image_url=original_request["base_image_url"],
                edit_prompt=original_request["edit_prompt"],
                conversation_id=conversation_id
            )

    async def _handle_edit_exception(
        self,
        error: Exception,
        base_image_url: str,
        edit_prompt: str,
        conversation_id: str = None
    ) -> dict:
        """
        处理图像编辑异常

        Args:
            error: 异常对象
            base_image_url: 基础图像URL
            edit_prompt: 编辑提示词
            conversation_id: 对话ID

        Returns:
            dict: 错误结果
        """
        error_message = str(error)
        logger.error(f"Image editing exception: {error_message}")

        if conversation_id:
            await self._send_autonomous_status(conversation_id, {
                "phase": "image_editing",
                "action": f"图像编辑失败: {error_message[:30]}...",
                "progress": 0,
                "error": True
            })

        return {
            "edit_success": False,
            "error": error_message,
            "original_image_url": base_image_url,
            "edit_prompt": edit_prompt,
            "message": f"图像编辑过程中出现错误: {error_message}"
        }

    async def _get_image_edit_service(self):
        """
        获取可用的图像编辑服务

        Returns:
            ImageGenerationService: 支持图像编辑的服务实例
        """
        try:
            from .image_generation.config_manager import get_image_generation_manager
            from .image_generation.base import ImageGenerationProvider

            manager = get_image_generation_manager()
            available_providers = manager.get_available_providers()

            # 按优先级尝试获取支持图像编辑的服务
            preferred_providers = [
                ImageGenerationProvider.DASHSCOPE,
                ImageGenerationProvider.LIBLIB_KONTEXT
            ]

            for provider in preferred_providers:
                if provider in available_providers:
                    service = manager.get_service(provider)
                    if service and service.supports_image_editing():
                        logger.info(f"Using {provider.value} for image editing")
                        return service

            # 如果没有找到支持编辑的服务，尝试所有可用服务
            for provider in available_providers:
                service = manager.get_service(provider)
                if service and service.supports_image_editing():
                    logger.info(f"Fallback to {provider.value} for image editing")
                    return service

            logger.error("No image editing service available")
            return None

        except Exception as e:
            logger.error(f"Error getting image edit service: {e}")
            return None

    async def _create_edit_request(
        self,
        base_image_url: str,
        edit_prompt: str,
        mask_image_url: str = None,
        negative_prompt: str = None,
        service_provider = None
    ):
        """
        创建统一的图像编辑请求

        Args:
            base_image_url: 基础图像URL
            edit_prompt: 编辑提示词
            mask_image_url: 遮罩图像URL
            negative_prompt: 负面提示词
            service_provider: 服务提供商

        Returns:
            ImageEditRequest: 统一的编辑请求对象
        """
        try:
            from .image_generation.base import ImageEditRequest, ImageGenerationProvider

            # 根据不同提供商设置特定参数
            additional_params = {}
            model = None

            if service_provider == ImageGenerationProvider.DASHSCOPE:
                model = "wanx2.1-imageedit"
            elif service_provider == ImageGenerationProvider.LIBLIB_KONTEXT:
                model = "pro"
                # Liblib特定参数可以通过additional_params传递
                additional_params = {
                    "aspect_ratio": "2:3",  # 默认比例
                }

            return ImageEditRequest(
                base_image_url=base_image_url,
                prompt=edit_prompt,
                mask_image_url=mask_image_url,
                negative_prompt=negative_prompt,
                model=model,
                guidance_scale=3.5,  # 默认引导强度
                additional_params=additional_params
            )

        except Exception as e:
            logger.error(f"Error creating edit request: {e}")
            raise
