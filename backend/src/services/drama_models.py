"""
Shared models for drama production.
This module contains shared data models used by both drama agents and workflows.
"""

from typing import List, Dict, Any, Optional
from pydantic import Field, BaseModel
from enum import Enum

from .base_agents import BaseRequirements


class DramaGenre(str, Enum):
    """Drama genre enumeration."""
    ROMANCE = "爱情"
    ACTION = "动作"
    COMEDY = "喜剧"
    DRAMA = "剧情"
    THRILLER = "惊悚"
    HORROR = "恐怖"
    SCIFI = "科幻"
    FANTASY = "奇幻"
    DOCUMENTARY = "纪录片"
    ANIMATION = "动画"


class TargetAudience(str, Enum):
    """Target audience enumeration."""
    CHILDREN = "儿童"
    TEENAGERS = "青少年"
    ADULTS = "成人"
    FAMILY = "家庭"
    GENERAL = "大众"


class ProductionScale(str, Enum):
    """Production scale enumeration."""
    SMALL = "小制作"
    MEDIUM = "中等制作"
    LARGE = "大制作"
    BLOCKBUSTER = "大片"


class DramaRequirements(BaseModel):
    """
    Drama-specific production requirements.
    Shared between drama agents and workflows.
    """
    # Base requirements fields
    category: str = Field(default="drama", description="Design category")
    style: str = Field(default="剧情", description="Design style")
    colors: List[str] = Field(default_factory=list, description="Color scheme")
    mood: str = Field(default="成长", description="Overall mood")
    # Basic information
    genre: DramaGenre = Field(default=DramaGenre.DRAMA, description="Drama genre")
    target_audience: TargetAudience = Field(default=TargetAudience.ADULTS, description="Target audience")
    duration: int = Field(default=5, description="Target duration in minutes")
    budget: str = Field(default="中等", description="Production budget level")
    production_scale: ProductionScale = Field(default=ProductionScale.MEDIUM, description="Production scale")
    
    # Content requirements
    setting: str = Field(default="现代", description="Story setting/time period")
    theme: str = Field(default="人生", description="Main theme or message")
    main_character_count: int = Field(default=2, description="Number of main characters")
    supporting_character_count: int = Field(default=3, description="Number of supporting characters")
    scene_count: int = Field(default=5, description="Number of scenes")
    
    # Style requirements
    visual_style: str = Field(default="写实", description="Visual style preference")
    dialogue_style: str = Field(default="自然", description="Dialogue style preference")
    narrative_style: str = Field(default="线性", description="Narrative structure style")
    
    # Production requirements
    music_style: str = Field(default="配乐", description="Music style preference")
    special_effects: str = Field(default="基础", description="Special effects level")
    filming_locations: List[str] = Field(default_factory=list, description="Preferred filming locations")
    
    # Additional specifications
    inspiration: Optional[str] = Field(default=None, description="Inspiration or reference works")
    cultural_context: Optional[str] = Field(default=None, description="Cultural context requirements")
    language: str = Field(default="中文", description="Primary language")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "category": self.category,
            "style": self.style,
            "colors": self.colors,
            "mood": self.mood,
            "genre": self.genre.value,
            "target_audience": self.target_audience.value,
            "duration": self.duration,
            "budget": self.budget,
            "production_scale": self.production_scale.value,
            "setting": self.setting,
            "theme": self.theme,
            "main_character_count": self.main_character_count,
            "supporting_character_count": self.supporting_character_count,
            "scene_count": self.scene_count,
            "visual_style": self.visual_style,
            "dialogue_style": self.dialogue_style,
            "narrative_style": self.narrative_style,
            "music_style": self.music_style,
            "special_effects": self.special_effects,
            "filming_locations": self.filming_locations,
            "inspiration": self.inspiration,
            "cultural_context": self.cultural_context,
            "language": self.language,
        }


class DramaArtifact(BaseModel):
    """Drama production artifact model."""
    artifact_type: str
    title: str
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: str = Field(default_factory=lambda: "2025-01-01T00:00:00Z")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "artifact_type": self.artifact_type,
            "title": self.title,
            "content": self.content,
            "metadata": self.metadata,
            "created_at": self.created_at
        }


class DramaProductionPhase(str, Enum):
    """Drama production phase enumeration."""
    SCRIPT = "script"
    CHARACTER_DESIGN = "character_design"
    SCENE_PLANNING = "scene_planning"
    VIDEO_PRODUCTION = "video_production"
    COMPREHENSIVE = "comprehensive"


class ProductionState(BaseModel):
    """Drama production state model."""
    phase: DramaProductionPhase
    requirements: DramaRequirements
    artifacts: List[DramaArtifact] = Field(default_factory=list)
    current_step: int = Field(default=0)
    total_steps: int = Field(default=1)
    is_complete: bool = Field(default=False)
    
    def add_artifact(self, artifact: DramaArtifact) -> None:
        """Add an artifact to the production state."""
        self.artifacts.append(artifact)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "phase": self.phase.value,
            "requirements": self.requirements.to_dict(),
            "artifacts": [artifact.to_dict() for artifact in self.artifacts],
            "current_step": self.current_step,
            "total_steps": self.total_steps,
            "is_complete": self.is_complete
        }