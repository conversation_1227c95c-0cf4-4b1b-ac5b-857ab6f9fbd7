"""
Fashion Design Agent - Concrete implementation of BaseDesignAgent for fashion design.

This module implements the fashion-specific design agent that inherits from BaseDesignAgent
and provides specialized functionality for fashion design workflows.
"""

import json
import logging
import time
from typing import Dict, Any, List, Optional, Type
from uuid import uuid4
from datetime import datetime

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import MemorySaver

from .base_agents import (
    BaseDesignAgent, BaseRequirements, AgentTask, AgentResult, 
    SharedContext, DesignArtifact, TaskStatus
)
from .workflow import (
    DesignWorkflow, FashionWorkflow, DesignRequirements, ConversationState,
    IntentType, WorkflowState
)
from ..core.config import settings

logger = logging.getLogger(__name__)


# ============================================================================
# Fashion-Specific Requirements
# ============================================================================

class FashionRequirements(BaseRequirements):
    """Fashion-specific design requirements."""
    materials: List[str]        # 面料材质
    occasion: str              # 穿着场合
    target_gender: str         # 目标性别
    key_features: List[str]    # 关键特征


# ============================================================================
# Fashion Design Agent Implementation
# ============================================================================

class FashionDesignAgent(BaseDesignAgent):
    """Fashion design agent with multi-agent collaboration capabilities."""
    
    def __init__(self, agent_id: Optional[str] = None):
        # Fashion-specific capabilities (initialize before super().__init__)
        self.domain_expertise = [
            "garment_design", "fabric_selection", "color_coordination",
            "style_analysis", "trend_forecasting", "fit_optimization"
        ]

        super().__init__(agent_id, "FashionDesignAgent")

        # Initialize the fashion workflow with self as design agent
        self.workflow_engine = FashionWorkflow(design_agent=self)
    
    def get_capabilities(self) -> List[str]:
        """Return fashion design capabilities."""
        base_capabilities = [
            "conversation_processing", "requirement_extraction",
            "design_generation", "design_modification", "collaborative_design"
        ]
        return base_capabilities + self.domain_expertise
    
    def get_requirements_schema(self) -> Type[BaseRequirements]:
        """Return fashion requirements schema."""
        return FashionRequirements
    
    async def process_conversation(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Process user conversation using the original workflow."""
        
        # Delegate to the original workflow for backward compatibility
        return await self.workflow_engine.process_conversation(
            user_input=user_input,
            conversation_id=conversation_id,
            user_id=user_id,
            **kwargs
        )
    
    async def execute_collaborative_task(
        self,
        task: AgentTask,
        shared_context: SharedContext
    ) -> AgentResult:
        """Execute a collaborative task."""
        
        start_time = time.time()
        task_id = task["id"]
        
        try:
            logger.info(f"Fashion agent executing task: {task['description']}")

            # 直接调用workflow，不需要复杂的任务类型判断
            requirements = task["requirements"]
            user_input = requirements.get("user_input", task["description"])
            conversation_id = shared_context["conversation_id"]

            # 直接中转到workflow
            result = await self.workflow_engine.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=requirements.get("user_id", "collaborative_user"),
                message_type="user"
            )

            execution_time = time.time() - start_time

            # 提取artifacts从设计结果中
            artifacts = []

            # 方案1: 从result中直接提取artifacts
            if isinstance(result, dict) and "artifacts" in result:
                artifacts = result["artifacts"]

            # 方案2: 从result中提取image_url创建artifacts
            elif isinstance(result, dict) and result.get("image_url"):
                from uuid import uuid4
                artifact = {
                    "id": str(uuid4()),
                    "type": "fashion",
                    "image_url": result["image_url"],
                    "description": "时装设计",
                    "metadata": {
                        "task_type": "fashion_design",
                        "agent_type": self.agent_type,
                        "created_by": self.agent_id,
                        "design_prompt": result.get("design_prompt", result.get("optimized_prompt", ""))
                    }
                }
                artifacts.append(artifact)
                logger.info(f"Created fashion artifact from result: {artifact['id']} with image: {result['image_url']}")

            # 方案3: 从workflow结果中提取
            elif isinstance(result, dict) and "workflow_result" in result:
                workflow_result = result["workflow_result"]
                if isinstance(workflow_result, dict) and workflow_result.get("image_url"):
                    from uuid import uuid4
                    artifact = {
                        "id": str(uuid4()),
                        "type": "fashion",
                        "image_url": workflow_result["image_url"],
                        "description": "时装设计",
                        "metadata": {
                            "task_type": "fashion_design",
                            "agent_type": self.agent_type,
                            "created_by": self.agent_id,
                            "design_prompt": workflow_result.get("design_prompt", "")
                        }
                    }
                    artifacts.append(artifact)
                    logger.info(f"Created fashion artifact from workflow_result: {artifact['id']} with image: {workflow_result['image_url']}")

            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                output=result,
                artifacts=artifacts,
                execution_time=execution_time,
                error_message=None,
                metadata={
                    "task_type": "design_generation",  # 简化为固定值
                    "agent_type": self.agent_type
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Fashion agent task execution failed: {e}")
            
            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                output={},
                artifacts=[],
                execution_time=execution_time,
                error_message=str(e),
                metadata={
                    "task_type": requirements.get("task_type", "unknown"),
                    "agent_type": self.agent_type
                }
            )

    async def generate_design(
        self,
        user_input: str,
        requirements: DesignRequirements,
        conversation_id: str,
        user_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        # 追踪传入的数据
        logger.info(f"=== Fashion Design Agent Debug ===")
        logger.info(f"user_input: {user_input}")
        logger.info(f"requirements type: {type(requirements)}")
        logger.info(f"requirements value: {requirements}")
        logger.info(f"conversation_id: {conversation_id}")
        logger.info(f"==================================")
        """服装设计生成的核心逻辑"""

        try:
            logger.info(f"FashionDesignAgent generating design for conversation: {conversation_id}")

            # 使用自主工具调用代理
            from .autonomous_agent import AutonomousToolCallingAgent
            autonomous_agent = AutonomousToolCallingAgent()

            # LLM自主决策工具调用生成新设计
            from dataclasses import asdict
            result = await autonomous_agent.autonomous_design_generation(
                user_input=user_input,
                structured_requirements=asdict(requirements),
                conversation_id=conversation_id
            )

            logger.info(f"Fashion design generation completed: {result.get('image_url')}")
            return result

        except Exception as e:
            logger.error(f"Fashion design generation failed: {e}")
            return {
                "error": str(e),
                "image_url": None,
                "optimized_prompt": None,
                "current_state": "error"
            }