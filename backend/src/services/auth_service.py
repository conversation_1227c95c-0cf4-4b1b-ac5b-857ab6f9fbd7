"""
Authentication service implementation.
Follows Single Responsibility Principle - handles only authentication.
"""

import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from uuid import UUID

from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import settings
from ..models.schemas import UserResponse
from .interfaces import IAuthService
from .user_service import UserService

logger = logging.getLogger(__name__)


class AuthService(IAuthService):
    """Authentication service implementation."""
    
    def __init__(self, db_session: AsyncSession):
        """Initialize auth service."""
        self.db = db_session
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.user_service = UserService(db_session)
    
    async def authenticate_user(self, email: str, password: str) -> Optional[UserResponse]:
        """Authenticate user with email and password."""
        try:
            # Get user model for password verification
            user_model = await self.user_service.get_user_model_by_email(email)
            if not user_model:
                return None
            
            # Verify password
            if not self.verify_password(password, user_model.password_hash):
                return None
            
            # Check if user is active
            if not user_model.is_active:
                return None
            
            # Return user response
            return UserResponse.model_validate(user_model)
            
        except Exception as e:
            logger.error(f"Error authenticating user {email}: {e}")
            return None
    
    async def create_access_token(self, user_id: UUID) -> str:
        """Create access token for user."""
        try:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
            to_encode = {
                "sub": str(user_id),
                "exp": expire,
                "type": "access"
            }
            
            encoded_jwt = jwt.encode(
                to_encode,
                settings.secret_key,
                algorithm=settings.algorithm
            )
            
            return encoded_jwt
            
        except Exception as e:
            logger.error(f"Error creating access token for user {user_id}: {e}")
            raise
    
    async def verify_token(self, token: str) -> Optional[UUID]:
        """Verify access token and return user ID."""
        try:
            payload = jwt.decode(
                token,
                settings.secret_key,
                algorithms=[settings.algorithm]
            )
            
            user_id_str: str = payload.get("sub")
            token_type: str = payload.get("type")

            if user_id_str is None or token_type != "access":
                return None

            # Handle UUID format - ensure it has dashes
            if len(user_id_str) == 32 and '-' not in user_id_str:
                # Convert from compact format to standard UUID format
                user_id_str = f"{user_id_str[:8]}-{user_id_str[8:12]}-{user_id_str[12:16]}-{user_id_str[16:20]}-{user_id_str[20:]}"

            user_id = UUID(user_id_str)
            
            # Verify user still exists and is active
            user = await self.user_service.get_user_by_id(user_id)
            if not user or not user.is_active:
                return None
            
            return user_id
            
        except (JWTError, ValueError) as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying token: {e}")
            return None
    
    def hash_password(self, password: str) -> str:
        """Hash password."""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
