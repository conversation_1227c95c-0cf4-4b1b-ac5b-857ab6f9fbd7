"""
Poster Design Agent - Specialized agent for poster and graphic design.

This module implements a poster design agent that uses PosterWorkflow
for conversation processing and provides specialized poster design capabilities.
"""

import logging
import time
from typing import Dict, Any, List, Optional, Type
from uuid import uuid4

from langchain_openai import ChatOpenAI

from .base_agents import BaseDesignAgent, BaseRequirements, AgentTask, AgentResult, SharedContext, TaskStatus
from ..core.config import settings

logger = logging.getLogger(__name__)


class PosterRequirements(BaseRequirements):
    """Poster-specific design requirements."""
    dimensions: str = "A4"           # 尺寸规格
    text_content: str = ""           # 文字内容
    brand_elements: List[str] = []   # 品牌元素
    layout_preference: str = "现代"   # 布局偏好
    purpose: str = "宣传推广"         # 用途


class PosterDesignAgent(BaseDesignAgent):
    """
    Poster design agent with specialized poster design capabilities.

    Uses PosterWorkflow for conversation processing and provides
    poster-specific design generation and modification functionality.
    """

    def __init__(self, agent_id: Optional[str] = None):
        # Poster-specific capabilities
        self.domain_expertise = [
            "layout_design", "typography_hierarchy", "visual_composition",
            "brand_integration", "color_harmony", "information_architecture"
        ]

        super().__init__(agent_id, "PosterDesignAgent")

        # Initialize LLM for poster design
        self.llm = ChatOpenAI(
            base_url=settings.qwen_base_url,
            api_key=settings.qwen_api_key,
            model=settings.qwen_model,
            temperature=0.7,
        )

        # Initialize the poster workflow with self as design agent
        from .workflow import PosterWorkflow
        self.workflow_engine = PosterWorkflow(design_agent=self)

    def get_capabilities(self) -> List[str]:
        """Return poster design capabilities."""
        base_capabilities = [
            "conversation_processing", "requirement_extraction",
            "design_generation", "design_modification", "collaborative_design"
        ]
        return base_capabilities + self.domain_expertise

    def get_requirements_schema(self) -> Type[BaseRequirements]:
        """Return poster requirements schema."""
        return PosterRequirements

    async def process_conversation(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Process user conversation for poster design using PosterWorkflow."""

        try:
            logger.info(f"PosterDesignAgent processing conversation: {conversation_id}")

            # 使用海报工作流处理对话
            result = await self.workflow_engine.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                **kwargs
            )

            # 添加海报设计特定的标识
            result["design_type"] = "poster"
            result["agent_type"] = self.agent_type

            return result

        except Exception as e:
            logger.error(f"Poster design conversation failed: {e}")
            return {
                "error": str(e),
                "agent_type": self.agent_type,
                "conversation_id": conversation_id,
                "design_type": "poster"
            }
    async def execute_collaborative_task(
        self,
        task: AgentTask,
        shared_context: SharedContext
    ) -> AgentResult:
        """Execute collaborative poster design tasks."""

        task_id = task["id"]
        start_time = time.time()

        try:
            task_type = task.get("type", "poster_design")
            requirements = task.get("requirements", {})

            logger.info(f"Executing poster task: {task_type}")

            # PosterDesignAgent 只处理海报设计相关的任务
            if task_type == "poster_design":
                result = await self._execute_poster_design_task(task, shared_context)
            else:
                # 其他任务类型不是海报Agent的职责，返回不支持的任务类型
                logger.warning(f"PosterDesignAgent不支持任务类型: {task_type}")
                result = {
                    "supported": False,
                    "message": f"PosterDesignAgent只处理poster_design任务，不支持{task_type}",
                    "suggested_agent": self._suggest_appropriate_agent(task_type)
                }

            execution_time = time.time() - start_time

            # 提取artifacts从设计结果中
            artifacts = []

            # 方案1: 从design_result中提取
            if isinstance(result, dict) and "design_result" in result:
                design_result = result["design_result"]
                if isinstance(design_result, dict) and "artifacts" in design_result:
                    artifacts = design_result["artifacts"]
                elif isinstance(design_result, dict) and "images" in design_result:
                    # 如果有images，转换为artifacts格式
                    for img in design_result["images"]:
                        if isinstance(img, dict):
                            artifact = {
                                "id": img.get("id", str(uuid4())),
                                "type": "poster",
                                "image_url": img.get("url", img.get("image_url")),
                                "description": img.get("description", "海报设计"),
                                "metadata": {
                                    "task_type": "poster_design",
                                    "agent_type": self.agent_type,
                                    "created_by": self.agent_id
                                }
                            }
                            artifacts.append(artifact)
                # 方案2: 从design_result直接提取image_url
                elif isinstance(design_result, dict) and design_result.get("image_url"):
                    artifact = {
                        "id": str(uuid4()),
                        "type": "poster",
                        "image_url": design_result["image_url"],
                        "description": "海报设计",
                        "metadata": {
                            "task_type": "poster_design",
                            "agent_type": self.agent_type,
                            "created_by": self.agent_id,
                            "design_prompt": design_result.get("design_prompt", "")
                        }
                    }
                    artifacts.append(artifact)
                    logger.info(f"Created poster artifact from design_result: {artifact['id']} with image: {design_result['image_url']}")

            # 方案3: 直接从result中提取image_url（workflow直接返回的情况）
            if not artifacts and isinstance(result, dict) and result.get("image_url"):
                artifact = {
                    "id": str(uuid4()),
                    "type": "poster",
                    "image_url": result["image_url"],
                    "description": "海报设计",
                    "metadata": {
                        "task_type": "poster_design",
                        "agent_type": self.agent_type,
                        "created_by": self.agent_id,
                        "design_prompt": result.get("design_prompt", result.get("optimized_prompt", ""))
                    }
                }
                artifacts.append(artifact)
                logger.info(f"Created poster artifact from result: {artifact['id']} with image: {result['image_url']}")

            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                output=result,
                artifacts=artifacts,
                execution_time=execution_time,
                metadata={
                    "task_type": task_type,
                    "agent_type": self.agent_type,
                    "poster_category": requirements.get("category", "general")
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Poster collaborative task failed: {e}")

            return AgentResult(
                task_id=task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                output={},
                artifacts=[],
                execution_time=execution_time,
                error_message=str(e),
                metadata={
                    "task_type": task.get("type", "unknown"),
                    "agent_type": self.agent_type
                }
            )

    # ============================================================================
    # 海报设计专用方法
    # ============================================================================

    async def _execute_poster_design_task(
        self,
        task: AgentTask,
        shared_context: SharedContext
    ) -> Dict[str, Any]:
        """执行海报设计任务 - PosterDesignAgent的核心职责"""

        requirements = task.get("requirements", {})
        user_input = requirements.get("user_input", "")

        logger.info(f"PosterAgent executing poster design task: {user_input}")

        try:
            # 从requirements中移除user_input，避免参数重复
            filtered_requirements = {k: v for k, v in requirements.items() if k != "user_input"}

            # 检查shared_context中是否有可用的设计artifacts（如Logo设计结果）
            shared_artifacts = shared_context.get("shared_artifacts", {})
            logo_artifacts = []

            # 查找Logo设计相关的artifacts
            for artifact_id, artifact in shared_artifacts.items():
                if (artifact.get("type") == "logo" or
                    "logo" in artifact.get("metadata", {}).get("task_type", "").lower() or
                    "logo" in artifact.get("description", "").lower()):
                    logo_artifacts.append(artifact)
                    logger.info(f"Found logo artifact for poster design: {artifact_id}")

            # 如果有Logo设计结果，将其添加到海报设计的上下文中
            if logo_artifacts:
                filtered_requirements["logo_artifacts"] = logo_artifacts
                filtered_requirements["has_logo_reference"] = True
                # 构建包含Logo信息的用户输入
                logo_info = f"\n\n重要：请在海报设计中使用已设计的Logo。"
                if len(logo_artifacts) > 0:
                    logo_artifact = logo_artifacts[0]  # 使用第一个Logo
                    if "image_url" in logo_artifact:
                        logo_info += f"Logo图片地址：{logo_artifact['image_url']}"
                    if "description" in logo_artifact:
                        logo_info += f"Logo描述：{logo_artifact['description']}"

                user_input = f"{user_input}{logo_info}"
                logger.info(f"Enhanced poster design request with {len(logo_artifacts)} logo artifacts")

            # 使用海报工作流引擎处理设计任务
            conversation_id = shared_context.get("conversation_id")
            logger.info(f"PosterAgent using conversation_id: {conversation_id}")

            design_result = await self.workflow_engine.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=shared_context.get("user_id"),
                **filtered_requirements
            )

            return {
                "poster_designed": True,
                "design_result": design_result,
                "confidence": 0.92,
                "task_type": "poster_design",
                "agent_contribution": "专业海报设计创建",
                "specialization": "海报设计专家"
            }

        except Exception as e:
            logger.error(f"Poster design task failed: {e}")
            return {
                "poster_designed": False,
                "error": str(e),
                "confidence": 0.0,
                "task_type": "poster_design"
            }

    def _suggest_appropriate_agent(self, task_type: str) -> str:
        """根据任务类型建议合适的Agent"""

        agent_suggestions = {
            "layout_optimization": "LayoutDesignAgent",
            "brand_integration": "BrandDesignAgent",
            "visual_enhancement": "VisualEffectsAgent",
            "content_review": "ContentReviewAgent",
            "typography": "TypographyAgent",
            "color_design": "ColorDesignAgent",
            "image_editing": "ImageEditingAgent"
        }

        return agent_suggestions.get(task_type, "GeneralDesignAgent")
