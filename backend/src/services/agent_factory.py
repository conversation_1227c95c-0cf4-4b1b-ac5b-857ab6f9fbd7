"""
Agent Factory and Routing System.

This module implements the factory pattern for creating design agents and
the routing system for directing requests to appropriate agents or agent collaborations.
"""

import logging
from typing import Dict, Any, List, Optional, Type
from uuid import uuid4

from .base_agents import (
    BaseDesignAgent, DesignType, CollaborationType,
    AgentTask, SharedContext
)
from .fashion_design_agent import FashionDesignAgent
from .poster_design_agent import PosterDesignAgent
from .logo_design_agent import LogoDesignAgent
from .drama_production_agent import DramaProductionAgent
from .expert_agents import ColorExpertAgent, TrendAnalysisAgent, TypographyAgent
from .collaboration_core import (
    TaskDecomposer, AgentCoordinator, ResultIntegrator, CollaborationOrchestrator
)
from .agent_communication import CommunicationManager
from .workflow.fashion_workflow import FashionWorkflow

logger = logging.getLogger(__name__)


# ============================================================================
# Agent Factory
# ============================================================================

class DesignAgentFactory:
    """Factory for creating design agents."""
    
    # Registry of available agent types
    _agent_registry: Dict[str, Type[BaseDesignAgent]] = {
        "FashionDesignAgent": FashionDesignAgent,
        "PosterDesignAgent": PosterDesignAgent,
        "LogoDesignAgent": LogoDesignAgent,
        "DramaProductionAgent": DramaProductionAgent,
        "ColorExpertAgent": ColorExpertAgent,
        "TrendAnalysisAgent": TrendAnalysisAgent,
        "TypographyAgent": TypographyAgent,
    }
    
    # Design type to primary agent mapping
    _design_type_mapping: Dict[DesignType, str] = {
        DesignType.FASHION: "FashionDesignAgent",
        DesignType.POSTER: "PosterDesignAgent",
        DesignType.LOGO: "LogoDesignAgent",
        DesignType.DRAMA: "DramaProductionAgent",
        # Future mappings:
        # DesignType.UI: "UIDesignAgent",
    }
    
    @classmethod
    def create_agent(
        cls,
        agent_type: str,
        agent_id: Optional[str] = None
    ) -> Optional[BaseDesignAgent]:
        """Create an agent instance by type."""
        
        agent_class = cls._agent_registry.get(agent_type)
        if not agent_class:
            logger.error(f"Unknown agent type: {agent_type}")
            return None
        
        try:
            agent = agent_class(agent_id)
            logger.info(f"Created agent: {agent_type} with ID: {agent.agent_id}")
            return agent
        except Exception as e:
            logger.error(f"Failed to create agent {agent_type}: {e}")
            return None
    
    @classmethod
    def create_agent_by_design_type(
        cls,
        design_type: DesignType,
        agent_id: Optional[str] = None
    ) -> Optional[BaseDesignAgent]:
        """Create primary agent for a design type."""
        
        agent_type = cls._design_type_mapping.get(design_type)
        if not agent_type:
            logger.error(f"No primary agent for design type: {design_type}")
            return None
        
        return cls.create_agent(agent_type, agent_id)
    
    @classmethod
    def create_agent_pool(
        cls,
        agent_types: List[str]
    ) -> Dict[str, BaseDesignAgent]:
        """Create a pool of agents."""
        
        agent_pool = {}
        for agent_type in agent_types:
            agent = cls.create_agent(agent_type)
            if agent:
                agent_pool[agent_type] = agent
        
        logger.info(f"Created agent pool with {len(agent_pool)} agents")
        return agent_pool
    
    @classmethod
    def register_agent_type(
        cls,
        agent_type: str,
        agent_class: Type[BaseDesignAgent]
    ):
        """Register a new agent type."""
        
        cls._agent_registry[agent_type] = agent_class
        logger.info(f"Registered new agent type: {agent_type}")
    
    @classmethod
    def get_available_agent_types(cls) -> List[str]:
        """Get list of available agent types."""
        return list(cls._agent_registry.keys())
    
    @classmethod
    def get_agent_capabilities(cls, agent_type: str) -> List[str]:
        """Get capabilities of an agent type."""
        
        agent_class = cls._agent_registry.get(agent_type)
        if not agent_class:
            return []
        
        # Create temporary instance to get capabilities
        try:
            temp_agent = agent_class()
            return temp_agent.get_capabilities()
        except Exception as e:
            logger.error(f"Failed to get capabilities for {agent_type}: {e}")
            return []


# ============================================================================
# Agent Router
# ============================================================================

class AgentRouter:
    """Routes requests to appropriate agents or agent collaborations."""
    
    def __init__(self):
        self.factory = DesignAgentFactory()
        self.communication_manager = CommunicationManager()
        self.active_agents: Dict[str, BaseDesignAgent] = {}
        self.collaboration_orchestrator = None

        # 先初始化默认代理，然后创建工作流
        self._initialize_default_agents()

        # 获取FashionDesignAgent作为默认设计代理
        fashion_agent = self.active_agents.get("FashionDesignAgent")
        self.fashion_workflow = FashionWorkflow(design_agent=fashion_agent)
    
    def _initialize_default_agents(self):
        """Initialize default set of agents."""
        
        default_agent_types = [
            "FashionDesignAgent",
            "PosterDesignAgent",
            "LogoDesignAgent",
            "DramaProductionAgent",
            "ColorExpertAgent",
            "TrendAnalysisAgent",
            "TypographyAgent"
        ]
        
        self.active_agents = self.factory.create_agent_pool(default_agent_types)
        
        # Initialize collaboration orchestrator
        if self.active_agents:
            self.collaboration_orchestrator = CollaborationOrchestrator(
                agent_pool=self.active_agents,
                communication_manager=self.communication_manager
            )
    
    async def route_single_agent_request(
        self,
        design_type: DesignType,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Route request to a single agent."""
        
        # Get primary agent for design type
        agent = self._get_or_create_primary_agent(design_type)
        if not agent:
            return {
                "error": f"No agent available for design type: {design_type}",
                "success": False
            }
        
        try:
            result = await agent.process_conversation(
                user_input=user_input,
                conversation_id=conversation_id,
                user_id=user_id,
                **kwargs
            )
            
            # Add routing metadata to the result
            result["agent_type"] = agent.agent_type
            result["agent_id"] = agent.agent_id
            result["routing_type"] = "single_agent"

            # Return in API-compatible format
            return {
                "success": True,
                "data": result,
                "agent_type": agent.agent_type,
                "routing_type": "single_agent"
            }
            
        except Exception as e:
            logger.error(f"Single agent request failed: {e}")
            return {
                "error": str(e),
                "agent_type": agent.agent_type,
                "success": False
            }
    
    async def route_collaborative_request(
        self,
        user_input: str,
        collaboration_type: CollaborationType,
        conversation_id: str,
        user_id: str,
        required_agents: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Route request to multi-agent collaboration."""
        
        if not self.collaboration_orchestrator:
            return {
                "error": "Collaboration orchestrator not available",
                "success": False
            }
        
        try:
            # Determine required agents if not specified
            if not required_agents:
                required_agents = await self._determine_required_agents(
                    user_input, collaboration_type
                )
            
            # Ensure required agents are available
            available_agents = self._ensure_agents_available(required_agents)
            
            if not available_agents:
                return {
                    "error": "Required agents not available",
                    "success": False
                }
            
            # Execute collaboration
            result = await self.collaboration_orchestrator.orchestrate_collaboration(
                user_request=user_input,
                collaboration_type=collaboration_type,
                conversation_id=conversation_id,
                user_id=user_id,
                required_agents=available_agents,
                **kwargs
            )
            
            result["routing_type"] = "collaborative"
            result["success"] = True
            
            return result
            
        except Exception as e:
            logger.error(f"Collaborative request failed: {e}")
            return {
                "error": str(e),
                "collaboration_type": collaboration_type,
                "success": False
            }
    
    async def route_expert_consultation(
        self,
        consultation_request: str,
        expert_type: str,
        conversation_id: str,
        user_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Route request to expert agent for consultation."""
        
        expert_agent = self.active_agents.get(expert_type)
        if not expert_agent:
            # Try to create the expert agent
            expert_agent = self.factory.create_agent(expert_type)
            if expert_agent:
                self.active_agents[expert_type] = expert_agent
        
        if not expert_agent:
            return {
                "error": f"Expert agent {expert_type} not available",
                "success": False
            }
        
        try:
            advice = await expert_agent.provide_expert_advice(
                request={
                    "consultation_request": consultation_request,
                    "context": context
                }
            )
            
            return {
                "expert_advice": advice,
                "expert_type": expert_type,
                "expert_id": expert_agent.agent_id,
                "routing_type": "expert_consultation",
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Expert consultation failed: {e}")
            return {
                "error": str(e),
                "expert_type": expert_type,
                "success": False
            }
    
    def _get_or_create_primary_agent(
        self,
        design_type: DesignType
    ) -> Optional[BaseDesignAgent]:
        """Get or create primary agent for design type."""
        
        # Check if we already have the primary agent
        primary_agent_type = self.factory._design_type_mapping.get(design_type)
        if primary_agent_type and primary_agent_type in self.active_agents:
            return self.active_agents[primary_agent_type]
        
        # Create new primary agent
        agent = self.factory.create_agent_by_design_type(design_type)
        if agent and primary_agent_type:
            self.active_agents[primary_agent_type] = agent
        
        return agent
    
    async def _determine_required_agents(
        self,
        user_input: str,
        collaboration_type: CollaborationType
    ) -> List[str]:
        """Determine which agents are needed for the request."""

        # 智能确定主要Agent类型
        required_agents = []
        user_input_lower = user_input.lower()

        # 确定主要设计Agent
        if any(keyword in user_input_lower for keyword in ["logo", "标识", "品牌标志", "商标", "标志"]):
            required_agents.append("LogoDesignAgent")
        elif any(keyword in user_input_lower for keyword in ["海报", "宣传", "广告", "poster"]):
            required_agents.append("PosterDesignAgent")
        elif any(keyword in user_input_lower for keyword in ["服装", "衣服", "裙子", "上衣", "裤子", "外套", "时装"]):
            required_agents.append("FashionDesignAgent")
        elif any(keyword in user_input_lower for keyword in ["剧本", "剧情", "电影", "短片", "视频", "拍摄", "导演", "角色", "场景", "故事", "情节", "戏剧", "话剧"]):
            required_agents.append("DramaProductionAgent")
        else:
            # 默认使用FashionDesignAgent
            required_agents.append("FashionDesignAgent")

        # 添加专家Agent
        if any(keyword in user_input_lower for keyword in ["颜色", "色彩", "配色"]):
            required_agents.append("ColorExpertAgent")

        if any(keyword in user_input_lower for keyword in ["趋势", "流行", "潮流"]):
            required_agents.append("TrendAnalysisAgent")

        if any(keyword in user_input_lower for keyword in ["字体", "文字", "排版"]):
            required_agents.append("TypographyAgent")

        # For peer review, include multiple design agents
        if collaboration_type == CollaborationType.PEER_REVIEW:
            required_agents.extend(["ColorExpertAgent", "TrendAnalysisAgent"])

        return list(set(required_agents))  # Remove duplicates

    def _detect_direct_agent_request(self, user_input: str) -> Optional[str]:
        """检测是否为明确的单Agent请求，返回Agent类型"""

        user_input_lower = user_input.lower()

        # Logo设计关键词 - 优先级最高，因为最容易被误识别
        logo_keywords = ["logo", "标识", "品牌标志", "商标", "标志", "品牌logo", "企业标识"]
        if any(keyword in user_input_lower for keyword in logo_keywords):
            return "LogoDesignAgent"

        # 海报设计关键词
        poster_keywords = ["海报", "宣传海报", "广告海报", "poster", "宣传", "广告"]
        if any(keyword in user_input_lower for keyword in poster_keywords):
            return "PosterDesignAgent"

        # 服装设计关键词
        fashion_keywords = ["服装", "衣服", "裙子", "上衣", "裤子", "外套", "时装", "连衣裙", "T恤", "衬衫"]
        if any(keyword in user_input_lower for keyword in fashion_keywords):
            return "FashionDesignAgent"

        # 剧情制作关键词
        drama_keywords = ["剧本", "剧情", "电影", "短片", "视频", "拍摄", "导演", "角色", "场景", "故事", "情节", "戏剧", "话剧", "制作", "编剧"]
        if any(keyword in user_input_lower for keyword in drama_keywords):
            return "DramaProductionAgent"

        # 如果没有明确关键词，返回None，让编排器决定
        return None
    
    def _ensure_agents_available(
        self,
        required_agents: List[str]
    ) -> List[str]:
        """Ensure required agents are available in the pool."""
        
        available_agents = []
        
        for agent_type in required_agents:
            if agent_type in self.active_agents:
                available_agents.append(agent_type)
            else:
                # Try to create the agent
                agent = self.factory.create_agent(agent_type)
                if agent:
                    self.active_agents[agent_type] = agent
                    available_agents.append(agent_type)
                else:
                    logger.warning(f"Could not create required agent: {agent_type}")
        
        return available_agents
    
    def get_active_agents(self) -> Dict[str, str]:
        """Get information about active agents."""
        return {
            agent_type: agent.agent_id 
            for agent_type, agent in self.active_agents.items()
        }
    
    def get_agent_capabilities_summary(self) -> Dict[str, List[str]]:
        """Get capabilities summary for all active agents."""
        return {
            agent_type: agent.get_capabilities()
            for agent_type, agent in self.active_agents.items()
        }
    
    async def cleanup_inactive_agents(self):
        """Cleanup inactive agents to free resources."""
        # Implementation could track agent usage and cleanup unused agents
        logger.info("Agent cleanup completed")

    async def intelligent_process_request(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        message_type: str = "user",
        requirements: Optional[Dict] = None,
        edit_image_url: Optional[str] = None,
        edit_image_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Intelligently process user request by deciding the best approach.

        This method analyzes the user input and decides whether to:
        1. Use the original LangGraph workflow (for fashion design)
        2. Route to specialized agents (for other design types)
        3. Use multi-agent collaboration (for complex requests)
        """

        try:
            # 使用编排器，让它内部智能决策
            if self.collaboration_orchestrator is None:
                self.collaboration_orchestrator = CollaborationOrchestrator(
                    agent_pool=self.active_agents,
                    communication_manager=self.communication_manager
                )

            logger.info(f"🤝 使用编排器处理: {user_input}")

            # 获取对话历史（从数据库或缓存中）
            conversation_history = await self._get_conversation_history(conversation_id)
            previous_requirements = await self._get_previous_requirements(conversation_id)

            # 让编排器完全负责智能分析和执行，传递上下文信息 - 异步模式
            import asyncio

            # 如果没有conversation_id，生成一个
            if not conversation_id:
                from uuid import uuid4
                conversation_id = str(uuid4())
                logger.info(f"Generated conversation_id for multi-agent collaboration: {conversation_id}")

            # 同步处理协作任务 - 避免重复执行
            try:
                result = await self.collaboration_orchestrator.orchestrate_collaboration(
                    user_request=user_input,
                    collaboration_type=CollaborationType.SEQUENTIAL,
                    conversation_id=conversation_id,
                    user_id=user_id,
                    conversation_history=conversation_history,
                    previous_requirements=previous_requirements,
                    **kwargs
                )

                if result.get("success"):
                    # 通过WebSocket推送最终的对话结果
                    await self._broadcast_collaboration_result(conversation_id, result)
                    logger.info(f"Multi-agent collaboration completed for: {conversation_id}")
                else:
                    logger.error(f"Multi-agent collaboration failed: {result.get('error')}")

                # 返回协作结果
                return result

            except Exception as e:
                logger.error(f"Synchronous collaboration processing failed: {e}")
                # 返回错误结果，让调用者处理
                return {
                    "success": False,
                    "error": str(e),
                    "conversation_id": conversation_id,
                    "status": "failed"
                }

        except Exception as e:
            logger.error(f"Intelligent processing failed: {e}")
            # 直接返回错误，不使用fallback
            return {
                "success": False,
                "error": str(e),
                "conversation_id": conversation_id,
                "status": "failed",
                "coordination_info": {
                    "approach": "error",
                    "handler": "none",
                    "reasoning": f"Processing failed: {str(e)}"
                }
            }

    def _extract_workflow_result(self, task_results: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        通用的工作流结果提取方法

        支持的Agent类型会自动检测并提取其工作流结果，无需硬编码
        """
        for task_result in task_results:
            if not isinstance(task_result, dict):
                continue

            agent_type = task_result.get("metadata", {}).get("agent_type")
            if not agent_type:
                continue

            output = task_result.get("output", {})
            if not output:
                continue

            # 检查是否是设计Agent的工作流结果
            workflow_result = self._detect_workflow_result(agent_type, output)
            if workflow_result:
                logger.info(f"直接返回{agent_type}的workflow结果")
                return workflow_result

        return None

    def _detect_workflow_result(self, agent_type: str, output: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        智能检测工作流结果

        根据输出结构自动判断是否包含工作流结果，支持多种Agent类型
        """
        # 检查是否包含工作流的标准字段
        workflow_indicators = [
            "current_state", "requirements", "messages", "conversation_id",
            "intent", "image_url", "design_prompt"
        ]

        # 方法1: 直接检查output是否包含工作流字段
        if any(key in output for key in workflow_indicators):
            return output

        # 方法2: 检查嵌套的design_result字段（如PosterDesignAgent）
        if "design_result" in output:
            design_result = output["design_result"]
            if isinstance(design_result, dict) and any(key in design_result for key in workflow_indicators):
                return design_result

        # 方法3: 检查其他可能的嵌套结构
        for key in ["workflow_result", "result", "data"]:
            if key in output:
                nested_result = output[key]
                if isinstance(nested_result, dict) and any(indicator in nested_result for indicator in workflow_indicators):
                    return nested_result

        return None

    def _analyze_request_strategy(self, user_input: str) -> Dict[str, Any]:
        """Analyze user request to determine processing strategy."""

        # For now, let the collaboration orchestrator decide
        # This removes the need for keyword matching and puts intelligence
        # in the collaboration layer where it belongs

        # The orchestrator can analyze the request and decide:
        # - Whether to use single agent or collaboration
        # - Which agents are needed
        # - What collaboration type to use

        return {
            "approach": "collaboration_orchestrator",
            "reasoning": "让协作编排器智能分析和决策"
        }

    
    async def _get_conversation_history(self, conversation_id: str) -> Optional[List[Dict[str, str]]]:
        """获取对话历史，用于上下文理解"""
        if not conversation_id:
            return None

        try:
            from ..core.database import db_manager
            from ..services.design_service import ChatService
            from uuid import UUID

            # 确保数据库已初始化
            if db_manager._async_session_factory is None:
                logger.warning("数据库未初始化，跳过对话历史获取")
                return None

            # 获取数据库会话
            async with db_manager.get_async_session_context() as session:
                chat_service = ChatService(session)

                # 获取对话的聊天消息（最近10条）
                messages = await chat_service.get_conversation_messages(
                    conversation_id=UUID(conversation_id),
                    skip=0,
                    limit=10
                )

                # 转换为上下文格式
                conversation_history = []
                for msg in messages:
                    conversation_history.append({
                        "role": msg.role,
                        "content": msg.content,
                        "created_at": msg.created_at.isoformat() if msg.created_at else "",
                        "message_metadata": msg.message_metadata or {}
                    })

                logger.info(f"获取到{len(conversation_history)}条对话历史")
                return conversation_history

        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return None

    async def _get_previous_requirements(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取之前的需求信息，用于上下文理解"""
        if not conversation_id:
            return None

        try:
            from ..core.database import db_manager
            from ..services.design_service import ChatService
            from uuid import UUID
            import json

            # 确保数据库已初始化
            if db_manager._async_session_factory is None:
                logger.warning("数据库未初始化，跳过之前需求获取")
                return None

            # 获取数据库会话
            async with db_manager.get_async_session_context() as session:
                chat_service = ChatService(session)

                # 获取对话的聊天消息
                messages = await chat_service.get_conversation_messages(
                    conversation_id=UUID(conversation_id),
                    skip=0,
                    limit=20  # 查看更多消息以找到需求信息
                )

                # 从消息的metadata中查找需求信息
                previous_requirements = None
                for msg in reversed(messages):  # 从最新的开始查找
                    if msg.message_metadata:
                        metadata = msg.message_metadata

                        # 查找需求确认相关的metadata
                        if metadata.get("type") == "requirements_confirmation":
                            previous_requirements = metadata.get("requirementsData")
                            break

                        # 查找workflow_state中的requirements
                        if "workflow_state" in metadata:
                            workflow_state = metadata["workflow_state"]
                            if isinstance(workflow_state, dict) and "requirements" in workflow_state:
                                previous_requirements = workflow_state["requirements"]
                                break

                        # 查找直接存储的requirements
                        if "requirements" in metadata:
                            previous_requirements = metadata["requirements"]
                            break

                if previous_requirements:
                    logger.info(f"找到之前的需求信息: {previous_requirements}")
                    return previous_requirements
                else:
                    logger.info("未找到之前的需求信息")
                    return None

        except Exception as e:
            logger.error(f"获取之前需求失败: {e}")
            return None

    async def _broadcast_collaboration_result(self, conversation_id: str, result: Dict[str, Any]):
        """通过WebSocket推送多Agent协作结果"""
        try:
            from .websocket_manager import get_design_broadcaster

            broadcaster = get_design_broadcaster()

            # 构建对话结果消息
            message_data = {
                "conversation_id": conversation_id,
                "messages": [
                    {
                        "role": "assistant",
                        "content": result.get("integration_summary", "多Agent协作完成")
                    }
                ],
                "coordination_info": {
                    "approach": "orchestrator",
                    "handler": "CollaborationOrchestrator",
                    "reasoning": "编排器智能处理",
                    "tasks_executed": result.get("successful_tasks", 0)
                },
                "timestamp": result.get("timestamp"),
                "metadata": result.get("metadata", {})
            }

            # 发送对话结果
            await broadcaster.send_conversation_result(conversation_id, message_data)

            logger.info(f"Collaboration result broadcasted for: {conversation_id}")

        except Exception as e:
            logger.error(f"Failed to broadcast collaboration result: {e}")
            # 不抛出异常，避免影响主流程
