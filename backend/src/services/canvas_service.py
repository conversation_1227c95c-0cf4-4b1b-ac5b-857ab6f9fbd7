"""
Canvas state service for managing design canvas persistence.
"""

import json
from typing import List, Optional, Dict, Any
from uuid import uuid4
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select

from ..models.database import CanvasState, CanvasArtifact, TaskArtifact, Conversation, User
from ..models.schemas_canvas import (
    CanvasStateCreate, CanvasStateUpdate, CanvasStateResponse,
    CanvasArtifactCreate, CanvasArtifactUpdate, CanvasArtifactResponse,
    CanvasStateData, CanvasArtifactPosition
)


class CanvasStateService:
    """Service for managing canvas state persistence."""

    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_canvas_state(
        self,
        conversation_id: str,
        user_id: str,
        canvas_data: CanvasStateData
    ) -> CanvasState:
        """Create a new canvas state."""

        try:
            print(f"🔧 CanvasService: 开始创建画布状态")
            print(f"📝 conversation_id: {conversation_id}")
            print(f"👤 user_id: {user_id}")

            # Check if canvas state already exists for this conversation
            print(f"🔍 检查是否存在现有画布状态")
            stmt = select(CanvasState).where(
                and_(
                    CanvasState.conversation_id == conversation_id,
                    CanvasState.user_id == user_id
                )
            )
            result = await self.db.execute(stmt)
            existing_state = result.scalar_one_or_none()

            if existing_state:
                print(f"📝 找到现有状态，更新: {existing_state.id}")
                # Update existing state
                return await self.update_canvas_state(existing_state.id, canvas_data)

            print(f"🆕 创建新的画布状态")
            # Create new canvas state
            canvas_data_dict = canvas_data.model_dump()
            print(f"📊 canvas_data_dict: {canvas_data_dict}")

            canvas_state = CanvasState(
                id=str(uuid4()),
                conversation_id=conversation_id,
                user_id=user_id,
                canvas_data=canvas_data_dict
            )

            print(f"💾 添加到数据库")
            self.db.add(canvas_state)
            print(f"💾 提交事务")
            await self.db.commit()
            print(f"💾 刷新对象")
            await self.db.refresh(canvas_state)

            print(f"✅ CanvasService: 画布状态创建成功")
            return canvas_state

        except Exception as e:
            print(f"❌ CanvasService: 创建画布状态失败: {e}")
            print(f"❌ Exception type: {type(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            raise
    
    async def get_canvas_state(self, conversation_id: str, user_id: str) -> Optional[CanvasState]:
        """Get canvas state for a conversation and user."""
        stmt = select(CanvasState).where(
            and_(
                CanvasState.conversation_id == conversation_id,
                CanvasState.user_id == user_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_canvas_state(self, canvas_state_id: str, canvas_data: CanvasStateData) -> CanvasState:
        """Update canvas state."""
        stmt = select(CanvasState).where(CanvasState.id == canvas_state_id)
        result = await self.db.execute(stmt)
        canvas_state = result.scalar_one_or_none()

        if not canvas_state:
            raise ValueError(f"Canvas state {canvas_state_id} not found")

        canvas_state.canvas_data = canvas_data.model_dump()
        canvas_state.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(canvas_state)

        # Sync artifacts
        await self.sync_canvas_artifacts(canvas_state_id, canvas_data.artifacts)

        return canvas_state
    
    async def sync_canvas_artifacts(
        self,
        canvas_state_id: str,
        artifact_positions: List[CanvasArtifactPosition]
    ) -> List[CanvasArtifact]:
        """Sync canvas artifacts with the given positions."""

        # Get existing artifacts
        stmt = select(CanvasArtifact).where(CanvasArtifact.canvas_state_id == canvas_state_id)
        result = await self.db.execute(stmt)
        existing_artifacts = result.scalars().all()
        
        existing_artifact_ids = {art.artifact_id for art in existing_artifacts}
        new_artifact_ids = {pos.artifact_id for pos in artifact_positions}
        
        # Update existing artifacts
        for artifact in existing_artifacts:
            if artifact.artifact_id in new_artifact_ids:
                # Find the corresponding position data
                position_data = next(
                    (pos for pos in artifact_positions if pos.artifact_id == artifact.artifact_id),
                    None
                )
                
                if position_data:
                    artifact.position_x = position_data.position_x
                    artifact.position_y = position_data.position_y
                    artifact.scale = position_data.scale
                    artifact.rotation = position_data.rotation
                    artifact.z_index = position_data.z_index
                    artifact.is_selected = position_data.is_selected
                    artifact.is_visible = position_data.is_visible
                    artifact.canvas_metadata = position_data.metadata
                    artifact.updated_at = datetime.utcnow()
        
        # Add new artifacts
        for artifact_id in new_artifact_ids - existing_artifact_ids:
            position_data = next(
                (pos for pos in artifact_positions if pos.artifact_id == artifact_id),
                None
            )
            
            if position_data:
                canvas_artifact = CanvasArtifact(
                    id=str(uuid4()),
                    canvas_state_id=canvas_state_id,
                    artifact_id=artifact_id,
                    position_x=position_data.position_x,
                    position_y=position_data.position_y,
                    scale=position_data.scale,
                    rotation=position_data.rotation,
                    z_index=position_data.z_index,
                    is_selected=position_data.is_selected,
                    is_visible=position_data.is_visible,
                    canvas_metadata=position_data.metadata
                )
                self.db.add(canvas_artifact)
        
        # Remove artifacts that are no longer in the canvas
        for artifact in existing_artifacts:
            if artifact.artifact_id not in new_artifact_ids:
                await self.db.delete(artifact)

        await self.db.commit()

        # Return updated artifacts
        stmt = select(CanvasArtifact).where(CanvasArtifact.canvas_state_id == canvas_state_id)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    def get_canvas_artifacts(self, canvas_state_id: str) -> List[CanvasArtifact]:
        """Get all artifacts for a canvas state."""
        return self.db.query(CanvasArtifact).filter(
            CanvasArtifact.canvas_state_id == canvas_state_id
        ).all()
    
    def update_artifact_position(
        self, 
        canvas_state_id: str, 
        artifact_id: str, 
        position_data: CanvasArtifactUpdate
    ) -> Optional[CanvasArtifact]:
        """Update position and properties of a specific artifact."""
        artifact = self.db.query(CanvasArtifact).filter(
            and_(
                CanvasArtifact.canvas_state_id == canvas_state_id,
                CanvasArtifact.artifact_id == artifact_id
            )
        ).first()
        
        if not artifact:
            return None
        
        # Update fields
        if position_data.position_x is not None:
            artifact.position_x = position_data.position_x
        if position_data.position_y is not None:
            artifact.position_y = position_data.position_y
        if position_data.scale is not None:
            artifact.scale = position_data.scale
        if position_data.rotation is not None:
            artifact.rotation = position_data.rotation
        if position_data.z_index is not None:
            artifact.z_index = position_data.z_index
        if position_data.is_selected is not None:
            artifact.is_selected = position_data.is_selected
        if position_data.is_visible is not None:
            artifact.is_visible = position_data.is_visible
        if position_data.metadata is not None:
            artifact.canvas_metadata = position_data.metadata
        
        artifact.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(artifact)
        
        return artifact
    
    def delete_canvas_state(self, canvas_state_id: str) -> bool:
        """Delete a canvas state."""
        canvas_state = self.db.query(CanvasState).filter(
            CanvasState.id == canvas_state_id
        ).first()
        
        if not canvas_state:
            return False
        
        self.db.delete(canvas_state)
        self.db.commit()
        
        return True
    
    def clear_canvas_state(self, conversation_id: str, user_id: str) -> bool:
        """Clear canvas state for a conversation."""
        canvas_state = self.db.query(CanvasState).filter(
            and_(
                CanvasState.conversation_id == conversation_id,
                CanvasState.user_id == user_id
            )
        ).first()
        
        if not canvas_state:
            return False
        
        # Clear artifacts
        self.db.query(CanvasArtifact).filter(
            CanvasArtifact.canvas_state_id == canvas_state.id
        ).delete()
        
        # Clear canvas data
        canvas_state.canvas_data = {"artifacts": [], "view_state": {}, "canvas_metadata": {}}
        canvas_state.updated_at = datetime.utcnow()
        
        self.db.commit()
        
        return True
    
    def to_response(self, canvas_state: CanvasState) -> CanvasStateResponse:
        """Convert canvas state to response schema."""
        return CanvasStateResponse(
            id=canvas_state.id,
            conversation_id=canvas_state.conversation_id,
            user_id=canvas_state.user_id,
            canvas_data=CanvasStateData(**canvas_state.canvas_data),
            created_at=canvas_state.created_at,
            updated_at=canvas_state.updated_at
        )
    
    def artifact_to_response(self, artifact: CanvasArtifact) -> CanvasArtifactResponse:
        """Convert canvas artifact to response schema."""
        return CanvasArtifactResponse(
            id=artifact.id,
            canvas_state_id=artifact.canvas_state_id,
            artifact_id=artifact.artifact_id,
            position_x=artifact.position_x,
            position_y=artifact.position_y,
            scale=artifact.scale,
            rotation=artifact.rotation,
            z_index=artifact.z_index,
            is_selected=artifact.is_selected,
            is_visible=artifact.is_visible,
            metadata=artifact.canvas_metadata,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at
        )