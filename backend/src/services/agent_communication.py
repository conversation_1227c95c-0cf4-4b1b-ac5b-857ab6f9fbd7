"""
Agent Communication System.

This module implements the communication infrastructure for multi-agent collaboration,
including message bus, shared workspace, and event system.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from collections import defaultdict
from uuid import uuid4

from .base_agents import (
    AgentMessage, DesignArtifact, MessageType,
    IMessageBus, ISharedWorkspace
)

logger = logging.getLogger(__name__)


# ============================================================================
# Message Bus Implementation
# ============================================================================

class MessageBus(IMessageBus):
    """In-memory message bus for agent communication."""
    
    def __init__(self):
        self.subscribers: Dict[str, Set[str]] = defaultdict(set)  # topic -> agent_ids
        self.agent_queues: Dict[str, asyncio.Queue] = {}  # agent_id -> message_queue
        self.message_history: List[AgentMessage] = []
        self.max_history = 1000
        self._lock = asyncio.Lock()
    
    async def publish(self, message: AgentMessage) -> bool:
        """Publish message to the bus."""
        try:
            async with self._lock:
                # Add to history
                self.message_history.append(message)
                if len(self.message_history) > self.max_history:
                    self.message_history.pop(0)
                
                # Deliver to subscribers
                topic = message["topic"]
                recipient_id = message.get("recipient_id")
                
                if recipient_id:
                    # Direct message to specific agent
                    await self._deliver_to_agent(recipient_id, message)
                else:
                    # Broadcast to all subscribers of the topic
                    for agent_id in self.subscribers[topic]:
                        if agent_id != message["sender_id"]:  # Don't send to sender
                            await self._deliver_to_agent(agent_id, message)
                
                logger.debug(f"Published message {message['id']} to topic {topic}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            return False
    
    async def subscribe(self, agent_id: str, topics: List[str]) -> bool:
        """Subscribe agent to topics."""
        try:
            async with self._lock:
                # Create queue for agent if not exists
                if agent_id not in self.agent_queues:
                    self.agent_queues[agent_id] = asyncio.Queue()
                
                # Subscribe to topics
                for topic in topics:
                    self.subscribers[topic].add(agent_id)
                
                logger.info(f"Agent {agent_id} subscribed to topics: {topics}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to subscribe agent {agent_id}: {e}")
            return False
    
    async def unsubscribe(self, agent_id: str, topics: List[str]) -> bool:
        """Unsubscribe agent from topics."""
        try:
            async with self._lock:
                for topic in topics:
                    self.subscribers[topic].discard(agent_id)
                
                logger.info(f"Agent {agent_id} unsubscribed from topics: {topics}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to unsubscribe agent {agent_id}: {e}")
            return False
    
    async def get_messages(self, agent_id: str, timeout: float = 1.0) -> List[AgentMessage]:
        """Get messages for agent (non-blocking)."""
        messages = []
        if agent_id not in self.agent_queues:
            return messages
        
        queue = self.agent_queues[agent_id]
        
        try:
            # Get all available messages
            while True:
                try:
                    message = await asyncio.wait_for(queue.get(), timeout=0.1)
                    messages.append(message)
                except asyncio.TimeoutError:
                    break
        except Exception as e:
            logger.error(f"Error getting messages for agent {agent_id}: {e}")
        
        return messages
    
    async def _deliver_to_agent(self, agent_id: str, message: AgentMessage):
        """Deliver message to specific agent."""
        if agent_id in self.agent_queues:
            try:
                await self.agent_queues[agent_id].put(message)
            except Exception as e:
                logger.error(f"Failed to deliver message to agent {agent_id}: {e}")
    
    def get_message_history(
        self, 
        conversation_id: Optional[str] = None,
        limit: int = 100
    ) -> List[AgentMessage]:
        """Get message history with optional filtering."""
        filtered_messages = self.message_history
        
        if conversation_id:
            filtered_messages = [m for m in filtered_messages if m.get("conversation_id") == conversation_id]
        
        if conversation_id:
            filtered_messages = [m for m in filtered_messages if m.get("conversation_id") == conversation_id]
        
        return filtered_messages[-limit:]


# ============================================================================
# Shared Workspace Implementation
# ============================================================================

class SharedWorkspace(ISharedWorkspace):
    """Shared workspace for design artifacts."""
    
    def __init__(self):
        self.artifacts: Dict[str, DesignArtifact] = {}
        self.conversation_artifacts: Dict[str, Set[str]] = defaultdict(set)  # conversation_id -> artifact_ids
        self.agent_artifacts: Dict[str, Set[str]] = defaultdict(set)    # agent_id -> artifact_ids
        self.access_control: Dict[str, Set[str]] = defaultdict(set)     # artifact_id -> allowed_agent_ids
        self.version_history: Dict[str, List[DesignArtifact]] = defaultdict(list)
        self._lock = asyncio.Lock()
    
    async def store_artifact(
        self,
        artifact: DesignArtifact,
        agent_id: str
    ) -> bool:
        """Store design artifact."""
        try:
            async with self._lock:
                artifact_id = artifact["id"]
                
                # Store artifact
                self.artifacts[artifact_id] = artifact
                
                # Update indices
                conversation_id = artifact["metadata"].get("conversation_id")
                if conversation_id:
                    self.conversation_artifacts[conversation_id].add(artifact_id)
                
                self.agent_artifacts[agent_id].add(artifact_id)
                
                # Set access control (creator has full access)
                self.access_control[artifact_id].add(agent_id)
                
                # Add to version history
                self.version_history[artifact_id].append(artifact)
                
                logger.info(f"Stored artifact {artifact_id} by agent {agent_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store artifact: {e}")
            return False
    
    async def get_artifact(
        self,
        artifact_id: str,
        agent_id: str
    ) -> Optional[DesignArtifact]:
        """Get design artifact."""
        try:
            async with self._lock:
                if artifact_id not in self.artifacts:
                    return None
                
                # Check access control
                if not self._has_access(artifact_id, agent_id):
                    logger.warning(f"Agent {agent_id} denied access to artifact {artifact_id}")
                    return None
                
                return self.artifacts[artifact_id]
                
        except Exception as e:
            logger.error(f"Failed to get artifact {artifact_id}: {e}")
            return None
    
    async def list_artifacts(
        self,
        conversation_id: str,
        artifact_type: Optional[str] = None
    ) -> List[DesignArtifact]:
        """List artifacts for project."""
        try:
            async with self._lock:
                artifact_ids = self.conversation_artifacts.get(conversation_id, set())
                artifacts = []
                
                for artifact_id in artifact_ids:
                    artifact = self.artifacts.get(artifact_id)
                    if artifact:
                        if artifact_type is None or artifact["type"] == artifact_type:
                            artifacts.append(artifact)
                
                # Sort by creation time (newest first)
                artifacts.sort(key=lambda a: a["created_at"], reverse=True)
                return artifacts
                
        except Exception as e:
            logger.error(f"Failed to list artifacts for conversation {conversation_id}: {e}")
            return []
    
    async def grant_access(
        self,
        artifact_id: str,
        agent_id: str,
        requester_id: str
    ) -> bool:
        """Grant access to artifact."""
        try:
            async with self._lock:
                # Check if requester has permission to grant access
                if not self._has_access(artifact_id, requester_id):
                    return False
                
                self.access_control[artifact_id].add(agent_id)
                logger.info(f"Granted access to artifact {artifact_id} for agent {agent_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to grant access: {e}")
            return False
    
    async def update_artifact(
        self,
        artifact_id: str,
        updates: Dict[str, Any],
        agent_id: str
    ) -> bool:
        """Update existing artifact."""
        try:
            async with self._lock:
                if artifact_id not in self.artifacts:
                    return False
                
                if not self._has_access(artifact_id, agent_id):
                    return False
                
                # Create new version
                current_artifact = self.artifacts[artifact_id].copy()
                current_artifact.update(updates)
                current_artifact["version"] = current_artifact.get("version", 1) + 1
                current_artifact["created_at"] = datetime.now().isoformat()
                current_artifact["created_by"] = agent_id
                
                # Store updated artifact
                self.artifacts[artifact_id] = current_artifact
                self.version_history[artifact_id].append(current_artifact)
                
                logger.info(f"Updated artifact {artifact_id} by agent {agent_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update artifact {artifact_id}: {e}")
            return False
    
    def _has_access(self, artifact_id: str, agent_id: str) -> bool:
        """Check if agent has access to artifact."""
        return agent_id in self.access_control.get(artifact_id, set())
    
    async def get_artifact_versions(
        self,
        artifact_id: str,
        agent_id: str
    ) -> List[DesignArtifact]:
        """Get version history of artifact."""
        try:
            async with self._lock:
                if not self._has_access(artifact_id, agent_id):
                    return []
                
                return self.version_history.get(artifact_id, [])
                
        except Exception as e:
            logger.error(f"Failed to get artifact versions: {e}")
            return []


# ============================================================================
# Event System
# ============================================================================

class EventSystem:
    """Event system for agent coordination."""
    
    def __init__(self):
        self.event_handlers: Dict[str, List[callable]] = defaultdict(list)
        self.event_history: List[Dict[str, Any]] = []
        self.max_history = 1000
        self._lock = asyncio.Lock()
    
    async def emit_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        source_agent: str
    ):
        """Emit event to all registered handlers."""
        try:
            async with self._lock:
                event = {
                    "id": str(uuid4()),
                    "type": event_type,
                    "data": data,
                    "source_agent": source_agent,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Add to history
                self.event_history.append(event)
                if len(self.event_history) > self.max_history:
                    self.event_history.pop(0)
                
                # Call handlers
                handlers = self.event_handlers.get(event_type, [])
                for handler in handlers:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(event)
                        else:
                            handler(event)
                    except Exception as e:
                        logger.error(f"Event handler failed: {e}")
                
                logger.debug(f"Emitted event {event_type} from {source_agent}")
                
        except Exception as e:
            logger.error(f"Failed to emit event: {e}")
    
    def register_handler(self, event_type: str, handler: callable):
        """Register event handler."""
        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered handler for event type: {event_type}")
    
    def unregister_handler(self, event_type: str, handler: callable):
        """Unregister event handler."""
        if handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
            logger.info(f"Unregistered handler for event type: {event_type}")
    
    def get_event_history(
        self,
        event_type: Optional[str] = None,
        source_agent: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get event history with optional filtering."""
        filtered_events = self.event_history
        
        if event_type:
            filtered_events = [e for e in filtered_events if e["type"] == event_type]
        
        if source_agent:
            filtered_events = [e for e in filtered_events if e["source_agent"] == source_agent]
        
        return filtered_events[-limit:]


# ============================================================================
# Communication Manager
# ============================================================================

class CommunicationManager:
    """Central manager for all agent communication."""
    
    def __init__(self):
        self.message_bus = MessageBus()
        self.shared_workspace = SharedWorkspace()
        self.event_system = EventSystem()
    
    async def initialize_agent_communication(self, agent_id: str, topics: List[str]):
        """Initialize communication for an agent."""
        await self.message_bus.subscribe(agent_id, topics)
        logger.info(f"Initialized communication for agent {agent_id}")
    
    async def cleanup_agent_communication(self, agent_id: str):
        """Cleanup communication for an agent."""
        # Remove from all topics
        for topic in self.message_bus.subscribers:
            self.message_bus.subscribers[topic].discard(agent_id)
        
        # Remove message queue
        if agent_id in self.message_bus.agent_queues:
            del self.message_bus.agent_queues[agent_id]
        
        logger.info(f"Cleaned up communication for agent {agent_id}")
    
    def get_communication_stats(self) -> Dict[str, Any]:
        """Get communication system statistics."""
        return {
            "active_agents": len(self.message_bus.agent_queues),
            "total_topics": len(self.message_bus.subscribers),
            "message_history_size": len(self.message_bus.message_history),
            "stored_artifacts": len(self.shared_workspace.artifacts),
            "event_history_size": len(self.event_system.event_history)
        }
