"""
Liblib Kontext文生图服务实现
基于Liblib AI的Kontext模型
"""

import asyncio
import time
import hmac
import hashlib
import base64
import uuid
import aiohttp
from datetime import datetime
from typing import Dict, Any, List, Optional

from .base import (
    ImageGenerationService,
    ImageGenerationRequest,
    ImageGenerationResult,
    ImageEditRequest,
    ImageEditResult,
    ImageGenerationStatus,
    ImageGenerationProvider,
    ProviderConfig,
    ImageGenerationException,
    GenerationTimeoutException,
    QuotaExceededException,
    InvalidRequestException
)





class LiblibKontextService(ImageGenerationService):
    """Liblib Kontext文生图服务实现"""
    
    def __init__(self, config: ProviderConfig):
        """
        初始化Liblib服务
        
        Args:
            config: Liblib配置，需要包含ak和sk
        """
        super().__init__(config)
        
        self.ak = config.api_key
        self.sk = config.api_secret
        if not self.sk:
            raise ValueError("Liblib service requires api_secret (sk)")
        
        self.base_url = config.base_url or "https://openapi.liblibai.cloud"
        self.poll_interval = config.additional_config.get("poll_interval", 5) if config.additional_config else 5
        
        # 支持的模板和模型
        self.templates = {
            "text2img": "fe9928fde1b4491c9b360dd24aa2b115",
            "img2img": "1c0a9712b3d84e1b8a9f49514a46d88c"
        }
        
        # 支持的宽高比
        self.aspect_ratios = ["1:1", "2:3", "3:4", "4:3", "3:2", "16:9", "9:16"]
        
        # 模型类型
        self.models = ["pro", "standard"]
    
    def _generate_signature(self, endpoint: str, timestamp: int, nonce: str) -> str:
        """
        生成API签名
        
        Args:
            endpoint: API端点
            timestamp: 时间戳
            nonce: 随机字符串
            
        Returns:
            str: 签名
        """
        data = f"{endpoint}&{timestamp}&{nonce}"
        hmac_code = hmac.new(self.sk.encode(), data.encode(), hashlib.sha1)
        signature = base64.urlsafe_b64encode(hmac_code.digest()).rstrip(b'=').decode()
        return signature
    
    def _build_api_url(self, endpoint: str) -> tuple[str, Dict[str, str]]:
        """
        构建API URL和参数
        
        Args:
            endpoint: API端点
            
        Returns:
            tuple: (URL, 查询参数)
        """
        timestamp = int(datetime.now().timestamp() * 1000)
        nonce = str(uuid.uuid1())
        signature = self._generate_signature(endpoint, timestamp, nonce)
        
        url = f"{self.base_url}{endpoint}"
        params = {
            "AccessKey": self.ak,
            "Signature": signature,
            "Timestamp": str(timestamp),
            "SignatureNonce": nonce
        }
        
        return url, params
    
    def validate_request(self, request: ImageGenerationRequest) -> bool:
        """
        验证Liblib请求参数
        
        Args:
            request: 图片生成请求
            
        Returns:
            bool: 验证是否通过
        """
        if not request.prompt or len(request.prompt.strip()) == 0:
            raise ValueError("Prompt cannot be empty")
        
        if len(request.prompt) > 1000:
            raise ValueError("Prompt too long (max 1000 characters)")
        
        if request.num_images < 1 or request.num_images > 4:
            raise ValueError("num_images must be between 1 and 4")
        
        # 检查宽高比
        aspect_ratio = self._calculate_aspect_ratio(request.width, request.height)
        if aspect_ratio not in self.aspect_ratios:
            raise ValueError(f"Unsupported aspect ratio: {aspect_ratio}. Supported: {self.aspect_ratios}")
        
        return True
    
    def _calculate_aspect_ratio(self, width: int, height: int) -> str:
        """
        计算宽高比
        
        Args:
            width: 宽度
            height: 高度
            
        Returns:
            str: 宽高比字符串
        """
        from math import gcd
        
        # 计算最大公约数
        common_divisor = gcd(width, height)
        ratio_w = width // common_divisor
        ratio_h = height // common_divisor
        
        # 映射到支持的宽高比
        ratio_str = f"{ratio_w}:{ratio_h}"
        
        # 如果不在支持列表中，找最接近的
        if ratio_str not in self.aspect_ratios:
            # 简单映射逻辑
            if width == height:
                return "1:1"
            elif width > height:
                if ratio_w / ratio_h > 1.5:
                    return "16:9"
                else:
                    return "4:3"
            else:
                if ratio_h / ratio_w > 1.5:
                    return "9:16"
                else:
                    return "3:4"
        
        return ratio_str
    
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """
        使用Liblib Kontext生成图片
        
        Args:
            request: 图片生成请求
            
        Returns:
            ImageGenerationResult: 生成结果
        """
        try:
            # 验证请求
            self.validate_request(request)
            
            start_time = time.time()
            
            # 构建请求数据
            aspect_ratio = self._calculate_aspect_ratio(request.width, request.height)
            
            # 质量映射到guidance_scale
            guidance_scale_map = {
                "draft": 2.0,
                "standard": 3.5,
                "high": 5.0,
                "premium": 7.5
            }
            
            guidance_scale = guidance_scale_map.get(request.quality, 3.5)
            if request.guidance_scale:
                guidance_scale = request.guidance_scale
            
            generate_data = {
                "templateUuid": self.templates["text2img"],
                "generateParams": {
                    "model": request.model or "pro",
                    "prompt": request.prompt,
                    "aspectRatio": aspect_ratio,
                    "guidance_scale": guidance_scale,
                    "imgCount": request.num_images
                }
            }
            
            # 添加可选参数
            if request.seed:
                generate_data["generateParams"]["seed"] = request.seed
            
            # 提交生成任务
            url, params = self._build_api_url("/api/generate/kontext/text2img")
            
            self.logger.info(f"Submitting generation task to Liblib: {request.prompt[:50]}...")
            
            async with aiohttp.ClientSession() as session:
                # 提交任务
                async with session.post(
                    url, 
                    params=params,
                    json=generate_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status != 200:
                        raise ImageGenerationException(
                            f"Failed to submit task: HTTP {response.status}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            str(response.status)
                        )
                    
                    result = await response.json()
                    
                    if result.get("code") != 0:
                        error_msg = result.get("msg", "Unknown error")
                        if "quota" in error_msg.lower() or "limit" in error_msg.lower():
                            raise QuotaExceededException(
                                f"Quota exceeded: {error_msg}",
                                ImageGenerationProvider.LIBLIB_KONTEXT,
                                "QUOTA_EXCEEDED"
                            )
                        raise ImageGenerationException(
                            f"Task submission failed: {error_msg}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            str(result.get("code"))
                        )
                    
                    generation_uuid = result["data"]["generateUuid"]
                    self.logger.info(f"Task submitted successfully, UUID: {generation_uuid}")
                
                # 轮询任务状态
                return await self._poll_generation_status(
                    session, generation_uuid, request, start_time
                )
                
        except asyncio.TimeoutError:
            raise GenerationTimeoutException(
                f"Generation timeout after {self.config.timeout}s",
                ImageGenerationProvider.LIBLIB_KONTEXT,
                "TIMEOUT"
            )
        except Exception as e:
            if isinstance(e, ImageGenerationException):
                raise
            return await self._handle_error(e, request)
    
    async def _poll_generation_status(
        self,
        session: aiohttp.ClientSession,
        generation_uuid: str,
        request: ImageGenerationRequest,
        start_time: float
    ) -> ImageGenerationResult:
        """
        轮询生成状态

        Args:
            session: HTTP会话
            generation_uuid: 生成任务UUID
            request: 原始请求
            start_time: 开始时间

        Returns:
            ImageGenerationResult: 生成结果
        """
        status_url, status_params = self._build_api_url("/api/generate/status")
        status_data = {"generateUuid": generation_uuid}

        last_progress = 0
        audit_start_time = None  # 记录开始审核的时间
        audit_timeout = 120  # 审核超时时间：2分钟

        while True:
            current_time = time.time()
            if (current_time - start_time) > self.config.timeout:
                raise GenerationTimeoutException(
                    f"Generation timeout after {self.config.timeout}s",
                    ImageGenerationProvider.LIBLIB_KONTEXT,
                    "TIMEOUT"
                )

            try:
                async with session.post(
                    status_url,
                    params=status_params,
                    json=status_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status != 200:
                        self.logger.warning(f"Status check failed with HTTP {response.status}, retrying...")
                        await asyncio.sleep(self.poll_interval)
                        continue

                    result = await response.json()

                    # 检查API响应状态
                    if result.get("code") != 0:
                        error_msg = result.get("msg", "Unknown error")
                        self.logger.warning(f"API returned error: {error_msg}, retrying...")
                        await asyncio.sleep(self.poll_interval)
                        continue

                    data = result.get("data", {})

                    # 获取生成状态和进度
                    generate_status = data.get("generateStatus", 0)
                    percent_completed = data.get("percentCompleted", 0)
                    generate_msg = data.get("generateMsg", "")

                    # 记录进度变化
                    if percent_completed > last_progress:
                        self.logger.info(f"Generation progress: {percent_completed:.1f}% - {generate_msg}")
                        last_progress = percent_completed

                    # 检查生成状态 (根据4.3.1节状态定义)
                    if generate_status == 5:  # 成功
                        result = await self._handle_generation_completed(
                            data, generation_uuid, request, start_time
                        )
                        if result is not None:  # 如果有图片通过审核或审核完成
                            return result
                        # 如果返回None，说明还有图片在审核中，继续轮询
                    elif generate_status == 6:  # 失败
                        error_msg = generate_msg or "Generation failed"
                        raise ImageGenerationException(
                            f"Generation failed: {error_msg}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            str(generate_status)
                        )
                    elif generate_status == 7:  # 超时
                        error_msg = generate_msg or "Generation timeout (30 minutes)"
                        raise GenerationTimeoutException(
                            f"Generation timeout: {error_msg}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            "SERVER_TIMEOUT"
                        )
                    elif generate_status in [1, 2, 3, 4]:  # 等待执行、执行中、已生图、审核中
                        status_desc = {
                            1: "等待执行",
                            2: "执行中",
                            3: "已生图",
                            4: "审核中"
                        }.get(generate_status, "未知状态")

                        # 检查审核超时
                        if generate_status == 4:  # 审核中
                            if audit_start_time is None:
                                audit_start_time = current_time
                                self.logger.info("Images entered audit phase")
                            elif (current_time - audit_start_time) > audit_timeout:
                                raise GenerationTimeoutException(
                                    f"Audit timeout after {audit_timeout}s",
                                    ImageGenerationProvider.LIBLIB_KONTEXT,
                                    "AUDIT_TIMEOUT"
                                )

                        self.logger.debug(f"Task status: {status_desc} ({generate_status}), progress: {percent_completed:.1f}%")
                    else:
                        self.logger.warning(f"Unknown generation status: {generate_status}")

            except asyncio.TimeoutError:
                self.logger.warning("Status check timeout, retrying...")
            except Exception as e:
                self.logger.error(f"Error during status check: {e}")
                await asyncio.sleep(self.poll_interval)
                continue

            await asyncio.sleep(self.poll_interval)

    async def _handle_generation_completed(
        self,
        data: Dict[str, Any],
        generation_uuid: str,
        request: ImageGenerationRequest,
        start_time: float
    ) -> Optional[ImageGenerationResult]:
        """
        处理生成成功的结果 (generateStatus = 5)

        Args:
            data: API返回的数据
            generation_uuid: 生成任务UUID
            request: 原始请求
            start_time: 开始时间

        Returns:
            ImageGenerationResult: 生成结果
        """
        generation_time = time.time() - start_time
        images = data.get("images", [])

        if not images:
            raise ImageGenerationException(
                "No images returned from completed generation",
                ImageGenerationProvider.LIBLIB_KONTEXT,
                "NO_IMAGES"
            )

        # 提取图片URL，处理审核状态 (根据4.3.2节审核状态定义)
        image_urls = []
        audit_pending_count = 0
        audit_failed_count = 0
        audit_blocked_count = 0

        for img in images:
            if img and isinstance(img, dict):
                image_url = img.get("imageUrl")
                audit_status = img.get("auditStatus", 0)

                if image_url and audit_status == 3:  # 审核通过
                    image_urls.append(image_url)
                elif audit_status in [1, 2]:  # 待审核、审核中
                    audit_pending_count += 1
                    status_desc = "待审核" if audit_status == 1 else "审核中"
                    self.logger.info(f"Image {status_desc}: {image_url}")
                elif audit_status == 4:  # 审核拦截
                    audit_blocked_count += 1
                    self.logger.warning(f"Image blocked by audit: {image_url}")
                elif audit_status == 5:  # 审核失败
                    audit_failed_count += 1
                    self.logger.warning(f"Image failed audit: {image_url}")
                else:
                    self.logger.warning(f"Image with unknown audit status {audit_status}: {image_url}")

        # 如果还有图片在审核中，继续等待
        if audit_pending_count > 0 and not image_urls:
            self.logger.info(f"Waiting for {audit_pending_count} images to complete audit...")
            return None  # 返回None表示需要继续轮询

        # 如果没有通过审核的图片
        if not image_urls:
            total_failed = audit_failed_count + audit_blocked_count
            if total_failed > 0:
                error_details = []
                if audit_failed_count > 0:
                    error_details.append(f"{audit_failed_count} failed")
                if audit_blocked_count > 0:
                    error_details.append(f"{audit_blocked_count} blocked")

                raise ImageGenerationException(
                    f"All images failed content audit: {', '.join(error_details)}",
                    ImageGenerationProvider.LIBLIB_KONTEXT,
                    "AUDIT_FAILED"
                )
            else:
                raise ImageGenerationException(
                    "No valid images returned from generation",
                    ImageGenerationProvider.LIBLIB_KONTEXT,
                    "NO_IMAGES"
                )

        # 构建元数据
        metadata = {
            "generation_uuid": generation_uuid,
            "generation_time": generation_time,
            "model": request.model or "pro",
            "aspect_ratio": self._calculate_aspect_ratio(request.width, request.height),
            "points_cost": data.get("pointsCost", 0),
            "account_balance": data.get("accountBalance", 0),
            "percent_completed": data.get("percentCompleted", 100),
            "generate_status": data.get("generateStatus", 2),
            "total_images": len(images),
            "passed_images": len(image_urls),
            "api_response": data
        }

        # 添加种子信息
        if images and isinstance(images[0], dict) and "seed" in images[0]:
            metadata["seeds"] = [img.get("seed") for img in images if img and isinstance(img, dict)]

        self.logger.info(
            f"Generation completed: {len(image_urls)}/{len(images)} images passed audit "
            f"in {generation_time:.2f}s, cost: {metadata['points_cost']} points"
        )

        return self._create_success_result(
            image_urls=image_urls,
            request=request,
            generation_id=generation_uuid,
            metadata=metadata,
            generation_time=generation_time
        )
    
    async def check_status(self, generation_id: str) -> ImageGenerationStatus:
        """
        检查生成状态

        Args:
            generation_id: 生成任务ID

        Returns:
            ImageGenerationStatus: 生成状态
        """
        try:
            url, params = self._build_api_url("/api/generate/status")
            data = {"generateUuid": generation_id}

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    params=params,
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status != 200:
                        self.logger.warning(f"Status check failed with HTTP {response.status}")
                        return ImageGenerationStatus.FAILED

                    result = await response.json()

                    if result.get("code") != 0:
                        error_msg = result.get("msg", "Unknown error")
                        self.logger.warning(f"API returned error: {error_msg}")
                        return ImageGenerationStatus.FAILED

                    data_obj = result.get("data", {})
                    generate_status = data_obj.get("generateStatus", 0)

                    # 根据generateStatus映射到内部状态 (4.3.1节状态定义)
                    if generate_status == 1:  # 等待执行
                        return ImageGenerationStatus.PENDING
                    elif generate_status in [2, 3, 4]:  # 执行中、已生图、审核中
                        return ImageGenerationStatus.PROCESSING
                    elif generate_status == 5:  # 成功
                        # 检查是否有审核通过的图片 (根据4.3.2节审核状态定义)
                        images = data_obj.get("images", [])
                        if images and any(
                            img and isinstance(img, dict) and img.get("auditStatus") == 3  # 审核通过
                            for img in images
                        ):
                            return ImageGenerationStatus.COMPLETED
                        elif images and any(
                            img and isinstance(img, dict) and img.get("auditStatus") in [1, 2]  # 待审核、审核中
                            for img in images
                        ):
                            return ImageGenerationStatus.PROCESSING  # 还在审核中
                        else:
                            # 标记为成功但没有通过审核的图片，仍视为失败
                            return ImageGenerationStatus.FAILED
                    elif generate_status in [6, 7]:  # 失败、超时
                        return ImageGenerationStatus.FAILED
                    else:
                        self.logger.warning(f"Unknown generation status: {generate_status}")
                        return ImageGenerationStatus.PROCESSING

        except Exception as e:
            self.logger.error(f"Failed to check status: {e}")
            return ImageGenerationStatus.FAILED

    async def edit_image(self, request: ImageEditRequest) -> ImageEditResult:
        """
        使用Liblib Kontext编辑图片

        Args:
            request: 图片编辑请求

        Returns:
            ImageEditResult: 编辑结果
        """
        try:
            # 验证请求
            if not self.validate_edit_request(request):
                return await self._handle_edit_error(
                    InvalidRequestException("Invalid edit request parameters", self.provider),
                    request
                )

            start_time = time.time()

            # 构建请求数据
            # 从additional_params中获取aspect_ratio，或使用默认值
            aspect_ratio = "1:1"  # 默认值
            if request.additional_params and "aspect_ratio" in request.additional_params:
                aspect_ratio = request.additional_params["aspect_ratio"]

            generate_data = {
                "templateUuid": self.templates["img2img"],
                "generateParams": {
                    "model": "pro",
                    "prompt": request.prompt,
                    "aspectRatio": aspect_ratio,
                    "guidance_scale": request.guidance_scale or 3.5,
                    "imgCount": 1,  # 编辑通常只生成一张图
                    "image_list": [request.base_image_url]
                }
            }

            # 添加可选参数
            if request.negative_prompt:
                generate_data["generateParams"]["negative_prompt"] = request.negative_prompt

            if request.seed:
                generate_data["generateParams"]["seed"] = request.seed

            # 提交编辑任务
            url, params = self._build_api_url("/api/generate/kontext/img2img")

            self.logger.info(f"Submitting image edit task to Liblib: {request.prompt[:50]}...")

            async with aiohttp.ClientSession() as session:
                # 提交任务
                async with session.post(
                    url,
                    params=params,
                    json=generate_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:

                    if response.status != 200:
                        raise ImageGenerationException(
                            f"Failed to submit edit task: HTTP {response.status}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            str(response.status)
                        )

                    result = await response.json()

                    if result.get("code") != 0:
                        error_msg = result.get("msg", "Unknown error")
                        if "quota" in error_msg.lower() or "limit" in error_msg.lower():
                            raise QuotaExceededException(
                                f"Quota exceeded: {error_msg}",
                                ImageGenerationProvider.LIBLIB_KONTEXT,
                                "QUOTA_EXCEEDED"
                            )
                        raise ImageGenerationException(
                            f"Edit task submission failed: {error_msg}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            str(result.get("code"))
                        )

                    generation_uuid = result["data"]["generateUuid"]
                    self.logger.info(f"Edit task submitted successfully, UUID: {generation_uuid}")

                # 轮询任务状态
                return await self._poll_edit_status(
                    session, generation_uuid, request, start_time
                )

        except asyncio.TimeoutError:
            raise GenerationTimeoutException(
                f"Edit timeout after {self.config.timeout}s",
                ImageGenerationProvider.LIBLIB_KONTEXT,
                "TIMEOUT"
            )
        except Exception as e:
            if isinstance(e, ImageGenerationException):
                raise
            return await self._handle_edit_error(e, request)

    def validate_edit_request(self, request: ImageEditRequest) -> bool:
        """
        验证图片编辑请求参数（重写基类方法）

        Args:
            request: 图片编辑请求

        Returns:
            bool: 验证是否通过
        """
        # 调用基类验证
        if not super().validate_edit_request(request):
            return False

        # Liblib特定的验证
        guidance_scale = request.guidance_scale or 3.5
        if guidance_scale < 1.0 or guidance_scale > 20.0:
            self.logger.error("Guidance scale must be between 1.0 and 20.0")
            return False

        return True

    async def _poll_edit_status(
        self,
        session: aiohttp.ClientSession,
        generation_uuid: str,
        request: ImageEditRequest,
        start_time: float
    ) -> ImageEditResult:
        """
        轮询编辑状态

        Args:
            session: HTTP会话
            generation_uuid: 生成任务UUID
            request: 原始编辑请求
            start_time: 开始时间

        Returns:
            ImageEditResult: 编辑结果
        """
        status_url, status_params = self._build_api_url("/api/generate/status")
        status_data = {"generateUuid": generation_uuid}

        last_progress = 0
        audit_start_time = None
        audit_timeout = 120  # 审核超时时间：2分钟

        while True:
            current_time = time.time()
            if (current_time - start_time) > self.config.timeout:
                raise GenerationTimeoutException(
                    f"Edit timeout after {self.config.timeout}s",
                    ImageGenerationProvider.LIBLIB_KONTEXT,
                    "TIMEOUT"
                )

            try:
                async with session.post(
                    status_url,
                    params=status_params,
                    json=status_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:

                    if response.status != 200:
                        self.logger.warning(f"Status check failed with HTTP {response.status}")
                        await asyncio.sleep(self.poll_interval)
                        continue

                    result = await response.json()

                    if result.get("code") != 0:
                        error_msg = result.get("msg", "Unknown error")
                        self.logger.warning(f"API returned error: {error_msg}")
                        await asyncio.sleep(self.poll_interval)
                        continue

                    data = result.get("data", {})
                    generate_status = data.get("generateStatus", 0)
                    generate_msg = data.get("generateMsg", "")
                    percent_completed = data.get("percentCompleted", 0)

                    # 记录进度变化
                    if percent_completed > last_progress:
                        self.logger.info(f"Edit progress: {percent_completed:.1f}%")
                        last_progress = percent_completed

                    # 检查生成状态
                    if generate_status == 5:  # 成功
                        result = await self._handle_edit_completed(
                            data, generation_uuid, request, start_time
                        )
                        if result is not None:
                            return result
                    elif generate_status == 6:  # 失败
                        error_msg = generate_msg or "Edit failed"
                        raise ImageGenerationException(
                            f"Edit failed: {error_msg}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            str(generate_status)
                        )
                    elif generate_status == 7:  # 超时
                        error_msg = generate_msg or "Edit timeout (30 minutes)"
                        raise GenerationTimeoutException(
                            f"Edit timeout: {error_msg}",
                            ImageGenerationProvider.LIBLIB_KONTEXT,
                            "SERVER_TIMEOUT"
                        )
                    elif generate_status in [1, 2, 3, 4]:  # 等待执行、执行中、已生图、审核中
                        status_desc = {
                            1: "等待执行",
                            2: "执行中",
                            3: "已生图",
                            4: "审核中"
                        }.get(generate_status, "未知状态")

                        # 检查审核超时
                        if generate_status == 4:  # 审核中
                            if audit_start_time is None:
                                audit_start_time = current_time
                                self.logger.info("Edited images entered audit phase")
                            elif (current_time - audit_start_time) > audit_timeout:
                                raise GenerationTimeoutException(
                                    f"Audit timeout after {audit_timeout}s",
                                    ImageGenerationProvider.LIBLIB_KONTEXT,
                                    "AUDIT_TIMEOUT"
                                )

                        self.logger.debug(f"Edit task status: {status_desc} ({generate_status}), progress: {percent_completed:.1f}%")
                    else:
                        self.logger.warning(f"Unknown edit status: {generate_status}")

            except asyncio.TimeoutError:
                self.logger.warning("Status check timeout, retrying...")
            except Exception as e:
                self.logger.warning(f"Status check error: {e}, retrying...")

            await asyncio.sleep(self.poll_interval)

    async def _handle_edit_completed(
        self,
        data: Dict[str, Any],
        generation_uuid: str,
        request: ImageEditRequest,
        start_time: float
    ) -> Optional[ImageEditResult]:
        """
        处理编辑完成的结果

        Args:
            data: API返回的数据
            generation_uuid: 生成任务UUID
            request: 原始编辑请求
            start_time: 开始时间

        Returns:
            ImageEditResult: 编辑结果，如果还有图片在审核中则返回None
        """
        images = data.get("images", [])
        if not images:
            raise ImageGenerationException(
                "No images returned from edit task",
                ImageGenerationProvider.LIBLIB_KONTEXT,
                "NO_IMAGES"
            )

        # 过滤出审核通过的图片
        approved_images = []
        pending_audit = False

        for img in images:
            if not img or not isinstance(img, dict):
                continue

            audit_status = img.get("auditStatus", 0)

            if audit_status == 3:  # 审核通过
                image_url = img.get("imageUrl")
                if image_url:
                    approved_images.append(image_url)
            elif audit_status in [1, 2]:  # 待审核、审核中
                pending_audit = True
            # audit_status == 4 表示审核不通过，直接忽略

        # 如果还有图片在审核中，继续等待
        if pending_audit and not approved_images:
            return None

        # 如果没有任何图片通过审核
        if not approved_images:
            raise ImageGenerationException(
                "All edited images failed audit",
                ImageGenerationProvider.LIBLIB_KONTEXT,
                "AUDIT_FAILED"
            )

        generation_time = time.time() - start_time

        # 构建元数据
        # 从additional_params中获取aspect_ratio，或使用默认值
        aspect_ratio = "1:1"  # 默认值
        if request.additional_params and "aspect_ratio" in request.additional_params:
            aspect_ratio = request.additional_params["aspect_ratio"]

        metadata = {
            "generation_uuid": generation_uuid,
            "template_uuid": self.templates["img2img"],
            "generation_time": generation_time,
            "total_images": len(images),
            "approved_images": len(approved_images),
            "points_cost": data.get("pointsCost", 0),
            "request_params": {
                "prompt": request.prompt,
                "base_image_url": request.base_image_url,
                "aspect_ratio": aspect_ratio,
                "guidance_scale": request.guidance_scale or 3.5,
                "mask_image_url": request.mask_image_url,
                "negative_prompt": request.negative_prompt,
                "seed": request.seed
            },
            "api_response": data
        }

        self.logger.info(
            f"Edit completed: {len(approved_images)}/{len(images)} images passed audit "
            f"in {generation_time:.2f}s, cost: {metadata['points_cost']} points"
        )

        # 返回第一张图片的URL（编辑通常只需要一张图）
        image_url = approved_images[0] if approved_images else None

        return self._create_edit_success_result(
            image_url=image_url,
            request=request,
            generation_id=generation_uuid,
            metadata=metadata,
            generation_time=generation_time
        )

    async def _handle_edit_error(self, error: Exception, request: ImageEditRequest) -> ImageEditResult:
        """
        处理编辑错误

        Args:
            error: 异常对象
            request: 原始编辑请求

        Returns:
            ImageEditResult: 错误结果
        """
        # 使用基类的错误处理方法
        return await super()._handle_edit_error(error, request)
