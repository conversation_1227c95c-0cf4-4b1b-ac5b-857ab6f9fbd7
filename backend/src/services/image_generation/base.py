"""
文生图服务基类和数据模型
使用策略模式设计可扩展的文生图服务架构
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class ImageGenerationProvider(Enum):
    """文生图服务提供商枚举"""
    DASHSCOPE = "dashscope"
    LIBLIB_KONTEXT = "liblib_kontext"
    STABLE_DIFFUSION = "stable_diffusion"
    MIDJOURNEY = "midjourney"
    GITEE = "gitee"


class ImageGenerationStatus(Enum):
    """图片生成状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


@dataclass
class ImageGenerationRequest:
    """文生图请求数据模型"""
    prompt: str
    negative_prompt: Optional[str] = None
    width: int = 1024
    height: int = 1024
    num_images: int = 1
    quality: str = "high"  # draft, standard, high, premium
    style: Optional[str] = None
    seed: Optional[int] = None
    guidance_scale: Optional[float] = None
    steps: Optional[int] = None
    model: Optional[str] = None
    additional_params: Optional[Dict[str, Any]] = None


@dataclass
class ImageGenerationResult:
    """文生图结果数据模型"""
    success: bool
    image_urls: List[str]
    prompt_used: str
    provider: ImageGenerationProvider
    generation_id: Optional[str] = None
    status: ImageGenerationStatus = ImageGenerationStatus.COMPLETED
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    generation_time: Optional[float] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class ImageEditRequest:
    """图像编辑请求数据模型"""
    base_image_url: str
    prompt: str
    mask_image_url: Optional[str] = None
    negative_prompt: Optional[str] = None
    model: Optional[str] = None
    seed: Optional[int] = None
    strength: Optional[float] = None  # 编辑强度 (0.0-1.0)
    guidance_scale: Optional[float] = None
    steps: Optional[int] = None
    additional_params: Optional[Dict[str, Any]] = None


@dataclass
class ImageEditResult:
    """图像编辑结果数据模型"""
    success: bool
    image_url: Optional[str]
    original_image_url: str
    prompt_used: str
    provider: ImageGenerationProvider
    generation_id: Optional[str] = None
    status: ImageGenerationStatus = ImageGenerationStatus.COMPLETED
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    generation_time: Optional[float] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class ProviderConfig:
    """服务提供商配置"""
    provider: ImageGenerationProvider
    api_key: str
    api_secret: Optional[str] = None
    base_url: Optional[str] = None
    model: Optional[str] = None
    timeout: int = 300
    max_retries: int = 3
    additional_config: Optional[Dict[str, Any]] = None


class ImageGenerationService(ABC):
    """文生图服务抽象基类"""

    def __init__(self, config: ProviderConfig):
        """
        初始化文生图服务

        Args:
            config: 服务提供商配置
        """
        self.config = config
        self.provider = config.provider
        self.logger = logging.getLogger(f"{__name__}.{self.provider.value}")

    @abstractmethod
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """
        生成图片的抽象方法

        Args:
            request: 图片生成请求

        Returns:
            ImageGenerationResult: 生成结果
        """
        pass

    @abstractmethod
    async def check_status(self, generation_id: str) -> ImageGenerationStatus:
        """
        检查生成状态的抽象方法

        Args:
            generation_id: 生成任务ID

        Returns:
            ImageGenerationStatus: 生成状态
        """
        pass

    @abstractmethod
    def validate_request(self, request: ImageGenerationRequest) -> bool:
        """
        验证请求参数的抽象方法

        Args:
            request: 图片生成请求

        Returns:
            bool: 验证是否通过
        """
        pass

    async def edit_image(self, request: ImageEditRequest) -> ImageEditResult:
        """
        编辑图片的方法（默认实现抛出不支持异常）

        Args:
            request: 图片编辑请求

        Returns:
            ImageEditResult: 编辑结果

        Raises:
            NotImplementedError: 如果服务不支持图片编辑
        """
        raise NotImplementedError(f"Image editing is not supported by {self.provider.value}")

    def validate_edit_request(self, request: ImageEditRequest) -> bool:
        """
        验证图片编辑请求参数（默认实现）

        Args:
            request: 图片编辑请求

        Returns:
            bool: 验证是否通过
        """
        if not request.base_image_url or not request.prompt:
            return False
        return True

    def supports_image_editing(self) -> bool:
        """
        检查服务是否支持图片编辑

        Returns:
            bool: 是否支持图片编辑
        """
        try:
            # 尝试调用edit_image方法，如果抛出NotImplementedError则不支持
            import inspect
            method = getattr(self.__class__, 'edit_image', None)
            if method and method != ImageGenerationService.edit_image:
                return True
            return False
        except:
            return False
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取服务提供商信息
        
        Returns:
            Dict: 提供商信息
        """
        return {
            "provider": self.provider.value,
            "model": self.config.model,
            "timeout": self.config.timeout,
            "max_retries": self.config.max_retries
        }
    
    async def _handle_error(self, error: Exception, request: ImageGenerationRequest) -> ImageGenerationResult:
        """
        统一错误处理
        
        Args:
            error: 异常对象
            request: 原始请求
            
        Returns:
            ImageGenerationResult: 错误结果
        """
        error_message = str(error)
        self.logger.error(f"Image generation failed: {error_message}")
        
        return ImageGenerationResult(
            success=False,
            image_urls=[],
            prompt_used=request.prompt,
            provider=self.provider,
            status=ImageGenerationStatus.FAILED,
            error_message=error_message,
            metadata={"error_type": type(error).__name__}
        )
    
    def _create_success_result(
        self,
        image_urls: List[str],
        request: ImageGenerationRequest,
        generation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        generation_time: Optional[float] = None
    ) -> ImageGenerationResult:
        """
        创建成功结果

        Args:
            image_urls: 生成的图片URL列表
            request: 原始请求
            generation_id: 生成任务ID
            metadata: 元数据
            generation_time: 生成耗时

        Returns:
            ImageGenerationResult: 成功结果
        """
        return ImageGenerationResult(
            success=True,
            image_urls=image_urls,
            prompt_used=request.prompt,
            provider=self.provider,
            generation_id=generation_id,
            status=ImageGenerationStatus.COMPLETED,
            metadata=metadata or {},
            generation_time=generation_time
        )

    def _create_edit_success_result(
        self,
        image_url: str,
        request: ImageEditRequest,
        generation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        generation_time: Optional[float] = None
    ) -> ImageEditResult:
        """
        创建图片编辑成功结果

        Args:
            image_url: 编辑后的图片URL
            request: 原始编辑请求
            generation_id: 生成任务ID
            metadata: 元数据
            generation_time: 生成耗时

        Returns:
            ImageEditResult: 编辑成功结果
        """
        return ImageEditResult(
            success=True,
            image_url=image_url,
            original_image_url=request.base_image_url,
            prompt_used=request.prompt,
            provider=self.provider,
            generation_id=generation_id,
            status=ImageGenerationStatus.COMPLETED,
            metadata=metadata or {},
            generation_time=generation_time
        )

    async def _handle_edit_error(self, error: Exception, request: ImageEditRequest) -> ImageEditResult:
        """
        统一图片编辑错误处理

        Args:
            error: 异常对象
            request: 原始编辑请求

        Returns:
            ImageEditResult: 错误结果
        """
        error_message = str(error)
        self.logger.error(f"Image editing failed: {error_message}")

        return ImageEditResult(
            success=False,
            image_url=None,
            original_image_url=request.base_image_url,
            prompt_used=request.prompt,
            provider=self.provider,
            status=ImageGenerationStatus.FAILED,
            error_message=error_message,
            metadata={"error_type": type(error).__name__}
        )


class ImageGenerationException(Exception):
    """文生图服务异常基类"""
    
    def __init__(self, message: str, provider: ImageGenerationProvider, error_code: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class ProviderNotSupportedException(ImageGenerationException):
    """不支持的服务提供商异常"""
    pass


class InvalidRequestException(ImageGenerationException):
    """无效请求异常"""
    pass


class GenerationTimeoutException(ImageGenerationException):
    """生成超时异常"""
    pass


class QuotaExceededException(ImageGenerationException):
    """配额超限异常"""
    pass
