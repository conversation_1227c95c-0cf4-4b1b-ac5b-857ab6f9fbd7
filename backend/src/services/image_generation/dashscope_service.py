"""
Dashscope文生图服务实现
基于阿里云DashScope的Flux模型
支持文生图和图片编辑功能
"""

import asyncio
import functools
import time
import os
import aiohttp
from http import HTTPStatus
from typing import Dict, Any, List, Optional

from dashscope import ImageSynthesis

from .base import (
    ImageGenerationService,
    ImageGenerationRequest,
    ImageGenerationResult,
    ImageEditRequest,
    ImageEditResult,
    ImageGenerationStatus,
    ImageGenerationProvider,
    ProviderConfig,
    ImageGenerationException,
    InvalidRequestException,
    GenerationTimeoutException,
    QuotaExceededException
)





class DashscopeImageService(ImageGenerationService):
    """Dashscope文生图服务实现"""
    
    def __init__(self, config: ProviderConfig):
        """
        初始化Dashscope服务
        
        Args:
            config: Dashscope配置
        """
        super().__init__(config)
        
        # 默认模型配置
        self.default_model = config.model or "flux-dev"
        
        # 质量预设映射
        self.quality_presets = {
            "draft": {"model": "flux-dev", "steps": 20},
            "standard": {"model": "flux-dev", "steps": 30},
            "high": {"model": "flux-dev", "steps": 50},
            "premium": {"model": "flux-dev", "steps": 80}
        }
        
        # 支持的尺寸
        self.supported_sizes = [
            "1024*1024", "1024*1536", "1536*1024",
            "768*768", "768*1024", "1024*768"
        ]
    
    def validate_request(self, request: ImageGenerationRequest) -> bool:
        """
        验证Dashscope请求参数
        
        Args:
            request: 图片生成请求
            
        Returns:
            bool: 验证是否通过
        """
        # 检查prompt
        if not request.prompt or len(request.prompt.strip()) == 0:
            raise ValueError("Prompt cannot be empty")
        
        if len(request.prompt) > 2000:
            raise ValueError("Prompt too long (max 2000 characters)")
        
        # 检查尺寸
        size_str = f"{request.width}*{request.height}"
        if size_str not in self.supported_sizes:
            raise ValueError(f"Unsupported size: {size_str}. Supported: {self.supported_sizes}")
        
        # 检查数量
        if request.num_images < 1 or request.num_images > 4:
            raise ValueError("num_images must be between 1 and 4")
        
        # 检查质量预设
        if request.quality not in self.quality_presets:
            raise ValueError(f"Unsupported quality: {request.quality}. Supported: {list(self.quality_presets.keys())}")
        
        return True
    
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """
        使用Dashscope生成图片
        
        Args:
            request: 图片生成请求
            
        Returns:
            ImageGenerationResult: 生成结果
        """
        try:
            # 验证请求
            self.validate_request(request)
            
            start_time = time.time()
            
            # 获取质量预设参数
            quality_params = self.quality_presets.get(request.quality, self.quality_presets["high"])
            
            # 构建API参数
            api_params = {
                "api_key": self.config.api_key,
                "model": "wan2.2-t2i-plus",
                "prompt": request.prompt,
                "n": request.num_images,
                "size": f"{request.width}*{request.height}"
            }
            
            # 添加可选参数
            if request.negative_prompt:
                api_params["negative_prompt"] = request.negative_prompt
            
            if request.seed:
                api_params["seed"] = request.seed
            
            # 添加额外参数
            if request.additional_params:
                api_params.update(request.additional_params)
            
            self.logger.info(f"Generating image with Dashscope: {request.prompt[:50]}...")
            
            # 异步调用Dashscope API
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                functools.partial(ImageSynthesis.call, **api_params)
            )
            
            generation_time = time.time() - start_time
            
            # 处理响应
            if response.status_code == HTTPStatus.OK and response.output.results:
                image_urls = [result.url for result in response.output.results]
                
                metadata = {
                    "model": api_params["model"],
                    "size": api_params["size"],
                    "quality_preset": request.quality,
                    "generation_time": generation_time,
                    "api_response": {
                        "status_code": response.status_code,
                        "request_id": getattr(response, 'request_id', None)
                    }
                }
                
                self.logger.info(f"Successfully generated {len(image_urls)} images in {generation_time:.2f}s")
                
                return self._create_success_result(
                    image_urls=image_urls,
                    request=request,
                    metadata=metadata,
                    generation_time=generation_time
                )
            
            else:
                # 处理API错误
                error_msg = f"Dashscope API error: status_code={response.status_code}"
                if hasattr(response, 'message'):
                    error_msg += f", message={response.message}"

                # 检查是否是配额问题
                if response.status_code == 429:
                    raise QuotaExceededException(
                        "API quota exceeded",
                        ImageGenerationProvider.DASHSCOPE,
                        "QUOTA_EXCEEDED"
                    )

                # 检查是否是内容审核问题
                if (response.status_code == 400 and
                    hasattr(response, 'code') and
                    response.code == "DataInspectionFailed"):
                    raise InvalidRequestException(
                        "Content may contain inappropriate material and was rejected by content filter",
                        ImageGenerationProvider.DASHSCOPE,
                        "CONTENT_FILTERED"
                    )

                raise ImageGenerationException(
                    error_msg,
                    ImageGenerationProvider.DASHSCOPE,
                    str(response.status_code)
                )
                
        except asyncio.TimeoutError:
            raise GenerationTimeoutException(
                f"Generation timeout after {self.config.timeout}s",
                ImageGenerationProvider.DASHSCOPE,
                "TIMEOUT"
            )
        except Exception as e:
            if isinstance(e, ImageGenerationException):
                raise
            return await self._handle_error(e, request)
    
    async def check_status(self, generation_id: str) -> ImageGenerationStatus:
        """
        检查生成状态 (Dashscope是同步的，所以这里返回完成状态)
        
        Args:
            generation_id: 生成任务ID
            
        Returns:
            ImageGenerationStatus: 生成状态
        """
        # Dashscope是同步API，不需要状态检查
        return ImageGenerationStatus.COMPLETED
    
    def get_supported_models(self) -> List[str]:
        """
        获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型列表
        """
        return ["flux-dev", "flux-schnell","wanx2.1-t2i-plus"]
    
    def get_supported_sizes(self) -> List[str]:
        """
        获取支持的尺寸列表
        
        Returns:
            List[str]: 支持的尺寸列表
        """
        return self.supported_sizes.copy()
    
    def estimate_cost(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """
        估算生成成本
        
        Args:
            request: 图片生成请求
            
        Returns:
            Dict: 成本估算信息
        """
        # 这里可以根据实际的计费规则来计算
        base_cost = 0.1  # 假设每张图片0.1元
        quality_multiplier = {
            "draft": 0.5,
            "standard": 1.0,
            "high": 1.5,
            "premium": 2.0
        }
        
        multiplier = quality_multiplier.get(request.quality, 1.0)
        total_cost = base_cost * request.num_images * multiplier
        
        return {
            "estimated_cost": total_cost,
            "currency": "CNY",
            "base_cost_per_image": base_cost,
            "quality_multiplier": multiplier,
            "num_images": request.num_images
        }

    async def edit_image(self, request: ImageEditRequest) -> ImageEditResult:
        """
        使用Dashscope编辑图片

        Args:
            request: 图片编辑请求

        Returns:
            ImageEditResult: 编辑结果
        """
        try:
            # 验证请求参数
            if not self.validate_edit_request(request):
                return await self._handle_edit_error(
                    InvalidRequestException("Invalid edit request parameters", self.provider),
                    request
                )

            start_time = time.time()

            # 构建API参数
            api_params = {
                "api_key": self.config.api_key,
                "model": request.model or "wanx2.1-imageedit",
                "function": "description_edit",
                "prompt": request.prompt,
                "base_image_url": request.base_image_url,
                "n": 1
            }

            # 添加可选参数
            if request.mask_image_url:
                api_params["mask_image_url"] = request.mask_image_url

            if request.negative_prompt:
                api_params["negative_prompt"] = request.negative_prompt

            if request.seed:
                api_params["seed"] = request.seed

            if request.guidance_scale:
                api_params["guidance_scale"] = request.guidance_scale

            if request.steps:
                api_params["steps"] = request.steps

            # 添加额外参数
            if request.additional_params:
                api_params.update(request.additional_params)

            self.logger.info(f"Editing image with Dashscope: {request.prompt[:50]}...")
            self.logger.debug(f"API参数: {api_params}")

            # 异步调用Dashscope API
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                functools.partial(ImageSynthesis.call, **api_params)
            )

            generation_time = time.time() - start_time

            # 处理响应
            if response.status_code == HTTPStatus.OK and response.output.results:
                image_url = response.output.results[0].url

                metadata = {
                    "model": api_params["model"],
                    "function": api_params["function"],
                    "generation_time": generation_time,
                    "original_url": image_url,
                    "api_response": {
                        "status_code": response.status_code,
                        "request_id": getattr(response, 'request_id', None)
                    }
                }

                self.logger.info(f"Successfully edited image in {generation_time:.2f}s")

                return self._create_edit_success_result(
                    image_url=image_url,
                    request=request,
                    metadata=metadata,
                    generation_time=generation_time
                )

            else:
                # 处理API错误
                error_msg = f"Dashscope image edit error: status_code={response.status_code}"
                if hasattr(response, 'message'):
                    error_msg += f", message={response.message}"

                # 检查是否是配额问题
                if response.status_code == 429:
                    raise QuotaExceededException(
                        "API quota exceeded",
                        ImageGenerationProvider.DASHSCOPE,
                        "QUOTA_EXCEEDED"
                    )

                # 检查是否是内容审核问题
                if (response.status_code == 400 and
                    hasattr(response, 'code') and
                    response.code == "DataInspectionFailed"):
                    return await self._handle_edit_error(
                        InvalidRequestException(
                            "Content may contain inappropriate material and was rejected by content filter",
                            ImageGenerationProvider.DASHSCOPE,
                            "CONTENT_FILTERED"
                        ),
                        request
                    )

                return await self._handle_edit_error(
                    ImageGenerationException(error_msg, self.provider),
                    request
                )

        except asyncio.TimeoutError as e:
            return await self._handle_edit_error(
                GenerationTimeoutException(
                    f"Image edit timeout after {self.config.timeout}s",
                    self.provider
                ),
                request
            )
        except Exception as e:
            return await self._handle_edit_error(e, request)

    async def _download_and_save_image(self, image_url: str) -> str:
        """
        下载并保存图片到本地

        Args:
            image_url: 图片URL

        Returns:
            str: 本地图片路径
        """
        try:
            # 创建保存目录
            save_dir = "static/generated_images"
            os.makedirs(save_dir, exist_ok=True)

            # 生成文件名
            timestamp = int(time.time() * 1000)
            filename = f"edited_{timestamp}.png"
            local_path = os.path.join(save_dir, filename)

            # 下载图片
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        with open(local_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)

                        self.logger.info(f"Image saved to: {local_path}")
                        return local_path
                    else:
                        raise Exception(f"Failed to download image: HTTP {response.status}")

        except Exception as e:
            self.logger.error(f"Failed to download and save image: {e}")
            # 如果下载失败，返回原始URL
            return image_url
