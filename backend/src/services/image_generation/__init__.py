"""
文生图服务包
提供统一的文生图服务接口和多平台支持
"""

from .base import (
    ImageGenerationService,
    ImageGenerationProvider,
    ImageGenerationStatus,
    ImageGenerationRequest,
    ImageGenerationResult,
    ImageEditRequest,
    ImageEditResult,
    ProviderConfig,
    ImageGenerationException,
    ProviderNotSupportedException,
    InvalidRequestException,
    GenerationTimeoutException,
    QuotaExceededException
)

from .dashscope_service import DashscopeImageService
from .liblib_service import LiblibKontextService
from .factory import ImageGenerationServiceFactory, ImageGenerationManager

__all__ = [
    # 基础类和枚举
    "ImageGenerationService",
    "ImageGenerationProvider",
    "ImageGenerationStatus",
    "ImageGenerationRequest",
    "ImageGenerationResult",
    "ImageEditRequest",
    "ImageEditResult",
    "ProviderConfig",

    # 异常类
    "ImageGenerationException",
    "ProviderNotSupportedException",
    "InvalidRequestException",
    "GenerationTimeoutException",
    "QuotaExceededException",

    # 服务实现
    "DashscopeImageService",
    "LiblibKontextService",

    # 工厂和管理器
    "ImageGenerationServiceFactory",
    "ImageGenerationManager"
]

# 版本信息
__version__ = "1.0.0"
