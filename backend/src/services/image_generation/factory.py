"""
文生图服务工厂
使用工厂模式管理和创建不同的文生图服务实例
"""

from typing import Dict, Type, Optional, Any, List
import logging
import time
import asyncio
import aiohttp

from .base import (
    ImageGenerationService, 
    ImageGenerationProvider, 
    ProviderConfig,
    ProviderNotSupportedException
)
from .dashscope_service import DashscopeImageService
from .liblib_service import LiblibKontextService
from .gitee_service import GiteeImageService

logger = logging.getLogger(__name__)


class ImageGenerationServiceFactory:
    """文生图服务工厂类"""
    
    # 注册的服务提供商
    _providers: Dict[ImageGenerationProvider, Type[ImageGenerationService]] = {
        ImageGenerationProvider.DASHSCOPE: DashscopeImageService,
        ImageGenerationProvider.LIBLIB_KONTEXT: LiblibKontextService,
        ImageGenerationProvider.GITEE: GiteeImageService,
    }
    
    # 服务实例缓存
    _instances: Dict[str, ImageGenerationService] = {}
    _max_cache_size = 10  # 限制缓存大小
    _cache_access_times: Dict[str, float] = {}  # 追踪访问时间
    
    @classmethod
    def register_provider(
        cls, 
        provider: ImageGenerationProvider, 
        service_class: Type[ImageGenerationService]
    ) -> None:
        """
        注册新的服务提供商
        
        Args:
            provider: 服务提供商枚举
            service_class: 服务实现类
        """
        cls._providers[provider] = service_class
        logger.info(f"Registered provider: {provider.value} -> {service_class.__name__}")
    
    @classmethod
    def get_available_providers(cls) -> list[ImageGenerationProvider]:
        """
        获取所有可用的服务提供商
        
        Returns:
            list: 可用的服务提供商列表
        """
        return list(cls._providers.keys())
    
    @classmethod
    def create_service(cls, config: ProviderConfig) -> ImageGenerationService:
        """
        创建文生图服务实例
        
        Args:
            config: 服务提供商配置
            
        Returns:
            ImageGenerationService: 服务实例
            
        Raises:
            ProviderNotSupportedException: 不支持的服务提供商
        """
        provider = config.provider
        
        if provider not in cls._providers:
            raise ProviderNotSupportedException(
                f"Provider {provider.value} is not supported",
                provider
            )
        
        # 生成缓存键
        cache_key = cls._generate_cache_key(config)
        
        # 检查缓存
        if cache_key in cls._instances:
            logger.debug(f"Returning cached instance for {provider.value}")
            # 更新访问时间
            cls._cache_access_times[cache_key] = time.time()
            return cls._instances[cache_key]
        
        # 检查缓存大小限制
        if len(cls._instances) >= cls._max_cache_size:
            cls._cleanup_cache()
        
        # 创建新实例
        service_class = cls._providers[provider]
        try:
            instance = service_class(config)
            cls._instances[cache_key] = instance
            cls._cache_access_times[cache_key] = time.time()
            logger.info(f"Created new service instance: {provider.value}")
            return instance
        except Exception as e:
            logger.error(f"Failed to create service instance for {provider.value}: {e}")
            raise
    
    @classmethod
    def _generate_cache_key(cls, config: ProviderConfig) -> str:
        """
        生成缓存键
        
        Args:
            config: 服务配置
            
        Returns:
            str: 缓存键
        """
        # 使用提供商、API密钥的哈希值作为缓存键
        import hashlib
        
        key_parts = [
            config.provider.value,
            config.api_key[:8] if config.api_key else "",  # 只使用前8位避免泄露
            config.model or "",
            str(config.timeout)
        ]
        
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    @classmethod
    def _cleanup_cache(cls) -> None:
        """清理最少使用的缓存条目"""
        if not cls._cache_access_times:
            return
            
        # 找到最少使用的条目
        lru_key = min(cls._cache_access_times.items(), key=lambda x: x[1])[0]
        if lru_key in cls._instances:
            del cls._instances[lru_key]
            del cls._cache_access_times[lru_key]
            logger.info(f"Cleaned up cache entry: {lru_key}")
    
    @classmethod
    def clear_cache(cls) -> None:
        """清空服务实例缓存"""
        cls._instances.clear()
        cls._cache_access_times.clear()
        logger.info("Service instance cache cleared")
    
    @classmethod
    def get_service_info(cls, provider: ImageGenerationProvider) -> Dict[str, Any]:
        """
        获取服务提供商信息
        
        Args:
            provider: 服务提供商
            
        Returns:
            Dict: 服务信息
        """
        if provider not in cls._providers:
            raise ProviderNotSupportedException(
                f"Provider {provider.value} is not supported",
                provider
            )
        
        service_class = cls._providers[provider]
        
        return {
            "provider": provider.value,
            "service_class": service_class.__name__,
            "module": service_class.__module__,
            "supported": True
        }


class ImageGenerationManager:
    """文生图服务管理器"""
    
    def __init__(self, default_provider: Optional[ImageGenerationProvider] = None):
        """
        初始化管理器
        
        Args:
            default_provider: 默认服务提供商
        """
        self.default_provider = default_provider or ImageGenerationProvider.DASHSCOPE
        self.factory = ImageGenerationServiceFactory()
        self._configs: Dict[ImageGenerationProvider, ProviderConfig] = {}
        self._session_pool = None
        self._max_concurrent_requests = 5
        
        logger.info(f"ImageGenerationManager initialized with default provider: {self.default_provider.value}")
    
    async def __aenter__(self):
        """初始化连接池"""
        self._session_pool = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=300),
            connector=aiohttp.TCPConnector(limit=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """清理连接池"""
        if self._session_pool:
            await self._session_pool.close()
    
    def add_provider_config(self, config: ProviderConfig) -> None:
        """
        添加服务提供商配置
        
        Args:
            config: 服务提供商配置
        """
        self._configs[config.provider] = config
        logger.info(f"Added config for provider: {config.provider.value}")
    
    def get_service(self, provider: Optional[ImageGenerationProvider] = None) -> ImageGenerationService:
        """
        获取文生图服务实例
        
        Args:
            provider: 服务提供商，如果为None则使用默认提供商
            
        Returns:
            ImageGenerationService: 服务实例
        """
        target_provider = provider or self.default_provider
        
        if target_provider not in self._configs:
            raise ValueError(f"No configuration found for provider: {target_provider.value}")
        
        config = self._configs[target_provider]
        return self.factory.create_service(config)
    
    def set_default_provider(self, provider: ImageGenerationProvider) -> None:
        """
        设置默认服务提供商
        
        Args:
            provider: 服务提供商
        """
        if provider not in self._configs:
            raise ValueError(f"No configuration found for provider: {provider.value}")
        
        self.default_provider = provider
        logger.info(f"Default provider changed to: {provider.value}")
    
    def get_available_providers(self) -> list[ImageGenerationProvider]:
        """
        获取已配置的服务提供商列表
        
        Returns:
            list: 已配置的服务提供商列表
        """
        return list(self._configs.keys())
    
    def health_check(self) -> Dict[ImageGenerationProvider, bool]:
        """
        检查所有配置的服务提供商健康状态
        
        Returns:
            Dict: 各提供商的健康状态
        """
        health_status = {}
        
        for provider in self._configs:
            try:
                service = self.get_service(provider)
                # 这里可以添加实际的健康检查逻辑
                health_status[provider] = True
                logger.debug(f"Health check passed for {provider.value}")
            except Exception as e:
                health_status[provider] = False
                logger.warning(f"Health check failed for {provider.value}: {e}")
        
        return health_status
    
    def get_provider_capabilities(self, provider: ImageGenerationProvider) -> Dict[str, Any]:
        """
        获取服务提供商能力信息
        
        Args:
            provider: 服务提供商
            
        Returns:
            Dict: 能力信息
        """
        try:
            service = self.get_service(provider)
            
            capabilities = {
                "provider": provider.value,
                "provider_info": service.get_provider_info()
            }
            
            # 如果服务支持，添加更多能力信息
            if hasattr(service, 'get_supported_models'):
                capabilities["supported_models"] = service.get_supported_models()
            
            if hasattr(service, 'get_supported_sizes'):
                capabilities["supported_sizes"] = service.get_supported_sizes()
            
            return capabilities
            
        except Exception as e:
            logger.error(f"Failed to get capabilities for {provider.value}: {e}")
            return {
                "provider": provider.value,
                "error": str(e)
            }
    
    async def generate_batch(self, requests: List[Any]) -> List[Any]:
        """批量处理多个图像生成请求"""
        semaphore = asyncio.Semaphore(self._max_concurrent_requests)
        
        async def process_single_request(request: Any) -> Any:
            async with semaphore:
                service = self.get_service()
                return await service.generate_image(request)
        
        tasks = [process_single_request(req) for req in requests]
        return await asyncio.gather(*tasks, return_exceptions=True)
