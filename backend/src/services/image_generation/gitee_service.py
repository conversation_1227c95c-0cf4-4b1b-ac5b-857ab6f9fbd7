"""
Gitee AI图像生成服务实现
基于OpenAI兼容API接口的Gitee AI图像生成服务
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
import logging
from openai import OpenAI
import requests

from .base import (
    ImageGenerationService,
    ImageGenerationRequest,
    ImageGenerationResult,
    ImageGenerationProvider,
    ImageGenerationStatus,
    ProviderConfig,
    ImageGenerationException
)

logger = logging.getLogger(__name__)


class GiteeImageService(ImageGenerationService):
    """Gitee AI图像生成服务实现"""
    
    def __init__(self, config: ProviderConfig):
        """
        初始化Gitee图像生成服务
        
        Args:
            config: 服务提供商配置
        """
        super().__init__(config)
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url=config.base_url or "https://ai.gitee.com/v1",
            api_key=config.api_key,
        )
        
        # 默认模型
        self.default_model = config.model or "FLUX_1-Krea-dev"
        
        # 支持的尺寸
        self.supported_sizes = [
            "512x512", "768x768", "1024x1024", 
            "1280x720", "720x1280", "1344x768", "768x1344"
        ]
        
        # 支持的模型
        self.supported_models = [
            "FLUX_1-Krea-dev",
            "FLUX_1-schnell",
            "FLUX_1-dev",
            "FLUX_1-pro"
        ]
        
        logger.info(f"GiteeImageService initialized with model: {self.default_model}")
    
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """
        生成图片
        
        Args:
            request: 图片生成请求
            
        Returns:
            ImageGenerationResult: 生成结果
        """
        start_time = time.time()
        
        try:
            # 验证请求
            if not self.validate_request(request):
                return self._create_error_result(
                    "Invalid request parameters", 
                    request
                )
            
            # 准备生成参数
            model = request.model or self.default_model
            size = f"{request.width}x{request.height}"
            
            # 构建额外参数
            extra_body = {}
            extra_body["guidance_scale"] = 4.5
            extra_body["num_inference_steps"] = 28
            extra_body["seed"] = 42
            
            # 添加其他额外参数
            if request.additional_params:
                extra_body.update(request.additional_params)
            
            logger.info(f"Generating image with Gitee AI: {request.prompt[:50]}...")
            
            # 调用Gitee API生成图片
            logger.info(f"Calling Gitee API with: model={model}, size={size}, n={request.num_images}")
            logger.info(f"Extra body: {extra_body}")
            
            response = self.client.images.generate(
                prompt=request.prompt,
                model=model,
                size=size,
                n=request.num_images,
                response_format="url",
                extra_body=extra_body if extra_body else None
            )
            
            # 提取图片URL - Gitee返回结构: {"created": 1, "data": [{"url": "string"}]}
            image_urls = []
            logger.info(f"Gitee API response type: {type(response)}")
            logger.info(f"Gitee API response: {response}")
            
            # 尝试多种方式提取URL
            try:
                # 方法1：直接访问response.data
                if hasattr(response, 'data') and response.data:
                    for data in response.data:
                        if hasattr(data, 'url') and data.url:
                            image_urls.append(data.url)
                            logger.info(f"Method 1 - Added image URL: {data.url}")
                
                # 方法2：如果方法1失败，尝试访问response.data[0].url
                if not image_urls and hasattr(response, 'data') and len(response.data) > 0:
                    first_item = response.data[0]
                    if hasattr(first_item, 'url') and first_item.url:
                        image_urls.append(first_item.url)
                        logger.info(f"Method 2 - Added image URL: {first_item.url}")
                
                # 方法3：尝试直接从response对象获取
                if not image_urls:
                    try:
                        # 转换为字典格式
                        response_dict = response.model_dump()
                        logger.info(f"Response as dict: {response_dict}")
                        
                        if 'data' in response_dict and response_dict['data']:
                            for item in response_dict['data']:
                                if 'url' in item and item['url']:
                                    image_urls.append(item['url'])
                                    logger.info(f"Method 3 - Added image URL: {item['url']}")
                    except Exception as e:
                        logger.warning(f"Method 3 failed: {e}")
                
            except Exception as e:
                logger.error(f"Error extracting image URLs: {e}")
            
            logger.info(f"Total image URLs extracted: {len(image_urls)}")
            logger.info(f"Image URLs: {image_urls}")
            
            generation_time = time.time() - start_time
            
            if not image_urls:
                return self._create_error_result(
                    "No images generated", 
                    request,
                    generation_time
                )
            
            # 构建元数据
            metadata = {
                "model": model,
                "size": size,
                "num_images": request.num_images,
                "guidance_scale": request.guidance_scale,
                "steps": request.steps,
                "seed": request.seed,
                "provider_params": extra_body
            }
            
            logger.info(f"Successfully generated {len(image_urls)} images in {generation_time:.2f}s")
            
            return self._create_success_result(
                image_urls=image_urls,
                request=request,
                metadata=metadata,
                generation_time=generation_time
            )
            
        except Exception as e:
            generation_time = time.time() - start_time
            logger.error(f"Gitee image generation failed: {str(e)}")
            return self._create_error_result(
                str(e),
                request,
                generation_time
            )
    
    async def check_status(self, generation_id: str) -> ImageGenerationStatus:
        """
        检查生成状态（Gitee API是同步的，直接返回完成状态）
        
        Args:
            generation_id: 生成任务ID
            
        Returns:
            ImageGenerationStatus: 生成状态
        """
        # Gitee API是同步的，所以直接返回完成状态
        return ImageGenerationStatus.COMPLETED
    
    def validate_request(self, request: ImageGenerationRequest) -> bool:
        """
        验证请求参数
        
        Args:
            request: 图片生成请求
            
        Returns:
            bool: 验证是否通过
        """
        # 检查必要参数
        if not request.prompt or not request.prompt.strip():
            logger.error("Prompt is required")
            return False
        
        # 检查尺寸
        size = f"{request.width}x{request.height}"
        if size not in self.supported_sizes:
            logger.error(f"Unsupported size: {size}")
            return False
        
        # 检查模型
        if request.model and request.model not in self.supported_models:
            logger.error(f"Unsupported model: {request.model}")
            return False
        
        # 检查图片数量
        if request.num_images < 1 or request.num_images > 4:
            logger.error(f"Number of images must be between 1 and 4, got: {request.num_images}")
            return False
        
        # 检查参数范围
        if request.guidance_scale is not None:
            if request.guidance_scale < 1.0 or request.guidance_scale > 20.0:
                logger.error(f"Guidance scale must be between 1.0 and 20.0, got: {request.guidance_scale}")
                return False
        
        if request.steps is not None:
            if request.steps < 1 or request.steps > 100:
                logger.error(f"Steps must be between 1 and 100, got: {request.steps}")
                return False
        
        return True
    
    def get_supported_models(self) -> List[str]:
        """
        获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型列表
        """
        return self.supported_models
    
    def get_supported_sizes(self) -> List[str]:
        """
        获取支持的图片尺寸列表
        
        Returns:
            List[str]: 支持的尺寸列表
        """
        return self.supported_sizes
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        获取服务提供商信息
        
        Returns:
            Dict[str, Any]: 提供商信息
        """
        base_info = super().get_provider_info()
        base_info.update({
            "supported_models": self.supported_models,
            "supported_sizes": self.supported_sizes,
            "default_model": self.default_model,
            "api_type": "openai-compatible"
        })
        return base_info
    
    def _create_error_result(
        self, 
        error_message: str, 
        request: ImageGenerationRequest,
        generation_time: Optional[float] = None
    ) -> ImageGenerationResult:
        """
        创建错误结果
        
        Args:
            error_message: 错误消息
            request: 原始请求
            generation_time: 生成耗时
            
        Returns:
            ImageGenerationResult: 错误结果
        """
        return ImageGenerationResult(
            success=False,
            image_urls=[],
            prompt_used=request.prompt,
            provider=self.provider,
            status=ImageGenerationStatus.FAILED,
            error_message=error_message,
            metadata={
                "model": request.model or self.default_model,
                "size": f"{request.width}x{request.height}",
                "generation_time": generation_time
            }
        )