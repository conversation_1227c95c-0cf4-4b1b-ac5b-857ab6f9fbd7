"""
文生图服务配置管理器
负责初始化和管理各个文生图服务的配置
"""

import logging
from typing import Optional

from ...core.config import settings
from .base import ImageGenerationProvider, ProviderConfig
from .factory import ImageGenerationManager

logger = logging.getLogger(__name__)


class ImageGenerationConfigManager:
    """文生图服务配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.manager = ImageGenerationManager()
        self._initialized = False
    
    def initialize(self) -> ImageGenerationManager:
        """
        初始化所有可用的文生图服务配置
        
        Returns:
            ImageGenerationManager: 配置好的管理器实例
        """
        if self._initialized:
            return self.manager
        
        logger.info("Initializing image generation services...")
        
        # 初始化Dashscope配置
        self._init_dashscope_config()
        
        # 初始化Liblib配置
        self._init_liblib_config()
        
        # 初始化Gitee配置
        self._init_gitee_config()
        
        # 设置默认提供商
        self._set_default_provider()
        
        self._initialized = True
        logger.info("Image generation services initialized successfully")
        
        return self.manager
    
    def _init_dashscope_config(self) -> None:
        """初始化Dashscope配置"""
        try:
            # 使用qwen_api_key作为dashscope的API密钥（兼容现有配置）
            api_key = settings.dashscope_api_key or settings.qwen_api_key
            
            if api_key:
                config = ProviderConfig(
                    provider=ImageGenerationProvider.DASHSCOPE,
                    api_key=api_key,
                    model=settings.dashscope_model,
                    timeout=300,
                    max_retries=3,
                    additional_config={
                        "base_url": "https://dashscope.aliyuncs.com"
                    }
                )
                
                self.manager.add_provider_config(config)
                logger.info("Dashscope configuration added successfully")
            else:
                logger.warning("Dashscope API key not found, skipping Dashscope configuration")
                
        except Exception as e:
            logger.error(f"Failed to initialize Dashscope config: {e}")
    
    def _init_liblib_config(self) -> None:
        """初始化Liblib配置"""
        try:
            if settings.liblib_ak and settings.liblib_sk:
                config = ProviderConfig(
                    provider=ImageGenerationProvider.LIBLIB_KONTEXT,
                    api_key=settings.liblib_ak,
                    api_secret=settings.liblib_sk,
                    base_url=settings.liblib_base_url,
                    model="pro",
                    timeout=600,  # Liblib需要更长的超时时间
                    max_retries=3,
                    additional_config={
                        "poll_interval": 5,
                        "templates": {
                            "text2img": "fe9928fde1b4491c9b360dd24aa2b115",
                            "img2img": "1c0a9712b3d84e1b8a9f49514a46d88c"
                        }
                    }
                )
                
                self.manager.add_provider_config(config)
                logger.info("Liblib Kontext configuration added successfully")
            else:
                logger.warning("Liblib AK/SK not found, skipping Liblib configuration")
                
        except Exception as e:
            logger.error(f"Failed to initialize Liblib config: {e}")
    
    def _init_gitee_config(self) -> None:
        """初始化Gitee配置"""
        try:
            if hasattr(settings, 'gitee_api_key') and settings.gitee_api_key:
                config = ProviderConfig(
                    provider=ImageGenerationProvider.GITEE,
                    api_key=settings.gitee_api_key,
                    base_url=getattr(settings, 'gitee_base_url', "https://ai.gitee.com/v1"),
                    model=getattr(settings, 'gitee_model', "FLUX_1-Krea-dev"),
                    timeout=300,
                    max_retries=3,
                    additional_config={
                        "api_type": "openai-compatible"
                    }
                )
                
                self.manager.add_provider_config(config)
                logger.info("Gitee configuration added successfully")
            else:
                logger.warning("Gitee API key not found, skipping Gitee configuration")
                
        except Exception as e:
            logger.error(f"Failed to initialize Gitee config: {e}")
    
    def _set_default_provider(self) -> None:
        """设置默认服务提供商"""
        try:
            available_providers = self.manager.get_available_providers()
            
            if not available_providers:
                logger.error("No image generation providers configured!")
                return
            
            # 根据配置设置默认提供商
            default_provider_name = settings.default_image_provider.lower()
            
            provider_mapping = {
                "dashscope": ImageGenerationProvider.DASHSCOPE,
                "liblib_kontext": ImageGenerationProvider.LIBLIB_KONTEXT,
                "liblib": ImageGenerationProvider.LIBLIB_KONTEXT,  # 别名
                "gitee": ImageGenerationProvider.GITEE,
            }
            
            default_provider = provider_mapping.get(default_provider_name)
            
            if default_provider and default_provider in available_providers:
                self.manager.set_default_provider(default_provider)
                logger.info(f"Default image generation provider set to: {default_provider.value}")
            else:
                # 使用第一个可用的提供商作为默认
                fallback_provider = available_providers[0]
                self.manager.set_default_provider(fallback_provider)
                logger.warning(f"Configured default provider '{default_provider_name}' not available, "
                             f"using fallback: {fallback_provider.value}")
                
        except Exception as e:
            logger.error(f"Failed to set default provider: {e}")
    
    def get_manager(self) -> ImageGenerationManager:
        """
        获取配置好的管理器实例
        
        Returns:
            ImageGenerationManager: 管理器实例
        """
        if not self._initialized:
            return self.initialize()
        return self.manager
    
    def health_check(self) -> dict:
        """
        执行健康检查
        
        Returns:
            dict: 健康检查结果
        """
        if not self._initialized:
            self.initialize()
        
        health_status = self.manager.health_check()
        
        # 转换为更友好的格式
        result = {
            "overall_status": "healthy" if any(health_status.values()) else "unhealthy",
            "providers": {}
        }
        
        for provider, status in health_status.items():
            result["providers"][provider.value] = {
                "status": "healthy" if status else "unhealthy",
                "capabilities": self.manager.get_provider_capabilities(provider) if status else None
            }
        
        return result
    
    def get_provider_info(self) -> dict:
        """
        获取所有提供商信息
        
        Returns:
            dict: 提供商信息
        """
        if not self._initialized:
            self.initialize()
        
        available_providers = self.manager.get_available_providers()
        default_provider = self.manager.default_provider
        
        return {
            "default_provider": default_provider.value,
            "available_providers": [p.value for p in available_providers],
            "total_providers": len(available_providers),
            "provider_details": {
                provider.value: self.manager.get_provider_capabilities(provider)
                for provider in available_providers
            }
        }


# 全局配置管理器实例
_config_manager: Optional[ImageGenerationConfigManager] = None


def get_image_generation_manager() -> ImageGenerationManager:
    """
    获取全局文生图服务管理器实例
    
    Returns:
        ImageGenerationManager: 管理器实例
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ImageGenerationConfigManager()
    
    return _config_manager.get_manager()


def get_config_manager() -> ImageGenerationConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        ImageGenerationConfigManager: 配置管理器实例
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ImageGenerationConfigManager()
    
    return _config_manager
