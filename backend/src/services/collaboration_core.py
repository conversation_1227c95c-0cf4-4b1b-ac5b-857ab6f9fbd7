"""
Multi-Agent Collaboration Core Components.

This module implements the core orchestration and coordination logic for multi-agent collaboration,
including task decomposition, agent coordination, and result integration.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from uuid import uuid4

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from .base_agents import (
    CollaborationType, AgentTask, AgentResult, SharedContext,
    DesignArtifact, TaskStatus, BaseDesignAgent,
    ITaskDecomposer, IAgentCoordinator, IResultIntegrator
)
from .agent_communication import CommunicationManager
from .websocket_manager import get_design_broadcaster
from ..core.config import settings

logger = logging.getLogger(__name__)


# ============================================================================
# Task Decomposition
# ============================================================================

class TaskDecomposer(ITaskDecomposer):
    """Intelligent task decomposer using LLM."""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            base_url=settings.qwen_base_url,
            api_key=settings.qwen_api_key,
            model=settings.qwen_model,
            temperature=0.3,  # Lower temperature for more structured output
        )
    
    async def decompose_request(
        self,
        user_request: str,
        collaboration_type: CollaborationType,
        available_agents: List[str],
        conversation_history: Optional[List[Dict[str, str]]] = None,
        previous_requirements: Optional[Dict[str, Any]] = None
    ) -> List[AgentTask]:
        """Decompose user request into structured agent tasks."""
        
        # 构建上下文信息
        context_info = ""
        if conversation_history:
            context_info += "\\n对话历史：\\n"
            for msg in conversation_history[-5:]:  # 只取最近5条消息
                role = msg.get("role", "unknown")
                content = msg.get("content", "")[:100]  # 限制长度
                context_info += f"- {role}: {content}...\\n"

        if previous_requirements:
            context_info += f"\\n之前的需求：{previous_requirements}\\n"

        decomposition_prompt = f"""
        你是一个智能任务分析器，负责分析设计需求并选择合适的Agent来处理。
        你需要理解多轮对话的上下文，判断用户的真实意图，并直接进行设计生成。

        当前用户输入：{user_request}
        {context_info}

        可用Agent类型：{', '.join(available_agents)}

        Agent能力说明：
        - FashionDesignAgent: 专门处理服装、时装设计，直接生成设计作品
        - PosterDesignAgent: 专门处理海报、宣传品设计，直接生成设计作品
        - LogoDesignAgent: 专门处理Logo、品牌标识设计，直接生成设计作品
        - DramaProductionAgent: 专门处理剧本创作、角色设计、场景规划、视频制作，直接生成制作方案

        核心分析原则：
        1. **直接生成策略**：所有设计需求都直接进入生成阶段，无需确认步骤
        2. **意图识别优先**：准确识别用户的设计意图和目标领域
        3. **独立任务处理**：每个新的设计需求都作为独立任务处理
        4. **智能依赖管理**：只在明确需要协作时才设置任务依赖关系
        5. **上下文智能应用**：只在用户明确提及修改或基于之前设计时才使用历史信息

        设计需求分类：
        🎨 **服装设计**：连衣裙、衬衫、裤子、外套、裙子、时装、服饰等
        📄 **海报设计**：宣传海报、广告、宣传品、活动海报、产品海报等
        🏷️ **Logo设计**：品牌标识、公司Logo、商标、标志设计等
        🎬 **剧情制作**：剧本、剧情、电影、短片、视频、角色、场景、故事等

        智能任务规划策略：
        - **单一设计需求** → 创建一个对应的Agent任务
        - **明确单项需求** → 只创建用户明确要求的设计任务
        - **品牌整体设计** → 只在用户明确要求"品牌视觉"、"整套设计"时创建多个任务
        - **修改现有设计** → 使用对应Agent的修改功能
        - **跨领域协作** → 只在用户明确要求多个设计时设置依赖关系

        示例分析（修正后）：
        ✅ "帮我设计一个海报" → PosterDesignAgent直接生成
        ✅ "设计一个Logo" → LogoDesignAgent直接生成（单一任务）
        ✅ "设计一件连衣裙" → FashionDesignAgent直接生成
        ✅ "写一个爱情剧本" → DramaProductionAgent直接生成
        ✅ "设计一个短片" → DramaProductionAgent直接生成
        ✅ "为公司设计品牌视觉" → Logo → 海报的任务链（明确的整体需求）
        ✅ "设计Logo和海报" → Logo → 海报的任务链（明确的多项需求）
        ✅ "把这个Logo改成蓝色" → LogoDesignAgent修改任务
        ✅ "基于这个Logo设计海报" → PosterDesignAgent，引用Logo信息

        请以JSON格式返回任务分解结果：
        {{
            "tasks": [
                {{
                    "description": "清晰的任务描述，专注于用户的具体需求",
                    "agent_type": "最适合的Agent类型",
                    "dependencies": [],
                    "expected_output": "预期的设计输出",
                    "priority": 10,
                    "requirements": {{
                        "user_input": "{user_request}",
                        "task_type": "design_generation",
                        "context_aware": true,
                        "direct_generation": true,
                        "conversation_context": "智能上下文理解"
                    }}
                }}
            ]
        }}

        🎯 **Agent选择指南**：
        - 🎨 服装/时装/衣服 → FashionDesignAgent
        - 📄 海报/宣传/广告 → PosterDesignAgent
        - 🏷️ Logo/标识/品牌 → LogoDesignAgent
        - 🎬 剧本/剧情/电影/视频/角色/场景 → DramaProductionAgent

        🚀 **任务生成策略**：
        - **直接生成**：所有新设计需求都直接进入生成阶段
        - **按需创建**：严格按照用户明确要求创建任务，不自动添加额外任务
        - **依赖管理**：使用数字索引表示任务依赖（0, 1, 2...）
        - **上下文感知**：只在明确修改或引用时使用历史信息

        🔧 **特殊处理规则**：
        - 单一Logo设计 → 只创建LogoDesignAgent任务
        - 单一海报设计 → 只创建PosterDesignAgent任务
        - 单一服装设计 → 只创建FashionDesignAgent任务
        - 单一剧情制作 → 只创建DramaProductionAgent任务
        - 品牌整体设计 → 创建完整的品牌视觉任务链（用户明确要求时）
        - 多项明确需求 → 创建对应的多个任务并设置依赖关系
        - 修改请求 → 使用对应Agent的修改功能

        ⚡ **优化重点**：
        - 减少用户等待时间，直接开始设计生成
        - 精确理解用户意图，严格按需创建任务
        - 避免过度设计，不自动添加用户未要求的任务
        - 只在用户明确要求多项设计时才创建协作任务
        - 保持任务描述清晰简洁，便于Agent理解执行
        """
        
        try:
            response = await self.llm.ainvoke([HumanMessage(content=decomposition_prompt)])

            # 记录LLM的原始输出
            logger.info(f"LLM分析结果: {response.content}")

            # Try to parse JSON response
            try:
                task_data = json.loads(response.content)
                logger.info(f"成功解析JSON: {task_data}")
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract JSON from the response
                import re
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    task_data = json.loads(json_match.group())
                    logger.info(f"从文本中提取JSON成功: {task_data}")
                else:
                    logger.error(f"无法从LLM输出中提取JSON: {response.content}")
                    raise ValueError("No valid JSON found in response")

            tasks = []
            task_id_mapping = {}  # 用于映射索引到任务ID

            # 第一轮：创建所有任务并建立ID映射
            for task_index, task_info in enumerate(task_data.get("tasks", [])):
                task_id = str(uuid4())
                task_id_mapping[task_index] = task_id

                task = AgentTask(
                    id=task_id,
                    agent_type=task_info.get("agent_type", available_agents[0] if available_agents else "FashionDesignAgent"),
                    description=task_info.get("description", user_request),
                    requirements=task_info.get("requirements", {"user_input": user_request}),
                    dependencies=[],  # 暂时为空，第二轮处理
                    priority=task_info.get("priority", 5),
                    expected_output=task_info.get("expected_output", "Design result"),
                    timeout=task_info.get("timeout"),
                    status=TaskStatus.PENDING
                )
                tasks.append(task)
                logger.info(f"创建任务: {task['agent_type']} - {task['description'][:50]}...")

            # 第二轮：处理dependencies
            for task_index, task_info in enumerate(task_data.get("tasks", [])):
                task_dependencies = task_info.get("dependencies", [])
                if not isinstance(task_dependencies, list):
                    continue

                valid_dependencies = []
                for dep in task_dependencies:
                    if isinstance(dep, int) and dep in task_id_mapping:
                        # 数字索引依赖，转换为任务ID
                        valid_dependencies.append(task_id_mapping[dep])
                    elif isinstance(dep, str) and any(t["id"] == dep for t in tasks):
                        # 字符串ID依赖，验证存在性
                        valid_dependencies.append(dep)

                # 更新任务的dependencies
                if task_index < len(tasks):
                    tasks[task_index]["dependencies"] = valid_dependencies

            logger.info(f"Decomposed request into {len(tasks)} tasks")
            return tasks

        except Exception as e:
            logger.error(f"Task decomposition failed: {e}")
            # Fallback to simple single-agent task
            return [AgentTask(
                id=str(uuid4()),
                agent_type=available_agents[0] if available_agents else "FashionDesignAgent",
                description=user_request,
                requirements={"user_input": user_request, "task_type": "design_generation"},
                dependencies=[],
                priority=5,
                expected_output="Design result",
                timeout=None,
                status=TaskStatus.PENDING
            )]


# ============================================================================
# Agent Coordination
# ============================================================================

class AgentCoordinator(IAgentCoordinator):
    """Coordinates multi-agent task execution."""
    
    def __init__(self, agent_pool: Dict[str, BaseDesignAgent]):
        self.agent_pool = agent_pool
        self.execution_strategies = {
            CollaborationType.SEQUENTIAL: self._execute_sequential,
            CollaborationType.PARALLEL: self._execute_parallel,
            CollaborationType.HIERARCHICAL: self._execute_hierarchical,
            CollaborationType.PEER_REVIEW: self._execute_peer_review
        }
    
    async def execute_tasks(
        self,
        tasks: List[AgentTask],
        collaboration_type: CollaborationType,
        shared_context: SharedContext
    ) -> List[AgentResult]:
        """Execute tasks using specified collaboration pattern."""
        
        strategy = self.execution_strategies.get(collaboration_type)
        if not strategy:
            raise ValueError(f"Unsupported collaboration type: {collaboration_type}")
        
        logger.info(f"Executing {len(tasks)} tasks using {collaboration_type} strategy")
        return await strategy(tasks, shared_context)
    
    async def _execute_sequential(
        self,
        tasks: List[AgentTask],
        shared_context: SharedContext
    ) -> List[AgentResult]:
        """Execute tasks sequentially based on dependencies."""
        
        results = []
        completed_tasks = set()
        
        # Sort tasks by dependencies and priority
        sorted_tasks = self._sort_tasks_by_dependencies(tasks)
        
        for task in sorted_tasks:
            logger.info(f"Processing task: {task['id'][:8]}... ({task['agent_type']})")
            logger.info(f"Task dependencies: {[dep[:8] + '...' for dep in task['dependencies']]}")
            logger.info(f"Completed tasks: {[ct[:8] + '...' for ct in completed_tasks]}")

            # Check if dependencies are satisfied
            unsatisfied_deps = [dep for dep in task["dependencies"] if dep not in completed_tasks]
            if unsatisfied_deps:
                logger.warning(f"Task {task['id'][:8]}... dependencies not satisfied, skipping")
                logger.warning(f"Unsatisfied dependencies: {[dep[:8] + '...' for dep in unsatisfied_deps]}")
                continue

            # Get agent and execute task
            agent = self._get_agent(task["agent_type"])
            if not agent:
                logger.error(f"Agent {task['agent_type']} not available")
                continue

            try:
                logger.info(f"Executing task: {task['id'][:8]}... ({task['agent_type']})")
                task["status"] = TaskStatus.IN_PROGRESS
                result = await agent.execute_collaborative_task(task, shared_context)

                # Update shared context with result
                shared_context["execution_history"].append(result)
                if result["artifacts"]:
                    for artifact in result["artifacts"]:
                        shared_context["shared_artifacts"][artifact["id"]] = artifact

                results.append(result)
                completed_tasks.add(task["id"])

                # 实时推送设计图到前端
                await self._broadcast_design_result(task, result, shared_context)

                logger.info(f"Task {task['id'][:8]}... completed successfully with status: {result['status']}")

            except Exception as e:
                logger.error(f"Task {task['id'][:8]}... failed: {e}")
                error_result = AgentResult(
                    task_id=task["id"],
                    agent_id=agent.agent_id,
                    status=TaskStatus.FAILED,
                    output={},
                    artifacts=[],
                    execution_time=0.0,
                    error_message=str(e),
                    metadata={}
                )
                results.append(error_result)
                # 重要修复：即使任务失败，也要将其标记为已完成，以免阻塞后续依赖任务
                completed_tasks.add(task["id"])
                logger.info(f"Task {task['id'][:8]}... marked as completed (failed) to unblock dependencies")
        
        return results

    async def _broadcast_design_result(self, task: dict, result: dict, shared_context: SharedContext):
        """实时推送设计结果到前端 - 使用SOLID原则重构的广播系统"""
        try:
            # 从shared_context中获取conversation_id
            conversation_id = shared_context.get("conversation_id")
            if not conversation_id:
                logger.warning("No conversation_id found in shared_context, skipping design broadcast")
                return

            # 使用新的广播服务
            from .broadcast import get_broadcast_service
            broadcast_service = get_broadcast_service()

            success = await broadcast_service.broadcast_design_result(task, result, conversation_id)

            if success:
                logger.info(f"Design result broadcasted for task {task.get('id', 'unknown')[:8]}...")
            else:
                logger.debug(f"No broadcastable design data found for task {task.get('id', 'unknown')[:8]}...")

        except Exception as e:
            logger.error(f"Failed to broadcast design result: {e}")
            # 不抛出异常，避免影响主流程
    
    async def _execute_parallel(
        self,
        tasks: List[AgentTask],
        shared_context: SharedContext
    ) -> List[AgentResult]:
        """Execute tasks in parallel."""
        
        async def execute_single_task(task: AgentTask) -> AgentResult:
            agent = self._get_agent(task["agent_type"])
            if not agent:
                return AgentResult(
                    task_id=task["id"],
                    agent_id="unknown",
                    status=TaskStatus.FAILED,
                    output={},
                    artifacts=[],
                    execution_time=0.0,
                    error_message=f"Agent {task['agent_type']} not available",
                    metadata={}
                )
            
            try:
                task["status"] = TaskStatus.IN_PROGRESS
                return await agent.execute_collaborative_task(task, shared_context)
            except Exception as e:
                logger.error(f"Parallel task {task['id']} failed: {e}")
                return AgentResult(
                    task_id=task["id"],
                    agent_id=agent.agent_id,
                    status=TaskStatus.FAILED,
                    output={},
                    artifacts=[],
                    execution_time=0.0,
                    error_message=str(e),
                    metadata={}
                )
        
        # Execute all tasks sequentially (synchronously)
        results = []
        for task in tasks:
            try:
                result = await execute_single_task(task)
                results.append(result)
            except Exception as e:
                logger.error(f"Task {task['id']} failed: {e}")
                # Create error result
                results.append(AgentResult(
                    task_id=task["id"],
                    agent_id=task.get("agent_id", "unknown"),
                    status=TaskStatus.FAILED,
                    output={},
                    artifacts=[],
                    execution_time=0.0,
                    error_message=str(e),
                    metadata={}
                ))
        
        # Update shared context with results
        for result in results:
            shared_context["execution_history"].append(result)
            if result["artifacts"]:
                for artifact in result["artifacts"]:
                    shared_context["shared_artifacts"][artifact["id"]] = artifact
        
        return results
    
    async def _execute_hierarchical(
        self,
        tasks: List[AgentTask],
        shared_context: SharedContext
    ) -> List[AgentResult]:
        """Execute tasks in hierarchical pattern (main + supporting)."""
        
        # Identify main task (highest priority) and supporting tasks
        main_task = max(tasks, key=lambda t: t["priority"])
        supporting_tasks = [t for t in tasks if t["id"] != main_task["id"]]
        
        results = []
        
        # First, execute supporting tasks sequentially
        if supporting_tasks:
            supporting_results = await self._execute_parallel(supporting_tasks, shared_context)
            results.extend(supporting_results)
        
        # Then execute main task with support from other results
        main_agent = self._get_agent(main_task["agent_type"])
        if main_agent:
            try:
                main_task["status"] = TaskStatus.IN_PROGRESS
                main_result = await main_agent.execute_collaborative_task(main_task, shared_context)
                results.append(main_result)
                
                # Update shared context
                shared_context["execution_history"].append(main_result)
                if main_result["artifacts"]:
                    for artifact in main_result["artifacts"]:
                        shared_context["shared_artifacts"][artifact["id"]] = artifact
                        
            except Exception as e:
                logger.error(f"Main task {main_task['id']} failed: {e}")
                error_result = AgentResult(
                    task_id=main_task["id"],
                    agent_id=main_agent.agent_id,
                    status=TaskStatus.FAILED,
                    output={},
                    artifacts=[],
                    execution_time=0.0,
                    error_message=str(e),
                    metadata={}
                )
                results.append(error_result)
        
        return results
    
    async def _execute_peer_review(
        self,
        tasks: List[AgentTask],
        shared_context: SharedContext
    ) -> List[AgentResult]:
        """Execute tasks with peer review pattern."""
        
        # Phase 1: Initial execution
        initial_results = await self._execute_parallel(tasks, shared_context)
        
        # Phase 2: Peer review and improvement
        reviewed_results = []
        
        for i, result in enumerate(initial_results):
            if result["status"] != TaskStatus.COMPLETED or not result["artifacts"]:
                reviewed_results.append(result)
                continue
            
            # Get reviews from other agents
            reviews = []
            for j, other_task in enumerate(tasks):
                if i != j:  # Don't review own work
                    reviewer = self._get_agent(other_task["agent_type"])
                    if reviewer:
                        try:
                            for artifact in result["artifacts"]:
                                review = await reviewer.peer_review(
                                    artifact, 
                                    ["quality", "creativity", "feasibility"]
                                )
                                reviews.append(review)
                        except Exception as e:
                            logger.error(f"Peer review failed: {e}")
            
            # Original agent improves based on reviews
            if reviews:
                original_agent = self._get_agent(tasks[i]["agent_type"])
                if original_agent:
                    try:
                        # Create improvement task
                        improvement_task = AgentTask(
                            id=str(uuid4()),
                            agent_type=tasks[i]["agent_type"],
                            description=f"Improve design based on peer reviews",
                            requirements={
                                "original_result": result,
                                "peer_reviews": reviews
                            },
                            dependencies=[],
                            priority=tasks[i]["priority"],
                            expected_output="Improved design",
                            timeout=None,
                            status=TaskStatus.IN_PROGRESS
                        )
                        
                        improved_result = await original_agent.execute_collaborative_task(
                            improvement_task, shared_context
                        )
                        reviewed_results.append(improved_result)
                        
                    except Exception as e:
                        logger.error(f"Improvement based on reviews failed: {e}")
                        reviewed_results.append(result)
                else:
                    reviewed_results.append(result)
            else:
                reviewed_results.append(result)
        
        return reviewed_results
    
    def _get_agent(self, agent_type: str) -> Optional[BaseDesignAgent]:
        """Get agent from pool."""
        return self.agent_pool.get(agent_type)
    
    def _sort_tasks_by_dependencies(self, tasks: List[AgentTask]) -> List[AgentTask]:
        """Sort tasks by dependencies using topological sort."""
        # Simple implementation - can be enhanced with proper topological sorting
        return sorted(tasks, key=lambda t: (len(t["dependencies"]), -t["priority"]))


# ============================================================================
# Result Integration
# ============================================================================

class ResultIntegrator(IResultIntegrator):
    """Integrates results from multiple agents."""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            base_url=settings.qwen_base_url,
            api_key=settings.qwen_api_key,
            model=settings.qwen_model,
            temperature=0.3,
        )
    
    async def integrate_results(
        self,
        results: List[AgentResult],
        shared_context: SharedContext
    ) -> Dict[str, Any]:
        """Integrate multiple agent results into final output."""
        
        # Collect successful results and artifacts
        successful_results = [r for r in results if r["status"] == TaskStatus.COMPLETED]
        all_artifacts = []
        
        for result in successful_results:
            all_artifacts.extend(result["artifacts"])
        
        # Use LLM to create integration summary
        integration_summary = await self._create_integration_summary(
            successful_results, shared_context
        )
        
        return {
            "collaboration_id": str(uuid4()),
            "conversation_id": shared_context["conversation_id"],
            "collaboration_type": shared_context["collaboration_type"],
            "status": "completed" if successful_results else "failed",
            "success": len(successful_results) > 0,  # 添加success字段
            "total_tasks": len(results),
            "successful_tasks": len(successful_results),
            "failed_tasks": len(results) - len(successful_results),
            "results": results,
            "final_artifacts": all_artifacts,
            "integration_summary": integration_summary,
            "execution_time": sum(r["execution_time"] for r in results),
            "created_at": datetime.now().isoformat()
        }
    
    async def _create_integration_summary(
        self,
        results: List[AgentResult],
        shared_context: SharedContext
    ) -> str:
        """Create intelligent integration summary using LLM."""
        
        summary_prompt = f"""
        请为以下多Agent协作结果创建一个整合摘要：
        
        协作类型：{shared_context["collaboration_type"]}
        对话ID：{shared_context["conversation_id"]}
        
        执行结果：
        {json.dumps([{
            "agent_id": r["agent_id"],
            "task_id": r["task_id"],
            "status": r["status"],
            "output_summary": str(r["output"])[:200] + "..." if len(str(r["output"])) > 200 else str(r["output"]),
            "artifacts_count": len(r["artifacts"])
        } for r in results], ensure_ascii=False, indent=2)}
        
        请创建一个简洁但全面的摘要，包括：
        1. 协作过程概述
        2. 主要成果
        3. 各Agent的贡献
        4. 最终输出质量评估
        
        请用中文回复，保持专业和友好的语调。
        """
        
        try:
            response = await self.llm.ainvoke([HumanMessage(content=summary_prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Integration summary creation failed: {e}")
            return f"协作完成，共有{len(results)}个Agent参与，生成了{sum(len(r['artifacts']) for r in results)}个设计产物。"


# ============================================================================
# Collaboration Orchestrator
# ============================================================================

class CollaborationOrchestrator:
    """Orchestrates multi-agent collaborations."""

    def __init__(
        self,
        agent_pool: Dict[str, 'BaseDesignAgent'],
        communication_manager: CommunicationManager
    ):
        self.agent_pool = agent_pool
        self.communication_manager = communication_manager

        # Initialize collaboration components
        self.task_decomposer = TaskDecomposer()
        self.agent_coordinator = AgentCoordinator(agent_pool)
        self.result_integrator = ResultIntegrator()

    async def orchestrate_collaboration(
        self,
        user_request: str,
        collaboration_type: CollaborationType,
        conversation_id: str,
        user_id: str,
        required_agents: Optional[List[str]] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        previous_requirements: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Orchestrate a multi-agent collaboration."""

        try:
            from uuid import uuid4

            # 1. Task Decomposition with Context
            available_agents = required_agents or list(self.agent_pool.keys())
            tasks = await self.task_decomposer.decompose_request(
                user_request=user_request,
                collaboration_type=collaboration_type,
                available_agents=available_agents,
                conversation_history=conversation_history,
                previous_requirements=previous_requirements
            )

            if not tasks:
                raise ValueError("No tasks generated from request")

            # 2. Create Shared Context
            shared_context = SharedContext(
                conversation_id=conversation_id,
                collaboration_type=collaboration_type,
                shared_artifacts={},
                global_requirements={
                    "user_request": user_request,
                    "user_id": user_id,
                    "category": "fashion",  # Default, could be determined
                    "style": "modern",
                    "colors": [],
                    "mood": "professional",
                    "target_audience": "general",
                    "additional_notes": None
                },
                execution_history=[],
                current_phase="task_execution",
                metadata=kwargs
            )

            # 3. Execute Tasks
            results = await self.agent_coordinator.execute_tasks(
                tasks, collaboration_type, shared_context
            )

            # 4. Integrate Results
            final_result = await self.result_integrator.integrate_results(
                results, shared_context
            )

            logger.info(f"Collaboration completed: {len(results)} tasks executed")
            return final_result

        except Exception as e:
            logger.error(f"Collaboration orchestration failed: {e}")
            return {
                "error": str(e),
                "collaboration_type": collaboration_type,
                "conversation_id": conversation_id,
                "success": False
            }
