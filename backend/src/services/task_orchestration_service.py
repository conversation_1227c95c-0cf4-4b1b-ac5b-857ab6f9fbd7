"""
Task-Orchestration Integration Service.

This service integrates the new task management system with the existing agent orchestration flow,
providing a unified interface for task creation, execution, and tracking.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from ..utils.timezone_utils import get_db_timestamp, get_time_display
from uuid import uuid4, UUID

from sqlalchemy.ext.asyncio import AsyncSession

from .task_service import TaskManagementService
from .agent_factory import Agent<PERSON>outer
from .collaboration_core import CollaborationOrchestrator
from .base_agents import CollaborationType
from ..models.schemas import (
    TaskCreate, TaskWorkflowRequest, TaskProgressUpdate, TaskStatusUpdate,
    TaskResponse, TaskWorkflowResponse, TaskWithDetailsResponse, TaskArtifactCreate,
    ChatMessageCreate
)
from ..models.database import Task as TaskModel

logger = logging.getLogger(__name__)


class TaskOrchestrationService:
    """Unified service for task management and agent orchestration."""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.task_service = TaskManagementService(db_session)
        self.agent_router = AgentRouter()
        self.collaboration_orchestrator = None
        
        # Initialize collaboration orchestrator if not already done
        if not self.agent_router.collaboration_orchestrator:
            self.agent_router.collaboration_orchestrator = CollaborationOrchestrator(
                agent_pool=self.agent_router.active_agents,
                communication_manager=self.agent_router.communication_manager
            )
        self.collaboration_orchestrator = self.agent_router.collaboration_orchestrator
    
    async def create_intelligent_task(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        task_type: str = "fashion_design",
        priority: int = 5,
        **kwargs
    ) -> TaskWorkflowResponse:
        """
        Create a task intelligently based on user input and integrate with orchestration.
        
        This method analyzes the user input and creates an appropriate task
        that integrates with the existing agent orchestration system.
        """
        
        try:
            # Ensure conversation record exists
            await self._ensure_conversation_exists(conversation_id, user_id, user_input)
            
            # Determine task type and parameters based on user input
            task_analysis = await self._analyze_user_input(user_input)
            
            # Add additional parameters to task analysis for message saving
            task_analysis.update({
                "message_type": kwargs.get("message_type", "user"),
                "requirements": kwargs.get("requirements"),
                "edit_image_url": kwargs.get("edit_image_url"),
                "edit_image_id": kwargs.get("edit_image_id")
            })
            
            # Create task workflow request
            workflow_request = TaskWorkflowRequest(
                conversation_id=UUID(conversation_id),
                task_type=task_analysis.get("task_type", task_type),
                name=task_analysis.get("name", f"Intelligent Task: {user_input[:50]}"),
                description=task_analysis.get("description", user_input),
                input_parameters={
                    "user_input": user_input,
                    "analysis": task_analysis,
                    "user_id": user_id,
                    **kwargs
                },
                priority=priority,
                use_template=True  # Use workflow templates for better structure
            )
            
            # Create task using task management service
            task_response = await self.task_service.create_task_from_workflow(workflow_request)
            
            # Start orchestration synchronously and wait for completion
            orchestration_result = await self._execute_task_orchestration(
                task_response.task_id,
                user_input,
                conversation_id,
                user_id,
                task_analysis
            )
            
            logger.info(f"Completed intelligent task {task_response.task_id} for conversation {conversation_id}")
            
            # Update task response with completion results
            task_response.status = "completed" if orchestration_result.get("success") else "failed"
            # Use setattr to safely set the output_results attribute
            setattr(task_response, 'output_results', orchestration_result)
            
            return task_response
            
        except Exception as e:
            logger.error(f"Failed to create intelligent task: {e}")
            raise
    
    async def _analyze_user_input(self, user_input: str) -> Dict[str, Any]:
        """Analyze user input to determine task type and parameters."""
        
        user_input_lower = user_input.lower()
        
        # Determine task type based on keywords
        if any(keyword in user_input_lower for keyword in ["logo", "标识", "品牌标志", "商标", "标志"]):
            task_type = "logo_design"
            name = "Logo设计任务"
        elif any(keyword in user_input_lower for keyword in ["海报", "宣传", "广告", "poster"]):
            task_type = "poster_design"
            name = "海报设计任务"
        elif any(keyword in user_input_lower for keyword in ["服装", "衣服", "裙子", "上衣", "裤子", "外套", "时装"]):
            task_type = "fashion_design"
            name = "服装设计任务"
        elif any(keyword in user_input_lower for keyword in ["剧本", "剧情", "电影", "短片", "视频", "拍摄", "导演", "角色", "场景", "故事"]):
            task_type = "drama_production"
            name = "剧本制作任务"
        else:
            task_type = "fashion_design"  # Default
            name = "智能设计任务"
        
        return {
            "task_type": task_type,
            "name": name,
            "description": user_input,
            "detected_keywords": self._extract_keywords(user_input),
            "complexity_score": self._calculate_complexity(user_input)
        }
    
    def _extract_keywords(self, user_input: str) -> List[str]:
        """Extract relevant keywords from user input."""
        keywords = []
        user_input_lower = user_input.lower()
        
        # Design type keywords
        design_keywords = {
            "logo": ["logo", "标识", "品牌标志", "商标", "标志"],
            "poster": ["海报", "宣传", "广告", "poster"],
            "fashion": ["服装", "衣服", "裙子", "上衣", "裤子", "外套", "时装"],
            "drama": ["剧本", "剧情", "电影", "短片", "视频", "拍摄", "导演", "角色", "场景"]
        }
        
        for category, words in design_keywords.items():
            if any(word in user_input_lower for word in words):
                keywords.append(category)
        
        # Style keywords
        style_keywords = ["现代", "简约", "复古", "时尚", "优雅", "运动", "商务", "休闲"]
        for style in style_keywords:
            if style in user_input_lower:
                keywords.append(style)
        
        return keywords
    
    def _calculate_complexity(self, user_input: str) -> int:
        """Calculate complexity score of the task."""
        # Simple complexity calculation based on input length and keywords
        base_score = min(len(user_input) // 10, 10)
        
        # Add complexity for specific keywords
        complexity_indicators = ["品牌", "系列", "多个", "整套", "完整", "复杂", "详细"]
        for indicator in complexity_indicators:
            if indicator in user_input.lower():
                base_score += 2
        
        return min(base_score, 10)
    
    async def _execute_task_orchestration(
        self,
        task_id: str,
        user_input: str,
        conversation_id: str,
        user_id: str,
        task_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the task using the existing orchestration system and return result."""
        
        try:
            # Save user message to conversation
            await self._save_user_message(
                conversation_id=conversation_id,
                user_id=user_id,
                user_input=user_input,
                message_type=task_analysis.get("message_type", "user"),
                requirements=task_analysis.get("requirements"),
                edit_image_url=task_analysis.get("edit_image_url"),
                edit_image_id=task_analysis.get("edit_image_id")
            )
            
            # Update task status to in_progress
            await self.task_service.update_task_status(TaskStatusUpdate(
                task_id=UUID(str(task_id)),
                status="in_progress"
            ))
            
            # Execute using collaboration orchestrator
            result = await self.collaboration_orchestrator.orchestrate_collaboration(
                user_request=user_input,
                collaboration_type=CollaborationType.SEQUENTIAL,
                conversation_id=conversation_id,
                user_id=user_id,
                task_context={
                    "task_id": task_id,
                    "task_analysis": task_analysis
                }
            )
            
            # Update task with results
            if result.get("success"):
                # Save artifacts to database
                await self._save_artifacts_from_result(task_id, result)
                
                # Save AI response message to conversation
                await self._save_ai_response_message(conversation_id, user_id, result)
                
                await self.task_service.update_task_status(TaskStatusUpdate(
                    task_id=UUID(str(task_id)),
                    status="completed",
                    output_results=result
                ))
                
                # Update progress to 100%
                await self.task_service.update_task_progress(TaskProgressUpdate(
                    task_id=UUID(str(task_id)),
                    progress=100.0,
                    message="Task completed successfully"
                ))
                
                logger.info(f"Task {task_id} completed successfully")
                return {"success": True, "result": result, "task_id": task_id}
            else:
                # Save error message to conversation
                error_result = {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "type": "error"
                }
                await self._save_ai_response_message(conversation_id, user_id, error_result)
                
                await self.task_service.update_task_status(TaskStatusUpdate(
                    task_id=UUID(str(task_id)),
                    status="failed",
                    error_message=result.get("error", "Unknown error")
                ))
                
                logger.error(f"Task {task_id} failed: {result.get('error')}")
                return {"success": False, "error": result.get("error"), "task_id": task_id}
                
        except Exception as e:
            logger.error(f"Task orchestration execution failed for {task_id}: {e}")
            
            # Save exception error message to conversation
            exception_result = {
                "success": False,
                "error": str(e),
                "type": "exception",
                "message": "任务执行过程中发生异常"
            }
            await self._save_ai_response_message(conversation_id, user_id, exception_result)
            
            # Update task status to failed
            await self.task_service.update_task_status(TaskStatusUpdate(
                task_id=UUID(str(task_id)),
                status="failed",
                error_message=str(e)
            ))
            
            return {"success": False, "error": str(e), "task_id": task_id}
    
    async def _save_artifacts_from_result(self, task_id: str, result: Dict[str, Any]) -> None:
        """Save artifacts from collaboration result to database."""
        try:
            # Extract artifacts from result - handle multiple possible formats
            artifacts = []
            
            # Try different possible artifact locations
            if "final_artifacts" in result:
                artifacts.extend(result["final_artifacts"])
            elif "artifacts" in result:
                artifacts.extend(result["artifacts"])
            
            # Check for single artifact object
            if "artifact" in result and result["artifact"]:
                artifact = result["artifact"]
                if isinstance(artifact, dict):
                    artifacts.append(artifact)
                elif isinstance(artifact, list):
                    artifacts.extend(artifact)
            
            # Check results array (common in multi-agent responses)
            if "results" in result and isinstance(result["results"], list):
                for agent_result in result["results"]:
                    if "artifacts" in agent_result:
                        artifacts.extend(agent_result["artifacts"])
                    elif "artifact" in agent_result:
                        artifact = agent_result["artifact"]
                        if isinstance(artifact, dict):
                            artifacts.append(artifact)
                        elif isinstance(artifact, list):
                            artifacts.extend(artifact)
            
            # Check for image_url directly in result
            if "image_url" in result and result["image_url"]:
                artifacts.append({
                    "image_url": result["image_url"],
                    "type": result.get("type", "design"),
                    "description": result.get("description", "Generated design"),
                    "metadata": result.get("metadata", {})
                })
            
            # Check for generated_content that might contain image URLs
            if "generated_content" in result and result["generated_content"]:
                content = result["generated_content"]
                if isinstance(content, dict) and "image_url" in content:
                    artifacts.append({
                        "image_url": content["image_url"],
                        "type": content.get("type", "design"),
                        "description": content.get("description", "Generated content"),
                        "metadata": content.get("metadata", {})
                    })
            
            # Save each artifact to database
            saved_count = 0
            for artifact in artifacts:
                if await self._save_single_artifact(task_id, artifact):
                    saved_count += 1
            
            logger.info(f"Saved {saved_count} out of {len(artifacts)} artifacts for task {task_id}")
            
        except Exception as e:
            logger.error(f"Failed to save artifacts for task {task_id}: {e}")
    
    async def _save_single_artifact(self, task_id: str, artifact: Any) -> bool:
        """Save a single artifact to database."""
        try:
            if not isinstance(artifact, dict):
                logger.warning(f"Skipping non-dict artifact: {type(artifact)}")
                return False
            
            # Handle different artifact structures
            image_url = None
            
            # Try various possible image URL locations
            if "image_url" in artifact:
                image_url = artifact["image_url"]
            elif "url" in artifact:
                image_url = artifact["url"]
            elif "file_url" in artifact:
                image_url = artifact["file_url"]
            elif "src" in artifact:
                image_url = artifact["src"]
            
            if not image_url:
                logger.warning(f"Artifact missing image URL: {list(artifact.keys())}")
                return False
            
            # Extract metadata with fallbacks
            metadata = artifact.get("metadata", {})
            if not isinstance(metadata, dict):
                metadata = {}
            
            # Enhance metadata with additional artifact info
            enhanced_metadata = {
                "agent_type": metadata.get("agent_type") or artifact.get("agent_type"),
                "design_prompt": metadata.get("design_prompt") or artifact.get("prompt") or artifact.get("description"),
                "created_by": metadata.get("created_by") or artifact.get("created_by"),
                "task_type": metadata.get("task_type") or artifact.get("task_type"),
                "original_artifact": artifact,
                "extraction_timestamp": get_db_timestamp()
            }
            
            # Create artifact data
            artifact_data = TaskArtifactCreate(
                task_id=UUID(task_id),
                agent_execution_id=None,  # Can be populated later if needed
                artifact_type=artifact.get("type", "design"),
                artifact_name=artifact.get("description", f"Design artifact {len(artifact)}"),
                file_url=image_url,
                file_path=None,  # URL-based storage
                file_size=None,  # Would need to fetch file size
                mime_type=self._detect_mime_type(image_url),
                metadata=enhanced_metadata,
                is_temporary=False,
                expires_at=None
            )
            
            saved_artifact = await self.task_service.create_task_artifact(artifact_data)
            logger.info(f"Saved artifact {saved_artifact.id} for task {task_id}: {image_url[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save single artifact: {e}")
            return False
    
    def _detect_mime_type(self, image_url: str) -> str:
        """Detect MIME type from image URL."""
        url_lower = image_url.lower()
        if url_lower.endswith(('.jpg', '.jpeg')):
            return "image/jpeg"
        elif url_lower.endswith('.png'):
            return "image/png"
        elif url_lower.endswith('.gif'):
            return "image/gif"
        elif url_lower.endswith('.webp'):
            return "image/webp"
        elif url_lower.endswith('.svg'):
            return "image/svg+xml"
        else:
            return "image/jpeg"  # Default
    
    async def get_task_with_orchestration_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task details combined with orchestration information."""
        
        try:
            # Get basic task details
            task_details = await self.task_service.get_task(task_id)
            if not task_details:
                return None
            
            # Convert to dict and add orchestration info
            task_dict = task_details.dict()
            
            # Add orchestration-specific information
            task_dict["orchestration_info"] = {
                "agent_router_status": "active",
                "collaboration_orchestrator_available": self.collaboration_orchestrator is not None,
                "active_agents": list(self.agent_router.get_active_agents().keys()),
                "execution_mode": "intelligent"
            }
            
            return task_dict
            
        except Exception as e:
            logger.error(f"Failed to get task with orchestration info: {e}")
            return None
    
    async def get_conversation_tasks_with_status(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Get all tasks for a conversation with enhanced status information."""
        
        try:
            # Get basic tasks
            tasks = await self.task_service.get_tasks_by_conversation(conversation_id)
            
            # Enhance with orchestration information
            enhanced_tasks = []
            for task in tasks:
                task_dict = task.dict()
                task_dict["orchestration_info"] = {
                    "can_retry": task.status in ["failed", "cancelled"],
                    "estimated_remaining_time": self._estimate_remaining_time(task),
                    "agent_suggestions": self._get_agent_suggestions(task)
                }
                enhanced_tasks.append(task_dict)
            
            return enhanced_tasks
            
        except Exception as e:
            logger.error(f"Failed to get conversation tasks with status: {e}")
            return []
    
    def _estimate_remaining_time(self, task: TaskResponse) -> Optional[int]:
        """Estimate remaining time for a task."""
        if task.status == "completed":
            return 0
        elif task.status == "in_progress":
            # Simple estimation based on progress
            if task.progress > 0:
                elapsed = (get_db_timestamp() - task.started_at).total_seconds() if task.started_at else 0
                remaining = elapsed * (100 - task.progress) / task.progress
                return int(remaining)
        
        # Default estimation based on task type
        estimations = {
            "fashion_design": 120,
            "logo_design": 90,
            "poster_design": 150,
            "drama_production": 300
        }
        return estimations.get(task.task_type, 120)
    
    def _get_agent_suggestions(self, task: TaskResponse) -> List[str]:
        """Get agent suggestions for retrying failed tasks."""
        if task.status != "failed":
            return []
        
        # Simple suggestions based on task type
        suggestions = {
            "fashion_design": ["FashionDesignAgent", "ColorExpertAgent"],
            "logo_design": ["LogoDesignAgent", "TypographyAgent"],
            "poster_design": ["PosterDesignAgent", "ColorExpertAgent"],
            "drama_production": ["DramaProductionAgent"]
        }
        
        return suggestions.get(task.task_type, ["FashionDesignAgent"])
    
    async def retry_failed_task(self, task_id: str) -> bool:
        """Retry a failed task with enhanced error handling."""
        
        try:
            # Get task details
            task_details = await self.task_service.get_task(task_id)
            if not task_details:
                return False
            
            if task_details.status != "failed":
                logger.warning(f"Cannot retry task {task_id}: not in failed status")
                return False
            
            # Reset task status
            await self.task_service.update_task_status(TaskStatusUpdate(
                task_id=UUID(str(task_id)),
                status="pending",
                error_message=None
            ))
            
            # Extract original parameters
            input_params = task_details.input_parameters or {}
            user_input = input_params.get("user_input", "")
            conversation_id = str(task_details.conversation_id)
            user_id = input_params.get("user_id", "unknown")
            task_analysis = input_params.get("analysis", {})
            
            # Restart orchestration
            await self._execute_task_orchestration(
                task_id,
                user_input,
                conversation_id,
                user_id,
                task_analysis
            )
            
            logger.info(f"Successfully restarted task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to retry task {task_id}: {e}")
            return False
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        
        try:
            # Get task service stats
            task_stats = {
                "total_tasks": 0,  # Would need to implement this in task service
                "active_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0
            }
            
            # Get agent router stats
            agent_stats = self.agent_router.get_agent_capabilities_summary()
            
            # Get communication stats
            comm_stats = self.agent_router.communication_manager.get_communication_stats()
            
            return {
                "task_management": task_stats,
                "agent_system": {
                    "active_agents": len(agent_stats),
                    "agent_capabilities": agent_stats
                },
                "communication": comm_stats,
                "orchestration": {
                    "collaboration_orchestrator_active": self.collaboration_orchestrator is not None,
                    "intelligent_routing_enabled": True
                },
                "integration_status": "fully_integrated"
            }
            
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
    
    async def _ensure_conversation_exists(self, conversation_id: str, user_id: str, user_input: str):
        """Ensure conversation record exists in database."""
        
        try:
            from ..models.database import Conversation
            from sqlalchemy import select
            
            # Check if conversation exists
            result = await self.db.execute(
                select(Conversation).where(Conversation.id == conversation_id)
            )
            conversation = result.scalar_one_or_none()
            
            # If conversation doesn't exist, create it
            if not conversation:
                conversation_record = Conversation(
                    id=conversation_id,
                    user_id=user_id,
                    title=user_input[:50] + "..." if len(user_input) > 50 else user_input,
                    status="active",
                    meta_data={"created_by": "task_orchestration"}
                )
                
                self.db.add(conversation_record)
                await self.db.commit()
                logger.info(f"Created conversation record: {conversation_id}")
            
        except Exception as e:
            logger.error(f"Error ensuring conversation exists: {e}")
            raise
    
    async def _save_conversation_message(
        self,
        conversation_id: str,
        user_id: str,
        role: str,
        content: str,
        image_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Save a conversation message to the database."""
        try:
            from .design_service import ChatService
            
            chat_service = ChatService(self.db)
            message_data = ChatMessageCreate(
                role=role,
                content=content,
                image_url=image_url,
                message_metadata=metadata or {}
            )
            
            conversation_uuid = UUID(conversation_id) if isinstance(conversation_id, str) else conversation_id
            message = await chat_service.create_message(conversation_uuid, message_data)
            logger.info(f"Saved {role} message for conversation {conversation_id}: {message.id}")
            
        except Exception as e:
            logger.error(f"Error saving conversation message: {e}")
            # Don't raise here, as message saving shouldn't stop the main process
    
    async def _save_user_message(
        self,
        conversation_id: str,
        user_id: str,
        user_input: str,
        message_type: str = "user",
        requirements: Optional[Dict] = None,
        edit_image_url: Optional[str] = None,
        edit_image_id: Optional[str] = None
    ) -> None:
        """Save user message to conversation."""
        metadata = {
            "message_type": message_type,
            "requirements": requirements,
            "edit_image_id": edit_image_id,
            "conversation_title": user_input[:50] + "..." if len(user_input) > 50 else user_input
        }
        
        await self._save_conversation_message(
            conversation_id=conversation_id,
            user_id=user_id,
            role="user",
            content=user_input,
            image_url=edit_image_url,
            metadata=metadata
        )
    
    async def _save_ai_response_message(
        self,
        conversation_id: str,
        user_id: str,
        result: Dict[str, Any]
    ) -> None:
        """Save AI response message to conversation."""
        ai_content = ""
        ai_metadata = {}
        image_url = None
        
        # Extract AI response content and metadata from result
        if isinstance(result, dict):
            # Extract message content from different possible locations
            if "messages" in result and result["messages"]:
                messages = result["messages"]
                if isinstance(messages, list) and len(messages) > 0:
                    last_message = messages[-1]
                    if isinstance(last_message, dict) and "content" in last_message:
                        ai_content = last_message["content"]
                    else:
                        ai_content = str(last_message)
                else:
                    ai_content = str(messages)
            elif "message" in result:
                ai_content = result["message"]
            elif "content" in result:
                ai_content = result["content"]
            elif "final_response" in result:
                ai_content = result["final_response"]
            else:
                ai_content = "AI处理完成"
            
            # Extract image URL
            if "image_url" in result:
                image_url = result["image_url"]
            elif "final_artifacts" in result and result["final_artifacts"]:
                # Get image URL from first artifact
                first_artifact = result["final_artifacts"][0]
                if isinstance(first_artifact, dict) and "image_url" in first_artifact:
                    image_url = first_artifact["image_url"]
            
            # Save comprehensive metadata
            ai_metadata = {
                "result_type": result.get("type", "unknown"),
                "task_status": result.get("success", False),
                "generated_content": result.get("generated_content"),
                "artifacts_count": len(result.get("final_artifacts", [])),
                "workflow_state": result.get("workflow_state"),
                "full_result": result
            }
        else:
            ai_content = str(result)
            ai_metadata = {"result_type": "string_response"}
        
        await self._save_conversation_message(
            conversation_id=conversation_id,
            user_id=user_id,
            role="assistant",
            content=ai_content,
            image_url=image_url,
            metadata=ai_metadata
        )