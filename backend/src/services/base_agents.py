"""
Multi-Agent Design System - Abstract Base Classes and Interfaces.

This module defines the core abstractions for the multi-agent design system,
including base classes for design agents, collaboration protocols, and communication interfaces.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, TypedDict, Union, Type
from enum import Enum
from datetime import datetime
import uuid


# ============================================================================
# Enums and Constants
# ============================================================================

class DesignType(str, Enum):
    """Supported design types."""
    FASHION = "fashion"
    POSTER = "poster"
    LOGO = "logo"
    DRAMA = "drama"
    UI = "ui"
    BRANDING = "branding"


class CollaborationType(str, Enum):
    """Agent collaboration patterns."""
    SEQUENTIAL = "sequential"      # 顺序流水线
    PARALLEL = "parallel"          # 并行处理
    HIERARCHICAL = "hierarchical"  # 分层委派
    PEER_REVIEW = "peer_review"    # 同行评议


class MessageType(str, Enum):
    """Agent message types."""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    REVIEW = "review"
    COLLABORATION = "collaboration"


class TaskStatus(str, Enum):
    """Task execution status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# ============================================================================
# Base Data Structures
# ============================================================================

class BaseRequirements(TypedDict):
    """Base requirements structure for all design types."""
    category: str                    # 设计类别
    style: str                      # 设计风格
    colors: List[str]               # 颜色方案
    mood: str                       # 整体感觉
    target_audience: str            # 目标受众
    additional_notes: Optional[str] # 额外说明


class AgentMessage(TypedDict):
    """Message structure for agent communication."""
    id: str
    sender_id: str
    recipient_id: Optional[str]  # None for broadcast
    topic: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: str
    conversation_id: str


class DesignArtifact(TypedDict):
    """Design artifact structure for shared workspace."""
    id: str
    type: str                    # image, prompt, analysis, etc.
    content: Union[str, Dict]    # URL, data, or structured content
    metadata: Dict[str, Any]
    created_by: str             # agent_id
    created_at: str
    version: int


class AgentTask(TypedDict):
    """Task structure for agent collaboration."""
    id: str
    agent_type: str
    description: str
    requirements: Dict[str, Any]
    dependencies: List[str]      # Task IDs this task depends on
    priority: int
    expected_output: str
    timeout: Optional[int]
    status: TaskStatus


class AgentResult(TypedDict):
    """Result structure from agent task execution."""
    task_id: str
    agent_id: str
    status: TaskStatus
    output: Dict[str, Any]
    artifacts: List[DesignArtifact]
    execution_time: float
    error_message: Optional[str]
    metadata: Dict[str, Any]


class SharedContext(TypedDict):
    """Shared context for agent collaboration."""
    conversation_id: str
    collaboration_type: CollaborationType
    shared_artifacts: Dict[str, DesignArtifact]
    global_requirements: BaseRequirements
    execution_history: List[AgentResult]
    current_phase: str
    metadata: Dict[str, Any]


# ============================================================================
# Abstract Base Classes
# ============================================================================

class BaseDesignAgent(ABC):
    """Abstract base class for all design agents."""
    
    def __init__(self, agent_id: Optional[str] = None, agent_type: Optional[str] = None):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.agent_type = agent_type or self.__class__.__name__
        self.is_collaborative = True

        # Initialize capabilities and schema after subclass initialization
        self._capabilities = None
        self._requirements_schema = None

    @property
    def capabilities(self) -> List[str]:
        """Get agent capabilities (lazy initialization)."""
        if self._capabilities is None:
            self._capabilities = self.get_capabilities()
        return self._capabilities

    @property
    def requirements_schema(self) -> Type[BaseRequirements]:
        """Get requirements schema (lazy initialization)."""
        if self._requirements_schema is None:
            self._requirements_schema = self.get_requirements_schema()
        return self._requirements_schema

    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """Return list of agent capabilities."""
        pass
    
    @abstractmethod
    def get_requirements_schema(self) -> Type[BaseRequirements]:
        """Return the requirements schema for this agent type."""
        pass
    
    @abstractmethod
    async def process_conversation(
        self,
        user_input: str,
        conversation_id: str,
        user_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Process user conversation - main entry point."""
        pass
    
    @abstractmethod
    async def execute_collaborative_task(
        self,
        task: AgentTask,
        shared_context: SharedContext
    ) -> AgentResult:
        """Execute a task in collaborative mode."""
        pass
    
    async def provide_expert_advice(
        self,
        request: Dict[str, Any],
        context: Optional[SharedContext] = None
    ) -> Dict[str, Any]:
        """Provide expert advice to other agents."""
        # Default implementation - can be overridden by expert agents
        return {
            "advice": "No specific advice available",
            "confidence": 0.0,
            "agent_id": self.agent_id
        }
    
    async def peer_review(
        self,
        artifact: DesignArtifact,
        review_criteria: List[str]
    ) -> Dict[str, Any]:
        """Review another agent's work."""
        # Default implementation - can be overridden
        return {
            "review_score": 0.5,
            "comments": "No specific review available",
            "suggestions": [],
            "reviewer_id": self.agent_id
        }
    
    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle incoming message from other agents."""
        # Default implementation - can be overridden
        return None


class BaseToolAgent(ABC):
    """Abstract base class for tool-calling agents."""
    
    def __init__(self):
        self.tools = self.get_available_tools()
    
    @abstractmethod
    def get_available_tools(self) -> List[Dict]:
        """Return list of available tools."""
        pass
    
    @abstractmethod
    async def autonomous_design_generation(
        self,
        user_input: str,
        requirements: Dict,
        conversation_id: str
    ) -> Dict:
        """Generate design autonomously using available tools."""
        pass
    
    @abstractmethod
    async def autonomous_design_modification(
        self,
        user_input: str,
        base_image: str,
        conversation_id: str
    ) -> Dict:
        """Modify design autonomously using available tools."""
        pass


# ============================================================================
# Communication Interfaces
# ============================================================================

class IMessageBus(ABC):
    """Interface for agent message bus."""
    
    @abstractmethod
    async def publish(self, message: AgentMessage) -> bool:
        """Publish message to the bus."""
        pass
    
    @abstractmethod
    async def subscribe(self, agent_id: str, topics: List[str]) -> bool:
        """Subscribe agent to topics."""
        pass
    
    @abstractmethod
    async def unsubscribe(self, agent_id: str, topics: List[str]) -> bool:
        """Unsubscribe agent from topics."""
        pass


class ISharedWorkspace(ABC):
    """Interface for shared workspace."""
    
    @abstractmethod
    async def store_artifact(
        self,
        artifact: DesignArtifact,
        agent_id: str
    ) -> bool:
        """Store design artifact."""
        pass
    
    @abstractmethod
    async def get_artifact(
        self,
        artifact_id: str,
        agent_id: str
    ) -> Optional[DesignArtifact]:
        """Get design artifact."""
        pass
    
    @abstractmethod
    async def list_artifacts(
        self,
        conversation_id: str,
        artifact_type: Optional[str] = None
    ) -> List[DesignArtifact]:
        """List artifacts for conversation."""
        pass


# ============================================================================
# Collaboration Interfaces
# ============================================================================

class ITaskDecomposer(ABC):
    """Interface for task decomposition."""
    
    @abstractmethod
    async def decompose_request(
        self,
        user_request: str,
        collaboration_type: CollaborationType,
        available_agents: List[str]
    ) -> List[AgentTask]:
        """Decompose user request into agent tasks."""
        pass


class IAgentCoordinator(ABC):
    """Interface for agent coordination."""
    
    @abstractmethod
    async def execute_tasks(
        self,
        tasks: List[AgentTask],
        collaboration_type: CollaborationType,
        shared_context: SharedContext
    ) -> List[AgentResult]:
        """Execute tasks using specified collaboration pattern."""
        pass


class IResultIntegrator(ABC):
    """Interface for result integration."""
    
    @abstractmethod
    async def integrate_results(
        self,
        results: List[AgentResult],
        shared_context: SharedContext
    ) -> Dict[str, Any]:
        """Integrate multiple agent results."""
        pass
