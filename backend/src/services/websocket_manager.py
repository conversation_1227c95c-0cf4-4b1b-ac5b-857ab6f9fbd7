"""
WebSocket管理器 - 实时状态推送系统
用于向前端推送自主工具调用的实时状态更新
"""

import json
import logging
import asyncio
from typing import Dict, Set, Optional
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 按对话ID分组的连接
        self.conversation_connections: Dict[str, Set[str]] = {}
        # 连接限制
        self._max_connections = 1000  # 限制总连接数
        self._max_conversation_connections = 50  # 限制每个对话的连接数
        self._message_queue_size = 100  # 限制消息队列大小
        # 启动定期清理任务（延迟初始化）
        self._cleanup_task = None
        self._initialized = False
        
    def _initialize_cleanup_task(self):
        """初始化清理任务"""
        if not self._initialized:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            self._initialized = True
            
    async def connect(self, websocket: WebSocket, connection_id: str, conversation_id: str = None):
        """建立WebSocket连接"""
        # 确保清理任务已初始化
        self._initialize_cleanup_task()
        
        # 检查连接限制
        if len(self.active_connections) >= self._max_connections:
            await websocket.close(code=1008, reason="Too many connections")
            logger.warning(f"Connection rejected: too many connections ({len(self.active_connections)}")
            return
        
        if conversation_id and len(self.conversation_connections.get(conversation_id, set())) >= self._max_conversation_connections:
            await websocket.close(code=1008, reason="Too many connections for conversation")
            logger.warning(f"Connection rejected: too many connections for conversation {conversation_id}")
            return
        
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        
        if conversation_id:
            if conversation_id not in self.conversation_connections:
                self.conversation_connections[conversation_id] = set()
            self.conversation_connections[conversation_id].add(connection_id)
        
        logger.info(f"WebSocket connected: {connection_id}, conversation: {conversation_id}")
    
    def disconnect(self, connection_id: str, conversation_id: str = None):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        if conversation_id and conversation_id in self.conversation_connections:
            self.conversation_connections[conversation_id].discard(connection_id)
            if not self.conversation_connections[conversation_id]:
                del self.conversation_connections[conversation_id]
        
        logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"Error sending message to {connection_id}: {e}")
                self.disconnect(connection_id)
    
    async def send_to_conversation(self, conversation_id: str, message: dict):
        """向特定对话的所有连接发送消息"""
        if conversation_id in self.conversation_connections:
            disconnected_connections = []
            
            for connection_id in self.conversation_connections[conversation_id]:
                if connection_id in self.active_connections:
                    try:
                        websocket = self.active_connections[connection_id]
                        await websocket.send_text(json.dumps(message, ensure_ascii=False))
                    except Exception as e:
                        logger.error(f"Error sending message to {connection_id}: {e}")
                        disconnected_connections.append(connection_id)
            
            # 清理断开的连接
            for connection_id in disconnected_connections:
                self.disconnect(connection_id, conversation_id)
    
    async def send_to_conversation_excluding_user(self, conversation_id: str, exclude_user_id: str, message: dict):
        """向特定对话中除指定用户外的所有连接发送消息"""
        if conversation_id in self.conversation_connections:
            disconnected_connections = []
            
            for connection_id in self.conversation_connections[conversation_id]:
                if connection_id in self.active_connections and connection_id != exclude_user_id:
                    try:
                        websocket = self.active_connections[connection_id]
                        await websocket.send_text(json.dumps(message, ensure_ascii=False))
                    except Exception as e:
                        logger.error(f"Error sending message to {connection_id}: {e}")
                        disconnected_connections.append(connection_id)
            
            # 清理断开的连接
            for connection_id in disconnected_connections:
                self.disconnect(connection_id, conversation_id)
    
    async def broadcast(self, message: dict):
        """优化的广播方法，使用并发发送"""
        if not self.active_connections:
            return
            
        # 创建消息一次
        message_json = json.dumps(message, ensure_ascii=False)
        
        # 使用asyncio.gather进行并发发送
        tasks = []
        for connection_id, websocket in self.active_connections.items():
            task = asyncio.create_task(self._send_message(websocket, message_json, connection_id))
            tasks.append(task)
        
        # 等待所有发送完成或失败
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_message(self, websocket: WebSocket, message: str, connection_id: str):
        """发送消息到单个连接，带错误处理"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending message to {connection_id}: {e}")
            self.disconnect(connection_id)
    
    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)
    
    def get_conversation_connections(self, conversation_id: str) -> int:
        """获取特定对话的连接数"""
        return len(self.conversation_connections.get(conversation_id, set()))
    
    async def _periodic_cleanup(self):
        """定期清理死连接"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                await self._cleanup_dead_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")
    
    async def _cleanup_dead_connections(self):
        """清理死亡的WebSocket连接"""
        dead_connections = []
        
        for connection_id, websocket in self.active_connections.items():
            try:
                # 发送ping检查连接是否存活
                await websocket.ping()
            except Exception:
                dead_connections.append(connection_id)
        
        for connection_id in dead_connections:
            self.disconnect(connection_id)
            logger.info(f"Cleaned up dead connection: {connection_id}")


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()


class DesignRealtimeBroadcaster:
    """设计实时推送广播器"""

    def __init__(self, manager: WebSocketManager):
        self.manager = manager

    async def send_design_generated(self, conversation_id: str, design_data: dict):
        """发送设计生成完成通知"""
        message = {
            "type": "design_generated",
            "conversation_id": conversation_id,
            "timestamp": design_data.get("timestamp"),
            "data": {
                "image_url": design_data.get("image_url"),
                "design_prompt": design_data.get("design_prompt"),
                "agent_type": design_data.get("agent_type"),
                "task_id": design_data.get("task_id"),
                "design_id": design_data.get("design_id"),
                "metadata": design_data.get("metadata", {})
            }
        }

        await self.manager.send_to_conversation(conversation_id, message)
        logger.info(f"Design generated notification sent to conversation {conversation_id}: {design_data.get('image_url')}")

    async def send_task_progress(self, conversation_id: str, progress_data: dict):
        """发送任务进度更新"""
        message = {
            "type": "task_progress",
            "conversation_id": conversation_id,
            "timestamp": progress_data.get("timestamp"),
            "data": progress_data
        }

    async def send_canvas_state_update(self, conversation_id: str, user_id: str, canvas_data: dict):
        """发送画布状态更新通知"""
        message = {
            "type": "canvas_state_update",
            "conversation_id": conversation_id,
            "user_id": user_id,
            "timestamp": canvas_data.get("timestamp", int(asyncio.get_event_loop().time())),
            "data": {
                "artifacts_count": len(canvas_data.get("artifacts", [])),
                "view_state": canvas_data.get("view_state", {}),
                "updated_at": canvas_data.get("updated_at")
            }
        }

        # 发送给同一对话中的其他用户（排除发送者）
        await self.manager.send_to_conversation_excluding_user(conversation_id, user_id, message)
        logger.info(f"Canvas state update sent to conversation {conversation_id}, excluding user {user_id}")

    async def send_artifact_added(self, conversation_id: str, user_id: str, artifact_data: dict):
        """发送新制品添加通知"""
        message = {
            "type": "artifact_added",
            "conversation_id": conversation_id,
            "user_id": user_id,
            "timestamp": artifact_data.get("timestamp", int(asyncio.get_event_loop().time())),
            "data": {
                "artifact_id": artifact_data.get("artifact_id"),
                "position_x": artifact_data.get("position_x", 0),
                "position_y": artifact_data.get("position_y", 0),
                "scale": artifact_data.get("scale", 1.0),
                "image_url": artifact_data.get("image_url"),
                "metadata": artifact_data.get("metadata", {})
            }
        }

        # 发送给同一对话中的其他用户（排除发送者）
        await self.manager.send_to_conversation_excluding_user(conversation_id, user_id, message)
        logger.info(f"Artifact added notification sent to conversation {conversation_id}, excluding user {user_id}")

    async def send_artifact_updated(self, conversation_id: str, user_id: str, artifact_data: dict):
        """发送制品更新通知"""
        message = {
            "type": "artifact_updated",
            "conversation_id": conversation_id,
            "user_id": user_id,
            "timestamp": artifact_data.get("timestamp", int(asyncio.get_event_loop().time())),
            "data": {
                "artifact_id": artifact_data.get("artifact_id"),
                "position_x": artifact_data.get("position_x", 0),
                "position_y": artifact_data.get("position_y", 0),
                "scale": artifact_data.get("scale", 1.0),
                "rotation": artifact_data.get("rotation", 0),
                "is_selected": artifact_data.get("is_selected", False)
            }
        }

        # 发送给同一对话中的其他用户（排除发送者）
        await self.manager.send_to_conversation_excluding_user(conversation_id, user_id, message)
        logger.info(f"Artifact updated notification sent to conversation {conversation_id}, excluding user {user_id}")

        await self.manager.send_to_conversation(conversation_id, message)
        logger.debug(f"Task progress sent to conversation {conversation_id}: {progress_data.get('status', 'Unknown')}")

    async def send_collaboration_status(self, conversation_id: str, status_data: dict):
        """发送多Agent协作状态"""
        message = {
            "type": "collaboration_status",
            "conversation_id": conversation_id,
            "timestamp": status_data.get("timestamp"),
            "data": status_data
        }

        await self.manager.send_to_conversation(conversation_id, message)
        logger.debug(f"Collaboration status sent to conversation {conversation_id}: {status_data.get('phase', 'Unknown')}")

    async def send_conversation_result(self, conversation_id: str, result_data: dict):
        """发送对话结果（AI回复消息）"""
        message = {
            "type": "conversation_result",
            "conversation_id": conversation_id,
            "timestamp": result_data.get("timestamp"),
            "data": result_data
        }

        await self.manager.send_to_conversation(conversation_id, message)
        logger.info(f"Conversation result sent to conversation {conversation_id}: {len(result_data.get('messages', []))} messages")


class AutonomousStatusBroadcaster:
    """自主工具调用状态广播器"""
    
    def __init__(self, manager: WebSocketManager):
        self.manager = manager
    
    async def send_status_update(self, conversation_id: str, status: dict):
        """发送状态更新"""
        message = {
            "type": "autonomous_generation_status",
            "conversation_id": conversation_id,
            "timestamp": status.get("timestamp"),
            "data": status
        }
        
        await self.manager.send_to_conversation(conversation_id, message)
        logger.debug(f"Status update sent to conversation {conversation_id}: {status.get('action', 'Unknown')}")
    
    async def send_tool_call_update(self, conversation_id: str, tool_name: str, status: str, result: dict = None):
        """发送工具调用更新"""
        message = {
            "type": "tool_call_update",
            "conversation_id": conversation_id,
            "data": {
                "tool_name": tool_name,
                "status": status,  # "started", "completed", "failed"
                "result": result
            }
        }
        
        await self.manager.send_to_conversation(conversation_id, message)
    
    async def send_generation_complete(self, conversation_id: str, final_result: dict):
        """发送生成完成通知"""
        message = {
            "type": "generation_complete",
            "conversation_id": conversation_id,
            "data": final_result
        }
        
        await self.manager.send_to_conversation(conversation_id, message)


# 全局广播器实例
design_broadcaster = None
status_broadcaster = None

def get_design_broadcaster():
    """获取设计广播器实例"""
    global design_broadcaster
    if design_broadcaster is None:
        design_broadcaster = DesignRealtimeBroadcaster(websocket_manager)
    return design_broadcaster

def get_status_broadcaster():
    """获取状态广播器实例"""
    global status_broadcaster
    if status_broadcaster is None:
        status_broadcaster = AutonomousStatusBroadcaster(websocket_manager)
    return status_broadcaster
