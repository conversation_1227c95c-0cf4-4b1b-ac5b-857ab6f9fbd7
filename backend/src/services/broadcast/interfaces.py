#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广播接口定义
符合SOLID原则的设计广播系统
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class BroadcastData:
    """广播数据标准格式"""
    image_url: str
    design_prompt: str
    agent_type: str
    task_id: str
    design_id: str
    timestamp: str
    metadata: Dict[str, Any]


class ArtifactExtractor(ABC):
    """Artifact提取器抽象接口"""
    
    @abstractmethod
    def can_extract(self, artifact: Dict[str, Any]) -> bool:
        """判断是否能处理该artifact"""
        pass
    
    @abstractmethod
    def extract_broadcast_data(
        self, 
        artifact: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        """从artifact中提取广播数据"""
        pass


class ResultExtractor(ABC):
    """结果提取器抽象接口"""
    
    @abstractmethod
    def can_extract(self, result: Dict[str, Any]) -> bool:
        """判断是否能处理该结果"""
        pass
    
    @abstractmethod
    def extract_broadcast_data(
        self, 
        result: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        """从结果中提取广播数据"""
        pass


class DesignBroadcaster(ABC):
    """设计广播器抽象接口"""
    
    @abstractmethod
    async def broadcast(self, conversation_id: str, data: BroadcastData) -> None:
        """广播设计数据"""
        pass
