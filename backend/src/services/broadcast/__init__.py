#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计广播系统
符合SOLID原则的可扩展广播架构
"""

from .interfaces import ArtifactExtractor, ResultExtractor, DesignBroadcaster, BroadcastData
from .extractors import (
    DesignImageArtifactExtractor,
    LogoArtifactExtractor,
    PosterArtifactExtractor,
    FashionArtifactExtractor,
    DramaArtifactExtractor,
    DirectResultExtractor
)
from .service import DesignBroadcastService, WebSocketDesignBroadcaster, get_broadcast_service

__all__ = [
    # 接口
    "ArtifactExtractor",
    "ResultExtractor", 
    "DesignBroadcaster",
    "BroadcastData",
    
    # 提取器
    "DesignImageArtifactExtractor",
    "LogoArtifactExtractor",
    "PosterArtifactExtractor",
    "FashionArtifactExtractor",
    "DramaArtifactExtractor",
    "DirectResultExtractor",
    
    # 服务
    "DesignBroadcastService",
    "WebSocketDesignBroadcaster",
    "get_broadcast_service",
]
