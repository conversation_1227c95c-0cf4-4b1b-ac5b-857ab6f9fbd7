#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广播数据提取器实现
每个提取器负责处理特定类型的数据
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

from .interfaces import ArtifactExtractor, ResultExtractor, BroadcastData

logger = logging.getLogger(__name__)


class DesignImageArtifactExtractor(ArtifactExtractor):
    """设计图片Artifact提取器"""
    
    def can_extract(self, artifact: Dict[str, Any]) -> bool:
        return (
            artifact.get("type") == "design_image" and 
            (artifact.get("url") or artifact.get("image_url"))
        )
    
    def extract_broadcast_data(
        self, 
        artifact: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        image_url = artifact.get("url") or artifact.get("image_url")
        if not image_url:
            return None
            
        return BroadcastData(
            image_url=image_url,
            design_prompt=artifact.get("metadata", {}).get("prompt", ""),
            agent_type=task.get("agent_type", "unknown"),
            task_id=task.get("id", "unknown"),
            design_id=artifact.get("id", "unknown"),
            timestamp=artifact.get("created_at", datetime.now().isoformat()),
            metadata=artifact.get("metadata", {})
        )


class LogoArtifactExtractor(ArtifactExtractor):
    """Logo Artifact提取器"""
    
    def can_extract(self, artifact: Dict[str, Any]) -> bool:
        return (
            artifact.get("type") == "logo" and 
            (artifact.get("url") or artifact.get("image_url"))
        )
    
    def extract_broadcast_data(
        self, 
        artifact: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        image_url = artifact.get("url") or artifact.get("image_url")
        if not image_url:
            return None
            
        return BroadcastData(
            image_url=image_url,
            design_prompt=artifact.get("description", ""),
            agent_type=task.get("agent_type", "LogoDesignAgent"),
            task_id=task.get("id", "unknown"),
            design_id=artifact.get("id", "unknown"),
            timestamp=artifact.get("created_at", datetime.now().isoformat()),
            metadata=artifact.get("metadata", {})
        )


class PosterArtifactExtractor(ArtifactExtractor):
    """海报Artifact提取器"""
    
    def can_extract(self, artifact: Dict[str, Any]) -> bool:
        return (
            artifact.get("type") == "poster" and 
            (artifact.get("url") or artifact.get("image_url"))
        )
    
    def extract_broadcast_data(
        self, 
        artifact: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        image_url = artifact.get("url") or artifact.get("image_url")
        if not image_url:
            return None
            
        return BroadcastData(
            image_url=image_url,
            design_prompt=artifact.get("description", ""),
            agent_type=task.get("agent_type", "PosterDesignAgent"),
            task_id=task.get("id", "unknown"),
            design_id=artifact.get("id", "unknown"),
            timestamp=artifact.get("created_at", datetime.now().isoformat()),
            metadata=artifact.get("metadata", {})
        )


class FashionArtifactExtractor(ArtifactExtractor):
    """时装Artifact提取器"""
    
    def can_extract(self, artifact: Dict[str, Any]) -> bool:
        return (
            artifact.get("type") == "fashion" and 
            (artifact.get("url") or artifact.get("image_url"))
        )
    
    def extract_broadcast_data(
        self, 
        artifact: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        image_url = artifact.get("url") or artifact.get("image_url")
        if not image_url:
            return None
            
        return BroadcastData(
            image_url=image_url,
            design_prompt=artifact.get("description", ""),
            agent_type=task.get("agent_type", "FashionDesignAgent"),
            task_id=task.get("id", "unknown"),
            design_id=artifact.get("id", "unknown"),
            timestamp=artifact.get("created_at", datetime.now().isoformat()),
            metadata=artifact.get("metadata", {})
        )


class DramaArtifactExtractor(ArtifactExtractor):
    """短剧Artifact提取器 - 处理文本内容的短剧artifacts"""

    def can_extract(self, artifact: Dict[str, Any]) -> bool:
        return (
            artifact.get("type") in ["drama_production", "drama", "script"] and
            artifact.get("content")
        )

    def extract_broadcast_data(
        self,
        artifact: Dict[str, Any],
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        content = artifact.get("content")
        if not content:
            return None

        # For drama artifacts, we create a special broadcast data with content instead of image
        return BroadcastData(
            image_url=None,  # No image for text-based artifacts
            design_prompt=artifact.get("description", "短剧制作"),
            agent_type=task.get("agent_type", "drama"),
            task_id=task.get("id", "unknown"),
            design_id=artifact.get("id", "unknown"),
            timestamp=artifact.get("metadata", {}).get("created_at", datetime.now().isoformat()),
            metadata={
                **artifact.get("metadata", {}),
                "content": content,
                "artifact_type": "drama_production",
                "content_type": "text"
            }
        )


class DirectResultExtractor(ResultExtractor):
    """直接结果提取器 - 处理workflow直接返回image_url的情况"""
    
    def can_extract(self, result: Dict[str, Any]) -> bool:
        return bool(result.get("image_url"))
    
    def extract_broadcast_data(
        self, 
        result: Dict[str, Any], 
        task: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        image_url = result.get("image_url")
        if not image_url:
            return None
            
        return BroadcastData(
            image_url=image_url,
            design_prompt=result.get("design_prompt", result.get("optimized_prompt", "")),
            agent_type=task.get("agent_type", "unknown"),
            task_id=task.get("id", "unknown"),
            design_id=f"design_{task.get('id', 'unknown')}_{int(datetime.now().timestamp())}",
            timestamp=datetime.now().isoformat(),
            metadata=result.get("metadata", {})
        )
