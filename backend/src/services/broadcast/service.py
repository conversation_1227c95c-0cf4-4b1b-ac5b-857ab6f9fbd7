#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计广播服务
符合SOLID原则的广播系统核心实现
"""

import logging
from typing import List, Dict, Any, Optional

from .interfaces import ArtifactExtractor, ResultExtractor, DesignBroadcaster, BroadcastData
from .extractors import (
    DesignImageArtifactExtractor,
    LogoArtifactExtractor,
    PosterArtifactExtractor,
    FashionArtifactExtractor,
    DramaArtifactExtractor,
    DirectResultExtractor
)

logger = logging.getLogger(__name__)


class WebSocketDesignBroadcaster(DesignBroadcaster):
    """WebSocket设计广播器实现"""
    
    def __init__(self):
        from ..websocket_manager import get_design_broadcaster
        self._websocket_broadcaster = get_design_broadcaster()
    
    async def broadcast(self, conversation_id: str, data: BroadcastData) -> None:
        """通过WebSocket广播设计数据"""
        try:
            design_data = {
                "image_url": data.image_url,
                "design_prompt": data.design_prompt,
                "agent_type": data.agent_type,
                "task_id": data.task_id,
                "design_id": data.design_id,
                "timestamp": data.timestamp,
                "metadata": data.metadata
            }
            
            await self._websocket_broadcaster.send_design_generated(conversation_id, design_data)
            logger.info(f"Design broadcasted via WebSocket: {data.design_id} -> {conversation_id}")
            
        except Exception as e:
            logger.error(f"Failed to broadcast design via WebSocket: {e}")
            raise


class DesignBroadcastService:
    """设计广播服务 - 主要业务逻辑"""
    
    def __init__(self, broadcaster: DesignBroadcaster):
        self.broadcaster = broadcaster
        
        # 注册所有提取器
        self.artifact_extractors: List[ArtifactExtractor] = [
            DesignImageArtifactExtractor(),
            LogoArtifactExtractor(),
            PosterArtifactExtractor(),
            FashionArtifactExtractor(),
            DramaArtifactExtractor(),
        ]
        
        self.result_extractors: List[ResultExtractor] = [
            DirectResultExtractor(),
        ]
    
    def register_artifact_extractor(self, extractor: ArtifactExtractor) -> None:
        """注册新的artifact提取器"""
        self.artifact_extractors.append(extractor)
        logger.info(f"Registered artifact extractor: {extractor.__class__.__name__}")
    
    def register_result_extractor(self, extractor: ResultExtractor) -> None:
        """注册新的结果提取器"""
        self.result_extractors.append(extractor)
        logger.info(f"Registered result extractor: {extractor.__class__.__name__}")
    
    async def broadcast_design_result(
        self, 
        task: Dict[str, Any], 
        result: Dict[str, Any], 
        conversation_id: str
    ) -> bool:
        """广播设计结果"""
        try:
            if not conversation_id:
                logger.warning("No conversation_id provided, skipping broadcast")
                return False
            
            # 尝试从artifacts中提取
            broadcast_data = await self._extract_from_artifacts(task, result)
            
            # 如果artifacts中没有找到，尝试从结果中直接提取
            if not broadcast_data:
                broadcast_data = await self._extract_from_result(task, result)
            
            # 如果找到数据，进行广播
            if broadcast_data:
                await self.broadcaster.broadcast(conversation_id, broadcast_data)
                logger.info(f"Successfully broadcasted design: {broadcast_data.design_id}")
                return True
            else:
                logger.debug(f"No broadcastable design data found in task result: {task.get('id', 'unknown')}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to broadcast design result: {e}")
            return False
    
    async def _extract_from_artifacts(
        self, 
        task: Dict[str, Any], 
        result: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        """从artifacts中提取广播数据"""
        artifacts = result.get("artifacts", [])
        if not artifacts:
            return None
        
        for artifact in artifacts:
            for extractor in self.artifact_extractors:
                if extractor.can_extract(artifact):
                    data = extractor.extract_broadcast_data(artifact, task)
                    if data:
                        logger.info(f"Extracted broadcast data from artifact using {extractor.__class__.__name__}")
                        return data
        
        return None
    
    async def _extract_from_result(
        self, 
        task: Dict[str, Any], 
        result: Dict[str, Any]
    ) -> Optional[BroadcastData]:
        """从结果中直接提取广播数据"""
        for extractor in self.result_extractors:
            if extractor.can_extract(result):
                data = extractor.extract_broadcast_data(result, task)
                if data:
                    logger.info(f"Extracted broadcast data from result using {extractor.__class__.__name__}")
                    return data
        
        return None


# 全局服务实例
_broadcast_service: Optional[DesignBroadcastService] = None


def get_broadcast_service() -> DesignBroadcastService:
    """获取广播服务实例（单例模式）"""
    global _broadcast_service
    if _broadcast_service is None:
        broadcaster = WebSocketDesignBroadcaster()
        _broadcast_service = DesignBroadcastService(broadcaster)
        logger.info("Design broadcast service initialized")
    return _broadcast_service
