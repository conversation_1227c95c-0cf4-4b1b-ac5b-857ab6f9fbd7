"""
User service implementation.
Follows Single Responsibility Principle and Dependency Inversion Principle.
"""

import logging
from typing import Optional
from uuid import UUID

from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.database import User as UserModel
from ..models.schemas import UserCreate, UserResponse, UserUpdate
from .interfaces import IUserService

logger = logging.getLogger(__name__)


class UserService(IUserService):
    """User service implementation."""
    
    def __init__(self, db_session: AsyncSession):
        """Initialize user service with database session."""
        self.db = db_session
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create a new user."""
        try:
            # Check if user already exists
            existing_user = await self.get_user_by_email(user_data.email)
            if existing_user:
                raise ValueError("User with this email already exists")
            
            # Hash password
            hashed_password = self.pwd_context.hash(user_data.password)
            
            # Create user model
            db_user = UserModel(
                email=user_data.email,
                password_hash=hashed_password,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
            )
            
            self.db.add(db_user)
            await self.db.commit()
            await self.db.refresh(db_user)
            
            logger.info(f"Created user: {user_data.email}")
            return UserResponse.model_validate(db_user)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating user: {e}")
            raise
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[UserResponse]:
        """Get user by ID."""
        try:
            # Convert UUID to string for SQLite compatibility
            user_id_str = str(user_id)
            stmt = select(UserModel).where(UserModel.id == user_id_str)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if user:
                return UserResponse.model_validate(user)
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            raise
    
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """Get user by email."""
        try:
            stmt = select(UserModel).where(UserModel.email == email)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if user:
                return UserResponse.model_validate(user)
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            raise
    
    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[UserResponse]:
        """Update user."""
        try:
            stmt = select(UserModel).where(UserModel.id == user_id)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                return None
            
            # Update fields
            update_data = user_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(user, field, value)
            
            await self.db.commit()
            await self.db.refresh(user)
            
            logger.info(f"Updated user: {user_id}")
            return UserResponse.model_validate(user)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating user {user_id}: {e}")
            raise
    
    async def delete_user(self, user_id: UUID) -> bool:
        """Delete user."""
        try:
            stmt = select(UserModel).where(UserModel.id == user_id)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                return False
            
            # Soft delete by setting is_active to False
            user.is_active = False
            await self.db.commit()
            
            logger.info(f"Deleted user: {user_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting user {user_id}: {e}")
            raise
    
    async def get_user_model_by_email(self, email: str) -> Optional[UserModel]:
        """Get user model by email (for authentication)."""
        try:
            stmt = select(UserModel).where(UserModel.email == email)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user model by email {email}: {e}")
            raise
