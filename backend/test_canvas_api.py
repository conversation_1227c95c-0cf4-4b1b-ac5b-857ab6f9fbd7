#!/usr/bin/env python3
"""
Test script for canvas API to debug the 500 error.
"""

import requests
import json

# Test data that matches the frontend format
test_canvas_data = {
    "artifacts": [
        {
            "artifact_id": "test-artifact-1",
            "position_x": 100,
            "position_y": 200,
            "scale": 1.0,
            "rotation": 0.0,
            "z_index": 0,
            "is_selected": False,
            "is_visible": True,
            "metadata": {
                "image_url": "https://example.com/image.jpg",
                "prompt": "test prompt",
                "timestamp": "2024-01-01T00:00:00.000Z"
            }
        }
    ],
    "view_state": {
        "scale": 1,
        "offset_x": 0,
        "offset_y": 0
    },
    "canvas_metadata": {
        "version": "1.0",
        "total_artifacts": 1,
        "saved_at": "2024-01-01T00:00:00.000Z"
    }
}

def test_canvas_api():
    """Test the canvas API with sample data."""
    
    # API endpoint
    conversation_id = "f71397af-d0df-407e-85b8-899aee4e4e81"
    url = f"http://localhost:8000/api/v1/canvas/state/{conversation_id}"
    
    # Headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"  # You might need a real token
    }
    
    print("Testing Canvas API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_canvas_data, indent=2)}")
    
    try:
        # Make the POST request
        response = requests.post(url, json=test_canvas_data, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Success!")
            print(f"Response: {response.json()}")
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_canvas_api()
