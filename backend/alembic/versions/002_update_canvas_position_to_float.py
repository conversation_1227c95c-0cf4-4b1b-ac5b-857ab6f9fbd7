"""Update canvas position fields to float

Revision ID: 002_update_canvas_position_to_float
Revises: 001_initial_migration
Create Date: 2025-08-11 15:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_update_canvas_position_to_float'
down_revision = '001_initial_migration'
branch_labels = None
depends_on = None


def upgrade():
    """Upgrade database schema."""
    # Update position_x and position_y columns to Float type for precise position storage
    op.alter_column('canvas_artifacts', 'position_x',
                    existing_type=sa.Integer(),
                    type_=sa.Float(),
                    existing_nullable=False)
    
    op.alter_column('canvas_artifacts', 'position_y',
                    existing_type=sa.Integer(),
                    type_=sa.Float(),
                    existing_nullable=False)


def downgrade():
    """Downgrade database schema."""
    # Revert position_x and position_y columns back to Integer type
    op.alter_column('canvas_artifacts', 'position_x',
                    existing_type=sa.Float(),
                    type_=sa.Integer(),
                    existing_nullable=False)
    
    op.alter_column('canvas_artifacts', 'position_y',
                    existing_type=sa.Float(),
                    type_=sa.Integer(),
                    existing_nullable=False)
