{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(npm run dev:*)", "Bash(find:*)", "Bash(npm install:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(npm run lint:*)", "Bash(ls:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true:*)", "Bash(npm run typecheck:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(open:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "Bash(-H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1MTI3NzdlOS1jNDE4LTRlOGQtOWQyZi0yYjhkNmIwN2Q4NjYiLCJleHAiOjE3NTQyOTE0NjgsInR5cGUiOiJhY2Nlc3MifQ.dv2_bXHl5TXDzb4AaJXFmB2mij98lJeXtc-6Wkeq9KE\")", "Bash(npm run)", "Bash(pip install:*)", "Bash(npm run type-check:*)", "Bash(node:*)"], "deny": []}}