designer页面逻辑：
1、可以直接通过url访问designer页面，参数为conversation_id和initialMessage，都可以通过url传递。也可以不传。
如果传了conversation_id，则说明是要对设计内容进行修改，需要获取该conversation_id对应的conversation信息，并初始化designer页面。
如果没有传conversation_id，则说明是新增设计内容，这个时候就要看是否有initialMessage参数，如果有，则自动触发发送消息的逻辑，消息内容为initialMessage。如果没有，则需要用户手动输入内容，并点击发送按钮。
2、发送消息的逻辑需要优化一下，必须要传conversation_id，如果是新增设计内容，则需要后端服务提供一个生成会话id的接口，返回会话id。然后再发送消息，发送消息后端服务不需要创建conversation_id。
